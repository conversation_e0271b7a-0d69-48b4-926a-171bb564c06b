"use strict";(()=>{var e={};e.id=7028,e.ids=[7028],e.modules={775:(e,r,t)=>{t.r(r),t.d(r,{patchFetch:()=>h,routeModule:()=>c,serverHooks:()=>m,workAsyncStorage:()=>p,workUnitAsyncStorage:()=>f});var a={};t.r(a),t.d(a,{GET:()=>d});var s=t(96559),i=t(48088),n=t(37719),o=t(32190),l=t(12909),u=t(2746);async function d(e){try{let{authenticated:r,user:t}=await (0,l.b9)(e);if(!r||!t)return o.NextResponse.json({success:!1,error:"Not authenticated"},{status:401});let{searchParams:a}=new URL(e.url),s=a.get("type")||"all",i=a.get("generation"),n={};switch(s){case"team":n.teamStats=await (0,u.fB)(t.id);break;case"health":n.treeHealth=await (0,u.g5)(t.id);break;case"generation":if(!i)return o.NextResponse.json({success:!1,error:"Generation parameter is required"},{status:400});{let e=parseInt(i);if(!(e>0)||!(e<=10))return o.NextResponse.json({success:!1,error:"Generation must be between 1 and 10"},{status:400});n.generationUsers=await (0,u.oS)(t.id,e)}break;case"counts":n.teamCounts=await (0,u.l6)(t.id);break;default:n.teamStats=await (0,u.fB)(t.id),n.treeHealth=await (0,u.g5)(t.id),n.teamCounts=await (0,u.l6)(t.id)}return o.NextResponse.json({success:!0,data:n})}catch(e){return console.error("Referral stats fetch error:",e),o.NextResponse.json({success:!1,error:"Failed to fetch referral statistics"},{status:500})}}let c=new s.AppRouteRouteModule({definition:{kind:i.RouteKind.APP_ROUTE,page:"/api/referrals/stats/route",pathname:"/api/referrals/stats",filename:"route",bundlePath:"app/api/referrals/stats/route"},resolvedPagePath:"C:\\Users\\<USER>\\Desktop\\Hash_Minings\\hashcorex\\src\\app\\api\\referrals\\stats\\route.ts",nextConfigOutput:"",userland:a}),{workAsyncStorage:p,workUnitAsyncStorage:f,serverHooks:m}=c;function h(){return(0,n.patchFetch)({workAsyncStorage:p,workUnitAsyncStorage:f})}},3295:e=>{e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},12909:(e,r,t)=>{t.d(r,{DT:()=>y,DY:()=>h,HU:()=>c,Lx:()=>w,b9:()=>m,qc:()=>x});var a=t(85663),s=t(43205),i=t.n(s),n=t(6710);let o=process.env.JWT_SECRET||"fallback-secret-key",l=process.env.JWT_EXPIRES_IN||"30d",u=async e=>await a.Ay.hash(e,12),d=async(e,r)=>await a.Ay.compare(e,r),c=e=>i().sign(e,o,{expiresIn:l}),p=e=>{try{return i().verify(e,o)}catch(e){return null}},f=()=>{let e="ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789",r="HC";for(let t=0;t<8;t++)r+=e.charAt(Math.floor(Math.random()*e.length));return r},m=async e=>{let r=e.headers.get("authorization")?.replace("Bearer ","")||e.cookies.get("auth-token")?.value;if(!r)return{authenticated:!1,user:null};let t=p(r);if(!t)return{authenticated:!1,user:null};let a=await n.Gy.findByEmail(t.email);return a?{authenticated:!0,user:a}:{authenticated:!1,user:null}},h=async e=>{let r,a;if(await n.Gy.findByEmail(e.email))throw Error("User already exists with this email");if(e.referralCode){let t=await n.Gy.findByReferralId(e.referralCode);if(!t)throw Error("Invalid referral code");r=t.id}let s=await u(e.password),i=!1;do a=f(),i=!await n.Gy.findByReferralId(a);while(!i);let o=await n.Gy.create({email:e.email,firstName:e.firstName,lastName:e.lastName,password:s,referralId:a});if(r){let{placeUserByReferralType:a}=await t.e(2746).then(t.bind(t,2746)),s="general";"left"===e.placementSide?s="left":"right"===e.placementSide&&(s="right"),await a(r,o.id,s)}return{id:o.id,email:o.email,referralId:o.referralId,kycStatus:o.kycStatus}},w=async e=>{let r=await n.Gy.findByEmail(e.email);if(!r||!await d(e.password,r.password))throw Error("Invalid email or password");return{token:c({userId:r.id,email:r.email}),user:{id:r.id,email:r.email,referralId:r.referralId,kycStatus:r.kycStatus}}},y=e=>/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(e),x=async e=>{let r=await n.Gy.findById(e);return r?.role==="ADMIN"}},27910:e=>{e.exports=require("stream")},28354:e=>{e.exports=require("util")},29294:e=>{e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},44870:e=>{e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},55511:e=>{e.exports=require("crypto")},63033:e=>{e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},79428:e=>{e.exports=require("buffer")},96330:e=>{e.exports=require("@prisma/client")}};var r=require("../../../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),a=r.X(0,[4447,580,5315,3306,2746],()=>t(775));module.exports=a})();