{"/_not-found/page": "/_not-found", "/api/admin/binary-matching/manual/route": "/api/admin/binary-matching/manual", "/api/admin/binary-points/history/route": "/api/admin/binary-points/history", "/api/admin/binary-points/reset-all/route": "/api/admin/binary-points/reset-all", "/api/admin/binary-points/route": "/api/admin/binary-points", "/api/admin/binary-points/test-limit/route": "/api/admin/binary-points/test-limit", "/api/admin/binary-points/update-limit/route": "/api/admin/binary-points/update-limit", "/api/admin/binary-points/user-history/[userId]/route": "/api/admin/binary-points/user-history/[userId]", "/api/admin/check/route": "/api/admin/check", "/api/admin/deposit-settings/route": "/api/admin/deposit-settings", "/api/admin/deposits/route": "/api/admin/deposits", "/api/admin/email-settings/route": "/api/admin/email-settings", "/api/admin/email-templates/route": "/api/admin/email-templates", "/api/admin/email-templates/[name]/route": "/api/admin/email-templates/[name]", "/api/admin/email-templates/seed/route": "/api/admin/email-templates/seed", "/api/admin/kyc/all/route": "/api/admin/kyc/all", "/api/admin/kyc/bulk/route": "/api/admin/kyc/bulk", "/api/admin/kyc/pending/route": "/api/admin/kyc/pending", "/api/admin/kyc/review/route": "/api/admin/kyc/review", "/api/admin/kyc/search/route": "/api/admin/kyc/search", "/api/admin/logs/export/route": "/api/admin/logs/export", "/api/admin/logs/route": "/api/admin/logs", "/api/admin/referral-commissions/route": "/api/admin/referral-commissions", "/api/admin/referral-commissions/stats/route": "/api/admin/referral-commissions/stats", "/api/admin/refresh-tree-cache/route": "/api/admin/refresh-tree-cache", "/api/admin/scheduler/status/route": "/api/admin/scheduler/status", "/api/admin/settings/pricing/route": "/api/admin/settings/pricing", "/api/admin/settings/route": "/api/admin/settings", "/api/admin/scheduler/trigger/route": "/api/admin/scheduler/trigger", "/api/admin/stats/route": "/api/admin/stats", "/api/admin/support/tickets/[ticketId]/responses/route": "/api/admin/support/tickets/[ticketId]/responses", "/api/admin/support/tickets/[ticketId]/route": "/api/admin/support/tickets/[ticketId]", "/api/admin/support/tickets/route": "/api/admin/support/tickets", "/api/admin/update-mining-units-roi/route": "/api/admin/update-mining-units-roi", "/api/admin/users/[userId]/details/route": "/api/admin/users/[userId]/details", "/api/admin/users/action/route": "/api/admin/users/action", "/api/admin/users/route": "/api/admin/users", "/api/admin/wallet/adjust/route": "/api/admin/wallet/adjust", "/api/admin/withdrawals/action/route": "/api/admin/withdrawals/action", "/api/auth/login/route": "/api/auth/login", "/api/admin/withdrawals/route": "/api/admin/withdrawals", "/api/auth/logout/route": "/api/auth/logout", "/api/auth/me/route": "/api/auth/me", "/api/auth/register/route": "/api/auth/register", "/api/auth/reset-password/route": "/api/auth/reset-password", "/api/auth/send-otp/route": "/api/auth/send-otp", "/api/auth/verify-otp/route": "/api/auth/verify-otp", "/api/cron/binary-matching/route": "/api/cron/binary-matching", "/api/binary-points/info/route": "/api/binary-points/info", "/api/cron/daily-roi/route": "/api/cron/daily-roi", "/api/cron/weekly-payout/route": "/api/cron/weekly-payout", "/api/cron/process-deposits/route": "/api/cron/process-deposits", "/api/crypto/prices/route": "/api/crypto/prices", "/api/earnings/route": "/api/earnings", "/api/errors/log/route": "/api/errors/log", "/api/init/route": "/api/init", "/api/kyc/documents/route": "/api/kyc/documents", "/api/kyc/submit/route": "/api/kyc/submit", "/api/kyc/status/route": "/api/kyc/status", "/api/kyc/upload/route": "/api/kyc/upload", "/api/mining-units/[id]/earnings/route": "/api/mining-units/[id]/earnings", "/api/mining-units/route": "/api/mining-units", "/api/referrals/search/route": "/api/referrals/search", "/api/referrals/stats/route": "/api/referrals/stats", "/api/referrals/tree/route": "/api/referrals/tree", "/api/support/tickets/[ticketId]/responses/route": "/api/support/tickets/[ticketId]/responses", "/api/support/tickets/route": "/api/support/tickets", "/api/test/binary-settings/route": "/api/test/binary-settings", "/api/user/notification-settings/route": "/api/user/notification-settings", "/api/user/profile-picture/route": "/api/user/profile-picture", "/api/user/withdrawal-address/route": "/api/user/withdrawal-address", "/api/wallet/balance/route": "/api/wallet/balance", "/api/wallet/deposit-address/route": "/api/wallet/deposit-address", "/api/wallet/deposit/info/route": "/api/wallet/deposit/info", "/api/wallet/deposit/verify/route": "/api/wallet/deposit/verify", "/api/wallet/transactions/[id]/route": "/api/wallet/transactions/[id]", "/api/wallet/transactions/route": "/api/wallet/transactions", "/api/wallet/withdraw/route": "/api/wallet/withdraw", "/api/wallet/withdrawal-settings/route": "/api/wallet/withdrawal-settings", "/favicon.ico/route": "/favicon.ico", "/(auth)/forgot-password/page": "/forgot-password", "/(public)/about/page": "/about", "/(auth)/login/page": "/login", "/(auth)/register/page": "/register", "/(public)/contact/page": "/contact", "/(public)/how-it-works/page": "/how-it-works", "/(dashboard)/dashboard/page": "/dashboard", "/(public)/privacy/page": "/privacy", "/admin/page": "/admin", "/(public)/terms/page": "/terms", "/page": "/"}