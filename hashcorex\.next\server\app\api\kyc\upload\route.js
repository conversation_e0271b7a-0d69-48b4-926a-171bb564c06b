"use strict";(()=>{var e={};e.id=8483,e.ids=[8483],e.modules={3295:e=>{e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},3829:(e,t,r)=>{r.r(t),r.d(t,{patchFetch:()=>x,routeModule:()=>y,serverHooks:()=>I,workAsyncStorage:()=>h,workUnitAsyncStorage:()=>w});var a={};r.r(a),r.d(a,{POST:()=>f});var s=r(96559),i=r(48088),n=r(37719),o=r(32190),u=r(12909),d=r(6710),l=r(31183),c=r(79748),p=r(33873),m=r(23870);async function f(e){try{let t,{authenticated:r,user:a}=await (0,u.b9)(e);if(!r||!a)return o.NextResponse.json({success:!1,error:"Not authenticated"},{status:401});let s=await e.formData(),i=s.get("file"),n=s.get("documentType"),f=s.get("idType"),y=s.get("documentSide");if(!i)return o.NextResponse.json({success:!1,error:"No file provided"},{status:400});if(!n||!["ID_DOCUMENT","SELFIE"].includes(n))return o.NextResponse.json({success:!1,error:"Invalid document type"},{status:400});if("ID_DOCUMENT"===n){if(!f||!["NATIONAL_ID","PASSPORT","DRIVING_LICENSE"].includes(f))return o.NextResponse.json({success:!1,error:"Invalid ID type"},{status:400});if(!y||!["FRONT","BACK"].includes(y))return o.NextResponse.json({success:!1,error:"Invalid document side"},{status:400})}if(!i.type.startsWith("image/"))return o.NextResponse.json({success:!1,error:"Only image files are allowed"},{status:400});if(i.size>5242880)return o.NextResponse.json({success:!1,error:"File size must be less than 5MB"},{status:400});let h=(0,p.join)(process.cwd(),"public","uploads","kyc");try{await (0,c.mkdir)(h,{recursive:!0})}catch(e){}let w=i.name.split(".").pop(),I=[a.id,n];f&&I.push(f),y&&I.push(y),I.push((0,m.A)());let x=`${I.join("_")}.${w}`,N=(0,p.join)(h,x),g=`/uploads/kyc/${x}`,D=await i.arrayBuffer(),T=Buffer.from(D);await (0,c.writeFile)(N,T);let k={userId:a.id,documentType:n};"ID_DOCUMENT"===n&&(k.idType=f,k.documentSide=y);let E=await l.prisma.kYCDocument.findFirst({where:k});if(E)t=await l.prisma.kYCDocument.update({where:{id:E.id},data:{filePath:g,status:"PENDING",reviewedAt:null,reviewedBy:null,rejectionReason:null}});else{let e={userId:a.id,documentType:n,filePath:g,status:"PENDING"};"ID_DOCUMENT"===n&&(e.idType=f,e.documentSide=y),t=await l.prisma.kYCDocument.create({data:e})}let S=await l.prisma.kYCDocument.findMany({where:{userId:a.id}}),v=S.some(e=>"SELFIE"===e.documentType),R=!1,C=S.find(e=>"ID_DOCUMENT"===e.documentType)?.idType;if(C){let e=S.filter(e=>"ID_DOCUMENT"===e.documentType&&e.idType===C);if("PASSPORT"===C)R=e.some(e=>"FRONT"===e.documentSide);else{let t=e.some(e=>"FRONT"===e.documentSide),r=e.some(e=>"BACK"===e.documentSide);R=t&&r}}return R&&v&&await l.prisma.user.update({where:{id:a.id},data:{kycStatus:"PENDING"}}),await d.AJ.create({action:"KYC_DOCUMENT_UPLOADED",userId:a.id,details:{documentType:n,idType:f||null,documentSide:y||null,fileName:x,fileSize:i.size,documentId:t.id},ipAddress:e.headers.get("x-forwarded-for")||"unknown",userAgent:e.headers.get("user-agent")||"unknown"}),o.NextResponse.json({success:!0,message:"Document uploaded successfully",data:{documentId:t.id,documentType:t.documentType,status:t.status}})}catch(e){return console.error("KYC document upload error:",e),o.NextResponse.json({success:!1,error:"Failed to upload document"},{status:500})}}let y=new s.AppRouteRouteModule({definition:{kind:i.RouteKind.APP_ROUTE,page:"/api/kyc/upload/route",pathname:"/api/kyc/upload",filename:"route",bundlePath:"app/api/kyc/upload/route"},resolvedPagePath:"C:\\Users\\<USER>\\Desktop\\Hash_Minings\\hashcorex\\src\\app\\api\\kyc\\upload\\route.ts",nextConfigOutput:"",userland:a}),{workAsyncStorage:h,workUnitAsyncStorage:w,serverHooks:I}=y;function x(){return(0,n.patchFetch)({workAsyncStorage:h,workUnitAsyncStorage:w})}},10846:e=>{e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},12909:(e,t,r)=>{r.d(t,{DT:()=>w,DY:()=>y,HU:()=>c,Lx:()=>h,b9:()=>f,qc:()=>I});var a=r(85663),s=r(43205),i=r.n(s),n=r(6710);let o=process.env.JWT_SECRET||"fallback-secret-key",u=process.env.JWT_EXPIRES_IN||"30d",d=async e=>await a.Ay.hash(e,12),l=async(e,t)=>await a.Ay.compare(e,t),c=e=>i().sign(e,o,{expiresIn:u}),p=e=>{try{return i().verify(e,o)}catch(e){return null}},m=()=>{let e="ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789",t="HC";for(let r=0;r<8;r++)t+=e.charAt(Math.floor(Math.random()*e.length));return t},f=async e=>{let t=e.headers.get("authorization")?.replace("Bearer ","")||e.cookies.get("auth-token")?.value;if(!t)return{authenticated:!1,user:null};let r=p(t);if(!r)return{authenticated:!1,user:null};let a=await n.Gy.findByEmail(r.email);return a?{authenticated:!0,user:a}:{authenticated:!1,user:null}},y=async e=>{let t,a;if(await n.Gy.findByEmail(e.email))throw Error("User already exists with this email");if(e.referralCode){let r=await n.Gy.findByReferralId(e.referralCode);if(!r)throw Error("Invalid referral code");t=r.id}let s=await d(e.password),i=!1;do a=m(),i=!await n.Gy.findByReferralId(a);while(!i);let o=await n.Gy.create({email:e.email,firstName:e.firstName,lastName:e.lastName,password:s,referralId:a});if(t){let{placeUserByReferralType:a}=await r.e(2746).then(r.bind(r,2746)),s="general";"left"===e.placementSide?s="left":"right"===e.placementSide&&(s="right"),await a(t,o.id,s)}return{id:o.id,email:o.email,referralId:o.referralId,kycStatus:o.kycStatus}},h=async e=>{let t=await n.Gy.findByEmail(e.email);if(!t||!await l(e.password,t.password))throw Error("Invalid email or password");return{token:c({userId:t.id,email:t.email}),user:{id:t.id,email:t.email,referralId:t.referralId,kycStatus:t.kycStatus}}},w=e=>/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(e),I=async e=>{let t=await n.Gy.findById(e);return t?.role==="ADMIN"}},23870:(e,t,r)=>{r.d(t,{A:()=>u});var a=r(55511);let s={randomUUID:a.randomUUID},i=new Uint8Array(256),n=i.length,o=[];for(let e=0;e<256;++e)o.push((e+256).toString(16).slice(1));let u=function(e,t,r){if(s.randomUUID&&!t&&!e)return s.randomUUID();let u=(e=e||{}).random??e.rng?.()??(n>i.length-16&&((0,a.randomFillSync)(i),n=0),i.slice(n,n+=16));if(u.length<16)throw Error("Random bytes length must be >= 16");if(u[6]=15&u[6]|64,u[8]=63&u[8]|128,t){if((r=r||0)<0||r+16>t.length)throw RangeError(`UUID byte range ${r}:${r+15} is out of buffer bounds`);for(let e=0;e<16;++e)t[r+e]=u[e];return t}return function(e,t=0){return(o[e[t+0]]+o[e[t+1]]+o[e[t+2]]+o[e[t+3]]+"-"+o[e[t+4]]+o[e[t+5]]+"-"+o[e[t+6]]+o[e[t+7]]+"-"+o[e[t+8]]+o[e[t+9]]+"-"+o[e[t+10]]+o[e[t+11]]+o[e[t+12]]+o[e[t+13]]+o[e[t+14]]+o[e[t+15]]).toLowerCase()}(u)}},27910:e=>{e.exports=require("stream")},28354:e=>{e.exports=require("util")},29294:e=>{e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:e=>{e.exports=require("path")},44870:e=>{e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},55511:e=>{e.exports=require("crypto")},63033:e=>{e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},79428:e=>{e.exports=require("buffer")},79748:e=>{e.exports=require("fs/promises")},96330:e=>{e.exports=require("@prisma/client")}};var t=require("../../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),a=t.X(0,[4447,580,5315,3306],()=>r(3829));module.exports=a})();