"use strict";(()=>{var e={};e.id=7752,e.ids=[7752],e.modules={3295:e=>{e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},12909:(e,r,t)=>{t.d(r,{DT:()=>E,DY:()=>h,HU:()=>d,Lx:()=>f,b9:()=>g,qc:()=>w});var s=t(85663),a=t(43205),i=t.n(a),o=t(6710);let n=process.env.JWT_SECRET||"fallback-secret-key",l=process.env.JWT_EXPIRES_IN||"30d",c=async e=>await s.Ay.hash(e,12),u=async(e,r)=>await s.Ay.compare(e,r),d=e=>i().sign(e,n,{expiresIn:l}),p=e=>{try{return i().verify(e,n)}catch(e){return null}},m=()=>{let e="ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789",r="HC";for(let t=0;t<8;t++)r+=e.charAt(Math.floor(Math.random()*e.length));return r},g=async e=>{let r=e.headers.get("authorization")?.replace("Bearer ","")||e.cookies.get("auth-token")?.value;if(!r)return{authenticated:!1,user:null};let t=p(r);if(!t)return{authenticated:!1,user:null};let s=await o.Gy.findByEmail(t.email);return s?{authenticated:!0,user:s}:{authenticated:!1,user:null}},h=async e=>{let r,s;if(await o.Gy.findByEmail(e.email))throw Error("User already exists with this email");if(e.referralCode){let t=await o.Gy.findByReferralId(e.referralCode);if(!t)throw Error("Invalid referral code");r=t.id}let a=await c(e.password),i=!1;do s=m(),i=!await o.Gy.findByReferralId(s);while(!i);let n=await o.Gy.create({email:e.email,firstName:e.firstName,lastName:e.lastName,password:a,referralId:s});if(r){let{placeUserByReferralType:s}=await t.e(2746).then(t.bind(t,2746)),a="general";"left"===e.placementSide?a="left":"right"===e.placementSide&&(a="right"),await s(r,n.id,a)}return{id:n.id,email:n.email,referralId:n.referralId,kycStatus:n.kycStatus}},f=async e=>{let r=await o.Gy.findByEmail(e.email);if(!r||!await u(e.password,r.password))throw Error("Invalid email or password");return{token:d({userId:r.id,email:r.email}),user:{id:r.id,email:r.email,referralId:r.referralId,kycStatus:r.kycStatus}}},E=e=>/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(e),w=async e=>{let r=await o.Gy.findById(e);return r?.role==="ADMIN"}},21111:(e,r,t)=>{t.d(r,{XG:()=>n,gm:()=>o});var s=t(49526),a=t(6710);class i{async getEmailConfig(){try{let e=await a.T8.getEmailSettings();if(!e||!e.smtpHost||!e.smtpUser||!e.smtpPassword)return console.warn("Email configuration not found or incomplete"),null;return{host:e.smtpHost,port:e.smtpPort||587,secure:e.smtpSecure||!1,user:e.smtpUser,password:e.smtpPassword,fromName:e.fromName||"HashCoreX",fromEmail:e.fromEmail||e.smtpUser}}catch(e){return console.error("Failed to get email configuration:",e),null}}async initializeTransporter(){try{if(this.config=await this.getEmailConfig(),!this.config)return!1;return this.transporter=s.createTransport({host:this.config.host,port:this.config.port,secure:this.config.secure,auth:{user:this.config.user,pass:this.config.password},tls:{rejectUnauthorized:!1}}),await this.transporter.verify(),console.log("Email transporter initialized successfully"),!0}catch(e){return console.error("Failed to initialize email transporter:",e),this.transporter=null,!1}}async sendEmail(e){try{if((!this.transporter||!this.config)&&!await this.initializeTransporter())throw Error("Email service not configured");let r={from:`"${this.config.fromName}" <${this.config.fromEmail}>`,to:e.to,subject:e.subject,html:e.html,text:e.text},t=await this.transporter.sendMail(r);return console.log("Email sent successfully:",t.messageId),!0}catch(e){return console.error("Failed to send email:",e),!1}}async sendOTPEmail(e,r,t,s="email_verification"){let a="password_reset"===s?"password_reset_otp":"otp_verification",i=await this.getEmailTemplate(a);if(!i)return console.error(`Email template '${a}' not found. Please ensure email templates are seeded.`),!1;let o=i.htmlContent,n=i.textContent||"";return o=(o=o.replace(/{{firstName}}/g,t||"User")).replace(/{{otp}}/g,r),n=(n=n.replace(/{{firstName}}/g,t||"User")).replace(/{{otp}}/g,r),await this.sendEmail({to:e,subject:i.subject,html:o,text:n})}async getEmailTemplate(e){try{return await a.T8.getEmailTemplate(e)}catch(e){return console.error("Failed to get email template:",e),null}}async testConnection(){try{if(this.config=await this.getEmailConfig(),!this.config)return console.error("Email configuration not found or incomplete"),!1;return this.transporter=s.createTransport({host:this.config.host,port:this.config.port,secure:this.config.secure,auth:{user:this.config.user,pass:this.config.password},tls:{rejectUnauthorized:!1}}),await this.transporter.verify(),console.log("Email connection test successful"),!0}catch(e){throw console.error("Email connection test failed:",e),this.transporter=null,e}}constructor(){this.transporter=null,this.config=null}}let o=new i,n=()=>Math.floor(1e5+9e5*Math.random()).toString()},21820:e=>{e.exports=require("os")},27910:e=>{e.exports=require("stream")},28354:e=>{e.exports=require("util")},29021:e=>{e.exports=require("fs")},29294:e=>{e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:e=>{e.exports=require("path")},34631:e=>{e.exports=require("tls")},37366:e=>{e.exports=require("dns")},44870:e=>{e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},55511:e=>{e.exports=require("crypto")},55591:e=>{e.exports=require("https")},60439:(e,r,t)=>{t.r(r),t.d(r,{patchFetch:()=>y,routeModule:()=>h,serverHooks:()=>w,workAsyncStorage:()=>f,workUnitAsyncStorage:()=>E});var s={};t.r(s),t.d(s,{GET:()=>p,POST:()=>g,PUT:()=>m});var a=t(96559),i=t(48088),o=t(37719),n=t(32190),l=t(12909),c=t(6710),u=t(21111),d=t(82629);async function p(e){try{let{authenticated:r,user:t}=await (0,l.b9)(e);if(!r||!t)return n.NextResponse.json({success:!1,error:"Unauthorized"},{status:401});if(!await (0,l.qc)(t.id))return n.NextResponse.json({success:!1,error:"Admin access required"},{status:403});let s=await c.T8.getEmailSettings(),a={...s,smtpPassword:s.smtpPassword?"••••••••":""};return n.NextResponse.json({success:!0,data:a})}catch(r){return console.error("Get email settings error:",r),await d.v5.logApiError(e,r,"GET_EMAIL_SETTINGS_ERROR"),n.NextResponse.json({success:!1,error:"Internal server error"},{status:500})}}async function m(e){try{let{authenticated:r,user:t}=await (0,l.b9)(e);if(!r||!t)return n.NextResponse.json({success:!1,error:"Unauthorized"},{status:401});if(!await (0,l.qc)(t.id))return n.NextResponse.json({success:!1,error:"Admin access required"},{status:403});let{smtpHost:s,smtpPort:a,smtpSecure:i,smtpUser:o,smtpPassword:u,fromName:d,fromEmail:p,emailEnabled:m}=await e.json();if(!s||!o||!p)return n.NextResponse.json({success:!1,error:"SMTP Host, User, and From Email are required"},{status:400});let g={smtpHost:s,smtpPort:parseInt(a)||587,smtpSecure:!!i,smtpUser:o,fromName:d||"HashCoreX",fromEmail:p,emailEnabled:!!m};return u&&"••••••••"!==u&&(g.smtpPassword=u),await c.T8.updateEmailSettings(g),n.NextResponse.json({success:!0,message:"Email settings updated successfully"})}catch(r){return console.error("Update email settings error:",r),await d.v5.logApiError(e,r,"UPDATE_EMAIL_SETTINGS_ERROR"),n.NextResponse.json({success:!1,error:"Internal server error"},{status:500})}}async function g(e){try{let{authenticated:r,user:t}=await (0,l.b9)(e);if(!r||!t)return n.NextResponse.json({success:!1,error:"Unauthorized"},{status:401});if(!await (0,l.qc)(t.id))return n.NextResponse.json({success:!1,error:"Admin access required"},{status:403});let{testEmail:s}=await e.json();if(!s)return n.NextResponse.json({success:!1,error:"Test email address is required"},{status:400});try{if(!await u.gm.testConnection())return n.NextResponse.json({success:!1,error:"Email connection test failed. Please check your SMTP settings."},{status:400})}catch(e){return console.error("Email connection test error:",e),n.NextResponse.json({success:!1,error:`Email connection failed: ${e instanceof Error?e.message:"Unknown error"}`},{status:400})}let a=await u.gm.getEmailTemplate("test_email"),i=!1;if(a){let e=a.htmlContent,r=a.textContent||"";e=e.replace(/{{testDate}}/g,new Date().toISOString()),r=r.replace(/{{testDate}}/g,new Date().toISOString()),i=await u.gm.sendEmail({to:s,subject:a.subject,html:e,text:r})}else i=await u.gm.sendEmail({to:s,subject:"HashCoreX Email Test",html:`
          <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
            <h2 style="color: #ffd60a;">HashCoreX Email Test</h2>
            <p>This is a test email to verify your SMTP configuration.</p>
            <p>If you received this email, your email settings are working correctly!</p>
            <p>Sent at: ${new Date().toISOString()}</p>
          </div>
        `,text:"HashCoreX Email Test - If you received this email, your email settings are working correctly!"});if(i)return n.NextResponse.json({success:!0,message:"Test email sent successfully"});return n.NextResponse.json({success:!1,error:"Failed to send test email"},{status:500})}catch(r){return console.error("Test email error:",r),await d.v5.logApiError(e,r,"TEST_EMAIL_ERROR"),n.NextResponse.json({success:!1,error:"Internal server error"},{status:500})}}let h=new a.AppRouteRouteModule({definition:{kind:i.RouteKind.APP_ROUTE,page:"/api/admin/email-settings/route",pathname:"/api/admin/email-settings",filename:"route",bundlePath:"app/api/admin/email-settings/route"},resolvedPagePath:"C:\\Users\\<USER>\\Desktop\\Hash_Minings\\hashcorex\\src\\app\\api\\admin\\email-settings\\route.ts",nextConfigOutput:"",userland:s}),{workAsyncStorage:f,workUnitAsyncStorage:E,serverHooks:w}=h;function y(){return(0,o.patchFetch)({workAsyncStorage:f,workUnitAsyncStorage:E})}},63033:e=>{e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},74075:e=>{e.exports=require("zlib")},79428:e=>{e.exports=require("buffer")},79551:e=>{e.exports=require("url")},79646:e=>{e.exports=require("child_process")},81630:e=>{e.exports=require("http")},82629:(e,r,t)=>{t.d(r,{v5:()=>a});var s=t(6710);class a{static async logError(e){try{let r=e.error instanceof Error?e.error:Error(String(e.error)),t={action:e.action,userId:e.userId,adminId:e.adminId,details:{message:r.message,stack:r.stack,name:r.name,requestUrl:e.requestUrl,requestMethod:e.requestMethod,requestBody:e.requestBody,additionalData:e.additionalData,timestamp:new Date().toISOString()},ipAddress:e.ipAddress,userAgent:e.userAgent};await s.AJ.create(t),console.error(`[${e.action}] Error logged:`,{message:r.message,stack:r.stack,userId:e.userId,adminId:e.adminId,url:e.requestUrl})}catch(r){console.error("Failed to log error to database:",r),console.error("Original error:",e.error)}}static async logApiError(e,r,t,s,a,i){try{let o=null;try{if("GET"!==e.method&&e.headers.get("content-type")?.includes("application/json")){let r=e.clone();(o=await r.json()).password&&(o.password="[REDACTED]"),o.token&&(o.token="[REDACTED]"),o.apiKey&&(o.apiKey="[REDACTED]")}}catch(e){}await this.logError({action:t,error:r,userId:s,adminId:a,ipAddress:e.headers.get("x-forwarded-for")||e.headers.get("x-real-ip")||"unknown",userAgent:e.headers.get("user-agent")||"unknown",requestUrl:e.url,requestMethod:e.method,requestBody:o,additionalData:i})}catch(e){console.error("Failed to log API error:",e),console.error("Original error:",r)}}static async logClientError(e){try{await s.AJ.create({action:"CLIENT_ERROR",userId:e.userId,details:{message:e.message,stack:e.stack,url:e.url,timestamp:e.timestamp,additionalData:e.additionalData},userAgent:e.userAgent})}catch(e){console.error("Failed to log client error:",e)}}static async logAuthError(e,r,t,s){await this.logApiError(e,r,"AUTH_ERROR",void 0,void 0,{email:t,...s})}static async logDatabaseError(e,r,t,a,i){try{await s.AJ.create({action:"DATABASE_ERROR",userId:a,details:{message:e instanceof Error?e.message:String(e),stack:e instanceof Error?e.stack:void 0,operation:r,table:t,timestamp:new Date().toISOString(),additionalData:i}})}catch(r){console.error("Failed to log database error:",r),console.error("Original error:",e)}}static async logBusinessError(e,r,t,s,a){await this.logError({action:"BUSINESS_LOGIC_ERROR",error:e,userId:t,adminId:s,additionalData:{operation:r,...a}})}static async logExternalApiError(e,r,t,s,a){await this.logError({action:"EXTERNAL_API_ERROR",error:e,userId:s,additionalData:{service:r,endpoint:t,...a}})}static async logValidationError(e,r,t,s,a){await this.logError({action:"VALIDATION_ERROR",error:e,userId:s,additionalData:{field:r,value:t,...a}})}}},91645:e=>{e.exports=require("net")},94735:e=>{e.exports=require("events")},96330:e=>{e.exports=require("@prisma/client")}};var r=require("../../../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),s=r.X(0,[4447,580,5315,9526,3306],()=>t(60439));module.exports=s})();