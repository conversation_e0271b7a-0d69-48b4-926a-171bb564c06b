"use strict";(()=>{var e={};e.id=6123,e.ids=[6123],e.modules={3295:e=>{e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},12909:(e,t,r)=>{r.d(t,{DT:()=>w,DY:()=>y,HU:()=>c,Lx:()=>h,b9:()=>f,qc:()=>x});var a=r(85663),s=r(43205),i=r.n(s),n=r(6710);let o=process.env.JWT_SECRET||"fallback-secret-key",u=process.env.JWT_EXPIRES_IN||"30d",d=async e=>await a.Ay.hash(e,12),l=async(e,t)=>await a.Ay.compare(e,t),c=e=>i().sign(e,o,{expiresIn:u}),p=e=>{try{return i().verify(e,o)}catch(e){return null}},m=()=>{let e="ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789",t="HC";for(let r=0;r<8;r++)t+=e.charAt(Math.floor(Math.random()*e.length));return t},f=async e=>{let t=e.headers.get("authorization")?.replace("Bearer ","")||e.cookies.get("auth-token")?.value;if(!t)return{authenticated:!1,user:null};let r=p(t);if(!r)return{authenticated:!1,user:null};let a=await n.Gy.findByEmail(r.email);return a?{authenticated:!0,user:a}:{authenticated:!1,user:null}},y=async e=>{let t,a;if(await n.Gy.findByEmail(e.email))throw Error("User already exists with this email");if(e.referralCode){let r=await n.Gy.findByReferralId(e.referralCode);if(!r)throw Error("Invalid referral code");t=r.id}let s=await d(e.password),i=!1;do a=m(),i=!await n.Gy.findByReferralId(a);while(!i);let o=await n.Gy.create({email:e.email,firstName:e.firstName,lastName:e.lastName,password:s,referralId:a});if(t){let{placeUserByReferralType:a}=await r.e(2746).then(r.bind(r,2746)),s="general";"left"===e.placementSide?s="left":"right"===e.placementSide&&(s="right"),await a(t,o.id,s)}return{id:o.id,email:o.email,referralId:o.referralId,kycStatus:o.kycStatus}},h=async e=>{let t=await n.Gy.findByEmail(e.email);if(!t||!await l(e.password,t.password))throw Error("Invalid email or password");return{token:c({userId:t.id,email:t.email}),user:{id:t.id,email:t.email,referralId:t.referralId,kycStatus:t.kycStatus}}},w=e=>/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(e),x=async e=>{let t=await n.Gy.findById(e);return t?.role==="ADMIN"}},27910:e=>{e.exports=require("stream")},28354:e=>{e.exports=require("util")},29294:e=>{e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},44870:e=>{e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},55511:e=>{e.exports=require("crypto")},63033:e=>{e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},66852:(e,t,r)=>{r.r(t),r.d(t,{patchFetch:()=>h,routeModule:()=>p,serverHooks:()=>y,workAsyncStorage:()=>m,workUnitAsyncStorage:()=>f});var a={};r.r(a),r.d(a,{GET:()=>l,POST:()=>c});var s=r(96559),i=r(48088),n=r(37719),o=r(32190),u=r(12909),d=r(31183);async function l(e){try{let{authenticated:t,user:r}=await (0,u.b9)(e);if(!t||!r)return o.NextResponse.json({success:!1,error:"Authentication required"},{status:401});if("ADMIN"!==r.role)return o.NextResponse.json({success:!1,error:"Admin access required"},{status:403});let a=await d.prisma.binaryPoints.findMany({include:{user:{select:{id:!0,email:!0,firstName:!0,lastName:!0,createdAt:!0}}},orderBy:[{leftPoints:"desc"},{rightPoints:"desc"}]});return o.NextResponse.json({success:!0,data:a})}catch(e){return console.error("Binary points data fetch error:",e),o.NextResponse.json({success:!1,error:"Failed to fetch binary points data"},{status:500})}}async function c(e){try{let t,{authenticated:r,user:a}=await (0,u.b9)(e);if(!r||!a)return o.NextResponse.json({success:!1,error:"Authentication required"},{status:401});if("ADMIN"!==a.role)return o.NextResponse.json({success:!1,error:"Admin access required"},{status:403});let{userId:s,leftPoints:i,rightPoints:n,action:l}=await e.json();if(!s||!i&&!n&&!l)return o.NextResponse.json({success:!1,error:"Invalid request data"},{status:400});return t="reset"===l?await d.prisma.binaryPoints.upsert({where:{userId:s},update:{leftPoints:0,rightPoints:0,flushDate:new Date},create:{userId:s,leftPoints:0,rightPoints:0,flushDate:new Date}}):"set"===l?await d.prisma.binaryPoints.upsert({where:{userId:s},update:{leftPoints:i||0,rightPoints:n||0},create:{userId:s,leftPoints:i||0,rightPoints:n||0}}):await d.prisma.binaryPoints.upsert({where:{userId:s},update:{leftPoints:{increment:i||0},rightPoints:{increment:n||0}},create:{userId:s,leftPoints:i||0,rightPoints:n||0}}),await d.prisma.systemLog.create({data:{action:"ADMIN_BINARY_POINTS_UPDATE",details:{adminId:a.id,adminEmail:a.email,targetUserId:s,action:l,leftPoints:i,rightPoints:n,timestamp:new Date().toISOString()}}}),o.NextResponse.json({success:!0,message:"Binary points updated successfully",data:t})}catch(e){return console.error("Binary points update error:",e),o.NextResponse.json({success:!1,error:"Failed to update binary points"},{status:500})}}let p=new s.AppRouteRouteModule({definition:{kind:i.RouteKind.APP_ROUTE,page:"/api/admin/binary-points/route",pathname:"/api/admin/binary-points",filename:"route",bundlePath:"app/api/admin/binary-points/route"},resolvedPagePath:"C:\\Users\\<USER>\\Desktop\\Hash_Minings\\hashcorex\\src\\app\\api\\admin\\binary-points\\route.ts",nextConfigOutput:"",userland:a}),{workAsyncStorage:m,workUnitAsyncStorage:f,serverHooks:y}=p;function h(){return(0,n.patchFetch)({workAsyncStorage:m,workUnitAsyncStorage:f})}},79428:e=>{e.exports=require("buffer")},96330:e=>{e.exports=require("@prisma/client")}};var t=require("../../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),a=t.X(0,[4447,580,5315,3306],()=>r(66852));module.exports=a})();