"use strict";(()=>{var e={};e.id=9315,e.ids=[9315],e.modules={3295:e=>{e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},12909:(e,t,r)=>{r.d(t,{DT:()=>w,DY:()=>h,HU:()=>c,Lx:()=>y,b9:()=>f,qc:()=>x});var s=r(85663),a=r(43205),i=r.n(a),n=r(6710);let o=process.env.JWT_SECRET||"fallback-secret-key",d=process.env.JWT_EXPIRES_IN||"30d",u=async e=>await s.Ay.hash(e,12),l=async(e,t)=>await s.Ay.compare(e,t),c=e=>i().sign(e,o,{expiresIn:d}),p=e=>{try{return i().verify(e,o)}catch(e){return null}},m=()=>{let e="ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789",t="HC";for(let r=0;r<8;r++)t+=e.charAt(Math.floor(Math.random()*e.length));return t},f=async e=>{let t=e.headers.get("authorization")?.replace("Bearer ","")||e.cookies.get("auth-token")?.value;if(!t)return{authenticated:!1,user:null};let r=p(t);if(!r)return{authenticated:!1,user:null};let s=await n.Gy.findByEmail(r.email);return s?{authenticated:!0,user:s}:{authenticated:!1,user:null}},h=async e=>{let t,s;if(await n.Gy.findByEmail(e.email))throw Error("User already exists with this email");if(e.referralCode){let r=await n.Gy.findByReferralId(e.referralCode);if(!r)throw Error("Invalid referral code");t=r.id}let a=await u(e.password),i=!1;do s=m(),i=!await n.Gy.findByReferralId(s);while(!i);let o=await n.Gy.create({email:e.email,firstName:e.firstName,lastName:e.lastName,password:a,referralId:s});if(t){let{placeUserByReferralType:s}=await r.e(2746).then(r.bind(r,2746)),a="general";"left"===e.placementSide?a="left":"right"===e.placementSide&&(a="right"),await s(t,o.id,a)}return{id:o.id,email:o.email,referralId:o.referralId,kycStatus:o.kycStatus}},y=async e=>{let t=await n.Gy.findByEmail(e.email);if(!t||!await l(e.password,t.password))throw Error("Invalid email or password");return{token:c({userId:t.id,email:t.email}),user:{id:t.id,email:t.email,referralId:t.referralId,kycStatus:t.kycStatus}}},w=e=>/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(e),x=async e=>{let t=await n.Gy.findById(e);return t?.role==="ADMIN"}},27910:e=>{e.exports=require("stream")},28354:e=>{e.exports=require("util")},29294:e=>{e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},44870:e=>{e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},55511:e=>{e.exports=require("crypto")},63033:e=>{e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},74621:(e,t,r)=>{r.r(t),r.d(t,{patchFetch:()=>y,routeModule:()=>p,serverHooks:()=>h,workAsyncStorage:()=>m,workUnitAsyncStorage:()=>f});var s={};r.r(s),r.d(s,{GET:()=>l,POST:()=>c});var a=r(96559),i=r(48088),n=r(37719),o=r(32190),d=r(12909),u=r(6710);async function l(e){try{let t,{authenticated:r,user:s}=await (0,d.b9)(e);if(!r||!s)return o.NextResponse.json({success:!1,error:"Not authenticated"},{status:401});if(!await (0,d.qc)(s.id))return o.NextResponse.json({success:!1,error:"Admin access required"},{status:403});let{searchParams:a}=new URL(e.url),i=parseInt(a.get("limit")||"50"),n=parseInt(a.get("offset")||"0"),l=a.get("status"),c=a.get("userId")||void 0;t=c?await u.J6.findByUserId(c,{status:l||void 0,limit:Math.min(i,100),offset:n}):await u.J6.findAll({status:l||void 0,limit:Math.min(i,100),offset:n});let p=await u.J6.getDepositStats();return o.NextResponse.json({success:!0,data:{deposits:t.map(e=>({id:e.id,userId:e.userId,user:e.user?{id:e.user.id,email:e.user.email,firstName:e.user.firstName,lastName:e.user.lastName}:null,transactionId:e.transactionId,amount:e.amount,usdtAmount:e.usdtAmount,tronAddress:e.tronAddress,senderAddress:e.senderAddress,status:e.status,blockNumber:e.blockNumber,blockTimestamp:e.blockTimestamp,confirmations:e.confirmations,verifiedAt:e.verifiedAt,processedAt:e.processedAt,failureReason:e.failureReason,createdAt:e.createdAt,updatedAt:e.updatedAt})),stats:{totalDeposits:p.totalDeposits,totalAmount:p.totalAmount,pendingDeposits:p.pendingDeposits},pagination:{limit:i,offset:n,hasMore:t.length===i},filters:{status:l,userId:c}}})}catch(e){return console.error("Admin deposits fetch error:",e),o.NextResponse.json({success:!1,error:"Failed to fetch deposits"},{status:500})}}async function c(e){try{let{authenticated:t,user:r}=await (0,d.b9)(e);if(!t||!r)return o.NextResponse.json({success:!1,error:"Not authenticated"},{status:401});if(!await (0,d.qc)(r.id))return o.NextResponse.json({success:!1,error:"Admin access required"},{status:403});let{action:s,transactionId:a,reason:i}=await e.json();if(!s||!a)return o.NextResponse.json({success:!1,error:"Action and transaction ID are required"},{status:400});if(!await u.J6.findByTransactionId(a))return o.NextResponse.json({success:!1,error:"Deposit not found"},{status:404});return o.NextResponse.json({success:!1,error:"Manual deposit actions are no longer supported. Deposits are processed automatically by the system."},{status:400})}catch(e){return console.error("Admin deposit action error:",e),o.NextResponse.json({success:!1,error:"Failed to process deposit action"},{status:500})}}let p=new a.AppRouteRouteModule({definition:{kind:i.RouteKind.APP_ROUTE,page:"/api/admin/deposits/route",pathname:"/api/admin/deposits",filename:"route",bundlePath:"app/api/admin/deposits/route"},resolvedPagePath:"C:\\Users\\<USER>\\Desktop\\Hash_Minings\\hashcorex\\src\\app\\api\\admin\\deposits\\route.ts",nextConfigOutput:"",userland:s}),{workAsyncStorage:m,workUnitAsyncStorage:f,serverHooks:h}=p;function y(){return(0,n.patchFetch)({workAsyncStorage:m,workUnitAsyncStorage:f})}},79428:e=>{e.exports=require("buffer")},96330:e=>{e.exports=require("@prisma/client")}};var t=require("../../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),s=t.X(0,[4447,580,5315,3306],()=>r(74621));module.exports=s})();