"use strict";(()=>{var e={};e.id=7700,e.ids=[7700],e.modules={803:(e,t,n)=>{n.d(t,{x:()=>o});var r=n(92731),s=n(2746),a=n(6710);class i{constructor(){this.tasks=new Map,this.isInitialized=!1}static getInstance(){return i.instance||(i.instance=new i),i.instance}async initialize(){if(this.isInitialized)return void console.log("Scheduler service already initialized");console.log("Initializing server-side scheduler service...");try{this.scheduleDailyROI(),this.scheduleWeeklyPayout(),this.scheduleBinaryMatching(),this.scheduleDepositProcessing(),this.isInitialized=!0,console.log("Server-side scheduler service initialized successfully"),await a.AJ.create({action:"SCHEDULER_SERVICE_INITIALIZED",details:{tasksScheduled:this.tasks.size,initTime:new Date().toISOString(),tasks:Array.from(this.tasks.keys())}})}catch(e){throw console.error("Error initializing scheduler service:",e),e}}scheduleDailyROI(){let e="daily-roi",t=new Date,n=new Date;n.setUTCHours(0,1,0,0),n<=t&&n.setUTCDate(n.getUTCDate()+1);let r=n.getTime()-t.getTime();console.log(`Scheduling daily ROI calculation for ${n.toISOString()}`),setTimeout(()=>{this.runDailyROI();let t=setInterval(()=>{this.runDailyROI()},864e5);this.tasks.set(e,{name:e,interval:t,isRunning:!1,lastRun:null,nextRun:new Date(Date.now()+864e5),runCount:0})},r),this.tasks.set(e,{name:e,interval:null,isRunning:!1,lastRun:null,nextRun:n,runCount:0})}scheduleWeeklyPayout(){let e="weekly-payout",t=this.getNextWeeklyTime(6,15),n=t.getTime()-Date.now();console.log(`Scheduling weekly payout for ${t.toISOString()}`),setTimeout(()=>{this.runWeeklyPayout();let t=setInterval(()=>{this.runWeeklyPayout()},6048e5);this.tasks.set(e,{name:e,interval:t,isRunning:!1,lastRun:null,nextRun:new Date(Date.now()+6048e5),runCount:0})},n),this.tasks.set(e,{name:e,interval:null,isRunning:!1,lastRun:null,nextRun:t,runCount:0})}scheduleBinaryMatching(){let e="binary-matching",t=this.getNextWeeklyTime(6,15),n=t.getTime()-Date.now();console.log(`Scheduling binary matching for ${t.toISOString()}`),setTimeout(()=>{this.runBinaryMatching();let t=setInterval(()=>{this.runBinaryMatching()},6048e5);this.tasks.set(e,{name:e,interval:t,isRunning:!1,lastRun:null,nextRun:new Date(Date.now()+6048e5),runCount:0})},n),this.tasks.set(e,{name:e,interval:null,isRunning:!1,lastRun:null,nextRun:t,runCount:0})}scheduleDepositProcessing(){let e="deposit-processing";console.log("Scheduling deposit processing every 10 minutes");let t=setInterval(()=>{this.runDepositProcessing()},6e5);this.runDepositProcessing(),this.tasks.set(e,{name:e,interval:t,isRunning:!1,lastRun:null,nextRun:new Date(Date.now()+6e5),runCount:0})}getNextWeeklyTime(e,t){let n=new Date,r=new Date;r.setUTCHours(t,0,0,0);let s=e-n.getUTCDay();return(s<0||0===s&&n.getUTCHours()>=t)&&(s+=7),r.setUTCDate(n.getUTCDate()+s),r}async runDailyROI(){let e=this.tasks.get("daily-roi");if(!e||e.isRunning)return void console.log("Daily ROI task already running or not found");e.isRunning=!0,e.lastRun=new Date,e.runCount++;try{console.log("Starting scheduled daily ROI calculation...");let t=await (0,r.eB)();console.log(`Expired ${t} old mining units`);let n=await (0,r.WL)();console.log(`Processed ${n.length} mining units for daily ROI`);let s=n.reduce((e,t)=>e+t.earnings,0),i=n.filter(e=>e.expired).length;await a.AJ.create({action:"DAILY_ROI_SCHEDULED_EXECUTED",details:{unitsProcessed:n.length,totalEarnings:s,expiredUnits:i,oldUnitsExpired:t,executionTime:new Date().toISOString(),runCount:e.runCount}}),console.log(`Daily ROI calculation completed successfully (run #${e.runCount})`)}catch(t){console.error("Daily ROI scheduled task error:",t),await a.AJ.create({action:"DAILY_ROI_SCHEDULED_ERROR",details:{error:t instanceof Error?t.message:"Unknown error",stack:t instanceof Error?t.stack:void 0,timestamp:new Date().toISOString(),runCount:e.runCount}})}finally{e.isRunning=!1,e.nextRun=new Date(Date.now()+864e5)}}async runWeeklyPayout(){let e=this.tasks.get("weekly-payout");if(!e||e.isRunning)return void console.log("Weekly payout task already running or not found");e.isRunning=!0,e.lastRun=new Date,e.runCount++;try{console.log("Starting scheduled weekly earnings payout...");let t=await (0,r.Oh)();console.log(`Processed earnings for ${t.length} users`);let n=t.reduce((e,t)=>e+t.totalEarnings,0);await a.AJ.create({action:"WEEKLY_PAYOUT_SCHEDULED_EXECUTED",details:{usersProcessed:t.length,totalDistributed:n,executionTime:new Date().toISOString(),payoutDay:"Saturday",payoutTime:"15:00 UTC",runCount:e.runCount}}),console.log(`Weekly payout completed successfully (run #${e.runCount})`)}catch(t){console.error("Weekly payout scheduled task error:",t),await a.AJ.create({action:"WEEKLY_PAYOUT_SCHEDULED_ERROR",details:{error:t instanceof Error?t.message:"Unknown error",stack:t instanceof Error?t.stack:void 0,timestamp:new Date().toISOString(),runCount:e.runCount}})}finally{e.isRunning=!1,e.nextRun=new Date(Date.now()+6048e5)}}async runBinaryMatching(){let e=this.tasks.get("binary-matching");if(!e||e.isRunning)return void console.log("Binary matching task already running or not found");e.isRunning=!0,e.lastRun=new Date,e.runCount++;try{console.log("Starting scheduled binary matching...");let t=await (0,s.E5)(),n=t.reduce((e,t)=>e+t.payout,0),r=t.reduce((e,t)=>e+t.matchedPoints,0);await a.AJ.create({action:"BINARY_MATCHING_SCHEDULED_EXECUTED",details:{usersProcessed:t.length,totalPayouts:n,totalMatchedPoints:r,executionTime:new Date().toISOString(),matchingTime:"15:00 UTC (Weekly)",runCount:e.runCount}}),console.log(`Binary matching completed successfully (run #${e.runCount})`)}catch(t){console.error("Binary matching scheduled task error:",t),await a.AJ.create({action:"BINARY_MATCHING_SCHEDULED_ERROR",details:{error:t instanceof Error?t.message:"Unknown error",stack:t instanceof Error?t.stack:void 0,timestamp:new Date().toISOString(),runCount:e.runCount}})}finally{e.isRunning=!1,e.nextRun=new Date(Date.now()+6048e5)}}async runDepositProcessing(){let e=this.tasks.get("deposit-processing");if(!e||e.isRunning)return void console.log("Deposit processing task already running or not found");e.isRunning=!0,e.lastRun=new Date,e.runCount++;try{console.log("Starting scheduled deposit processing...");let{processDeposits:t}=await Promise.all([n.e(9480),n.e(9046)]).then(n.bind(n,79046)),r=await t();await a.AJ.create({action:"DEPOSIT_PROCESSING_SCHEDULED_EXECUTED",details:{...r,executionTime:new Date().toISOString(),runCount:e.runCount}}),console.log(`Deposit processing completed successfully (run #${e.runCount})`)}catch(t){console.error("Deposit processing scheduled task error:",t),await a.AJ.create({action:"DEPOSIT_PROCESSING_SCHEDULED_ERROR",details:{error:t instanceof Error?t.message:"Unknown error",stack:t instanceof Error?t.stack:void 0,timestamp:new Date().toISOString(),runCount:e.runCount}})}finally{e.isRunning=!1,e.nextRun=new Date(Date.now()+6e5)}}stop(){for(let[e,t]of(console.log("Stopping scheduler service..."),this.tasks))t.interval&&(clearInterval(t.interval),console.log(`Stopped task: ${e}`));this.tasks.clear(),this.isInitialized=!1,console.log("Scheduler service stopped")}getStatus(){return{isInitialized:this.isInitialized,totalTasks:this.tasks.size,tasks:Array.from(this.tasks.entries()).map(([e,t])=>({name:e,isRunning:t.isRunning,lastRun:t.lastRun?.toISOString()||null,nextRun:t.nextRun?.toISOString()||null,runCount:t.runCount}))}}async triggerTask(e){switch(e){case"daily-roi":await this.runDailyROI();break;case"weekly-payout":await this.runWeeklyPayout();break;case"binary-matching":await this.runBinaryMatching();break;case"deposit-processing":await this.runDepositProcessing();break;default:throw Error(`Unknown task: ${e}`)}}}let o=i.getInstance()},3295:e=>{e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},12909:(e,t,n)=>{n.d(t,{DT:()=>w,DY:()=>p,HU:()=>d,Lx:()=>m,b9:()=>y,qc:()=>k});var r=n(85663),s=n(43205),a=n.n(s),i=n(6710);let o=process.env.JWT_SECRET||"fallback-secret-key",l=process.env.JWT_EXPIRES_IN||"30d",u=async e=>await r.Ay.hash(e,12),c=async(e,t)=>await r.Ay.compare(e,t),d=e=>a().sign(e,o,{expiresIn:l}),g=e=>{try{return a().verify(e,o)}catch(e){return null}},h=()=>{let e="ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789",t="HC";for(let n=0;n<8;n++)t+=e.charAt(Math.floor(Math.random()*e.length));return t},y=async e=>{let t=e.headers.get("authorization")?.replace("Bearer ","")||e.cookies.get("auth-token")?.value;if(!t)return{authenticated:!1,user:null};let n=g(t);if(!n)return{authenticated:!1,user:null};let r=await i.Gy.findByEmail(n.email);return r?{authenticated:!0,user:r}:{authenticated:!1,user:null}},p=async e=>{let t,r;if(await i.Gy.findByEmail(e.email))throw Error("User already exists with this email");if(e.referralCode){let n=await i.Gy.findByReferralId(e.referralCode);if(!n)throw Error("Invalid referral code");t=n.id}let s=await u(e.password),a=!1;do r=h(),a=!await i.Gy.findByReferralId(r);while(!a);let o=await i.Gy.create({email:e.email,firstName:e.firstName,lastName:e.lastName,password:s,referralId:r});if(t){let{placeUserByReferralType:r}=await n.e(2746).then(n.bind(n,2746)),s="general";"left"===e.placementSide?s="left":"right"===e.placementSide&&(s="right"),await r(t,o.id,s)}return{id:o.id,email:o.email,referralId:o.referralId,kycStatus:o.kycStatus}},m=async e=>{let t=await i.Gy.findByEmail(e.email);if(!t||!await c(e.password,t.password))throw Error("Invalid email or password");return{token:d({userId:t.id,email:t.email}),user:{id:t.id,email:t.email,referralId:t.referralId,kycStatus:t.kycStatus}}},w=e=>/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(e),k=async e=>{let t=await i.Gy.findById(e);return t?.role==="ADMIN"}},27910:e=>{e.exports=require("stream")},28354:e=>{e.exports=require("util")},29294:e=>{e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},44870:e=>{e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},55511:e=>{e.exports=require("crypto")},63033:e=>{e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},67258:(e,t,n)=>{n.r(t),n.d(t,{patchFetch:()=>m,routeModule:()=>g,serverHooks:()=>p,workAsyncStorage:()=>h,workUnitAsyncStorage:()=>y});var r={};n.r(r),n.d(r,{POST:()=>d});var s=n(96559),a=n(48088),i=n(37719),o=n(32190),l=n(12909),u=n(803),c=n(6710);async function d(e){try{let{authenticated:t,user:n}=await (0,l.b9)(e);if(!t||!n)return o.NextResponse.json({success:!1,error:"Not authenticated"},{status:401});if("ADMIN"!==n.role)return o.NextResponse.json({success:!1,error:"Access denied"},{status:403});let{taskName:r}=await e.json();if(!r)return o.NextResponse.json({success:!1,error:"Task name is required"},{status:400});let s=["daily-roi","weekly-payout","binary-matching","deposit-processing"];if(!s.includes(r))return o.NextResponse.json({success:!1,error:`Invalid task name. Valid tasks: ${s.join(", ")}`},{status:400});return await c.AJ.create({action:"MANUAL_TASK_TRIGGER",adminId:n.id,details:{taskName:r,triggeredBy:n.email,triggeredAt:new Date().toISOString()}}),await u.x.triggerTask(r),o.NextResponse.json({success:!0,message:`Task '${r}' triggered successfully`,data:{taskName:r,triggeredBy:n.email,triggeredAt:new Date().toISOString()}})}catch(e){return console.error("Error triggering scheduled task:",e),o.NextResponse.json({success:!1,error:e instanceof Error?e.message:"Failed to trigger task"},{status:500})}}let g=new s.AppRouteRouteModule({definition:{kind:a.RouteKind.APP_ROUTE,page:"/api/admin/scheduler/trigger/route",pathname:"/api/admin/scheduler/trigger",filename:"route",bundlePath:"app/api/admin/scheduler/trigger/route"},resolvedPagePath:"C:\\Users\\<USER>\\Desktop\\Hash_Minings\\hashcorex\\src\\app\\api\\admin\\scheduler\\trigger\\route.ts",nextConfigOutput:"",userland:r}),{workAsyncStorage:h,workUnitAsyncStorage:y,serverHooks:p}=g;function m(){return(0,i.patchFetch)({workAsyncStorage:h,workUnitAsyncStorage:y})}},79428:e=>{e.exports=require("buffer")},96330:e=>{e.exports=require("@prisma/client")}};var t=require("../../../../../webpack-runtime.js");t.C(e);var n=e=>t(t.s=e),r=t.X(0,[4447,580,5315,3306,2746,5112],()=>n(67258));module.exports=r})();