"use strict";exports.id=6679,exports.ids=[6679],exports.modules={36679:(e,r,t)=>{t.d(r,{mc:()=>i,so:()=>c,xA:()=>n,si:()=>g});var s=t(60687),l=t(43210),o=t(4780);let i=({children:e,className:r,size:t="lg"})=>(0,s.jsx)("div",{className:(0,o.cn)("mx-auto px-4 sm:px-6 lg:px-8",{sm:"max-w-3xl",md:"max-w-5xl",lg:"max-w-7xl",xl:"max-w-8xl",full:"max-w-full"}[t],r),children:e}),n=({children:e,className:r,cols:t={default:1,md:2,lg:3},gap:l=6})=>(0,s.jsx)("div",{className:(0,o.cn)((()=>{let e=["grid"];return t.default&&e.push(`grid-cols-${t.default}`),t.sm&&e.push(`sm:grid-cols-${t.sm}`),t.md&&e.push(`md:grid-cols-${t.md}`),t.lg&&e.push(`lg:grid-cols-${t.lg}`),t.xl&&e.push(`xl:grid-cols-${t.xl}`),e.push(`gap-${l}`),e.join(" ")})(),r),children:e}),c=({children:e,className:r,direction:t="row",align:l="start",justify:i="start",wrap:n="nowrap",gap:c=0})=>(0,s.jsx)("div",{className:(0,o.cn)("flex",{row:"flex-row",col:"flex-col","row-reverse":"flex-row-reverse","col-reverse":"flex-col-reverse"}[t],{start:"items-start",center:"items-center",end:"items-end",stretch:"items-stretch",baseline:"items-baseline"}[l],{start:"justify-start",center:"justify-center",end:"justify-end",between:"justify-between",around:"justify-around",evenly:"justify-evenly"}[i],{wrap:"flex-wrap",nowrap:"flex-nowrap","wrap-reverse":"flex-wrap-reverse"}[n],c>0&&`gap-${c}`,r),children:e});var a=t(85814),x=t.n(a),h=t(77849),d=t(71180),m=t(11860),j=t(12941);let g=({children:e})=>{let[r,t]=(0,l.useState)(!1);return(0,s.jsxs)("div",{className:"min-h-screen bg-white",children:[(0,s.jsx)("nav",{className:"fixed top-0 w-full z-50 glass-morphism border-b border-white/20",children:(0,s.jsxs)(i,{children:[(0,s.jsxs)(c,{justify:"between",align:"center",className:"h-16 lg:h-20",children:[(0,s.jsxs)(x(),{href:"/",className:"flex items-center space-x-2 lg:space-x-3",children:[(0,s.jsxs)("div",{className:"relative",children:[(0,s.jsx)(d.MX,{className:"h-8 w-8 lg:h-10 lg:w-10 text-yellow-500 animate-pulse"}),(0,s.jsx)("div",{className:"absolute inset-0 bg-yellow-500/20 rounded-full animate-ping"})]}),(0,s.jsx)("span",{className:"text-xl lg:text-3xl font-black text-slate-900",children:"HashCoreX"})]}),(0,s.jsxs)("div",{className:"hidden lg:flex items-center gap-8",children:[(0,s.jsx)(x(),{href:"/about",className:"text-gray-700 hover:text-yellow-600 transition-all duration-300 font-medium hover:scale-105",children:"About"}),(0,s.jsx)(x(),{href:"/how-it-works",className:"text-gray-700 hover:text-yellow-600 transition-all duration-300 font-medium hover:scale-105",children:"How It Works"}),(0,s.jsx)(x(),{href:"/contact",className:"text-gray-700 hover:text-yellow-600 transition-all duration-300 font-medium hover:scale-105",children:"Contact"}),(0,s.jsx)(x(),{href:"/login",children:(0,s.jsx)(h.$n,{variant:"ghost",size:"md",className:"font-semibold",children:"Login"})}),(0,s.jsx)(x(),{href:"/register",children:(0,s.jsx)(h.$n,{variant:"primary",size:"md",className:"font-semibold",children:"Get Started"})})]}),(0,s.jsx)("button",{onClick:()=>t(!r),className:"lg:hidden p-2 rounded-lg hover:bg-gray-100 transition-colors","aria-label":"Toggle mobile menu",children:r?(0,s.jsx)(m.A,{className:"h-6 w-6 text-gray-700"}):(0,s.jsx)(j.A,{className:"h-6 w-6 text-gray-700"})})]}),r&&(0,s.jsx)("div",{className:"lg:hidden absolute top-full left-0 right-0 bg-white border-b border-gray-200 shadow-lg",children:(0,s.jsxs)("div",{className:"px-4 py-6 space-y-4",children:[(0,s.jsx)(x(),{href:"/about",className:"block text-gray-700 hover:text-yellow-600 transition-colors font-medium py-2",onClick:()=>t(!1),children:"About"}),(0,s.jsx)(x(),{href:"/how-it-works",className:"block text-gray-700 hover:text-yellow-600 transition-colors font-medium py-2",onClick:()=>t(!1),children:"How It Works"}),(0,s.jsx)(x(),{href:"/contact",className:"block text-gray-700 hover:text-yellow-600 transition-colors font-medium py-2",onClick:()=>t(!1),children:"Contact"}),(0,s.jsxs)("div",{className:"pt-4 space-y-3",children:[(0,s.jsx)(x(),{href:"/login",onClick:()=>t(!1),children:(0,s.jsx)(h.$n,{variant:"ghost",size:"md",className:"w-full font-semibold",children:"Login"})}),(0,s.jsx)(x(),{href:"/register",onClick:()=>t(!1),children:(0,s.jsx)(h.$n,{variant:"primary",size:"md",className:"w-full font-semibold",children:"Get Started"})})]})]})})]})}),(0,s.jsx)("main",{className:"pt-16 lg:pt-20",children:e}),(0,s.jsx)("footer",{className:"bg-slate-900 text-white py-12",children:(0,s.jsxs)(i,{children:[(0,s.jsxs)(n,{cols:{default:1,md:4},gap:8,children:[(0,s.jsxs)("div",{children:[(0,s.jsxs)(c,{align:"center",gap:2,className:"mb-4",children:[(0,s.jsx)(d.MX,{className:"h-8 w-8 text-yellow-400"}),(0,s.jsx)("span",{className:"text-2xl font-bold",children:"HashCoreX"})]}),(0,s.jsx)("p",{className:"text-gray-300",children:"Sustainable cryptocurrency mining powered by renewable energy."})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("h4",{className:"font-semibold mb-4 text-white",children:"Platform"}),(0,s.jsxs)("div",{className:"space-y-2",children:[(0,s.jsx)(x(),{href:"/about",className:"block text-gray-300 hover:text-white transition-colors",children:"About Us"}),(0,s.jsx)(x(),{href:"/how-it-works",className:"block text-gray-300 hover:text-white transition-colors",children:"How It Works"}),(0,s.jsx)(x(),{href:"/contact",className:"block text-gray-300 hover:text-white transition-colors",children:"Contact Us"})]})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("h4",{className:"font-semibold mb-4 text-white",children:"Support"}),(0,s.jsxs)("div",{className:"space-y-2",children:[(0,s.jsx)(x(),{href:"/contact",className:"block text-gray-300 hover:text-white transition-colors",children:"Contact Us"}),(0,s.jsx)(x(),{href:"/contact",className:"block text-gray-300 hover:text-white transition-colors",children:"Help Center"})]})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("h4",{className:"font-semibold mb-4 text-white",children:"Legal"}),(0,s.jsxs)("div",{className:"space-y-2",children:[(0,s.jsx)(x(),{href:"/privacy",className:"block text-gray-300 hover:text-white transition-colors",children:"Privacy Policy"}),(0,s.jsx)(x(),{href:"/terms",className:"block text-gray-300 hover:text-white transition-colors",children:"Terms of Service"})]})]})]}),(0,s.jsx)("div",{className:"border-t border-gray-700 mt-12 pt-8 text-center text-gray-300",children:(0,s.jsx)("p",{children:"\xa9 2024 HashCoreX. All rights reserved."})})]})})]})}},71180:(e,r,t)=>{t.d(r,{Lc:()=>i,hK:()=>n,NC:()=>o,MX:()=>l,Kj:()=>c});var s=t(60687);t(43210);let l=({className:e,size:r=24})=>(0,s.jsxs)("svg",{width:r,height:r,viewBox:"0 0 24 24",fill:"none",xmlns:"http://www.w3.org/2000/svg",className:e,children:[(0,s.jsx)("rect",{x:"2",y:"4",width:"20",height:"16",rx:"2",stroke:"currentColor",strokeWidth:"2",fill:"none"}),(0,s.jsx)("line",{x1:"2",y1:"8",x2:"22",y2:"8",stroke:"currentColor",strokeWidth:"2"}),(0,s.jsx)("line",{x1:"2",y1:"12",x2:"22",y2:"12",stroke:"currentColor",strokeWidth:"2"}),(0,s.jsx)("line",{x1:"2",y1:"16",x2:"22",y2:"16",stroke:"currentColor",strokeWidth:"2"}),(0,s.jsx)("line",{x1:"6",y1:"4",x2:"6",y2:"20",stroke:"currentColor",strokeWidth:"2"}),(0,s.jsx)("line",{x1:"10",y1:"4",x2:"10",y2:"20",stroke:"currentColor",strokeWidth:"2"}),(0,s.jsx)("line",{x1:"14",y1:"4",x2:"14",y2:"20",stroke:"currentColor",strokeWidth:"2"}),(0,s.jsx)("line",{x1:"18",y1:"4",x2:"18",y2:"20",stroke:"currentColor",strokeWidth:"2"}),(0,s.jsx)("circle",{cx:"20",cy:"2",r:"1",fill:"currentColor"}),(0,s.jsx)("path",{d:"M19 1l1 1-1 1-1-1z",fill:"currentColor"})]}),o=({className:e,size:r=24})=>(0,s.jsxs)("svg",{width:r,height:r,viewBox:"0 0 24 24",fill:"none",xmlns:"http://www.w3.org/2000/svg",className:e,children:[(0,s.jsx)("rect",{x:"2",y:"6",width:"20",height:"12",rx:"2",stroke:"currentColor",strokeWidth:"2",fill:"none"}),(0,s.jsx)("rect",{x:"4",y:"8",width:"3",height:"3",rx:"0.5",fill:"currentColor"}),(0,s.jsx)("rect",{x:"8",y:"8",width:"3",height:"3",rx:"0.5",fill:"currentColor"}),(0,s.jsx)("rect",{x:"12",y:"8",width:"3",height:"3",rx:"0.5",fill:"currentColor"}),(0,s.jsx)("rect",{x:"16",y:"8",width:"3",height:"3",rx:"0.5",fill:"currentColor"}),(0,s.jsx)("rect",{x:"4",y:"12",width:"3",height:"3",rx:"0.5",fill:"currentColor"}),(0,s.jsx)("rect",{x:"8",y:"12",width:"3",height:"3",rx:"0.5",fill:"currentColor"}),(0,s.jsx)("rect",{x:"12",y:"12",width:"3",height:"3",rx:"0.5",fill:"currentColor"}),(0,s.jsx)("rect",{x:"16",y:"12",width:"3",height:"3",rx:"0.5",fill:"currentColor"}),(0,s.jsx)("circle",{cx:"20",cy:"4",r:"1",fill:"currentColor"}),(0,s.jsx)("line",{x1:"20",y1:"4",x2:"20",y2:"6",stroke:"currentColor",strokeWidth:"2"}),(0,s.jsx)("line",{x1:"18",y1:"2",x2:"22",y2:"2",stroke:"currentColor",strokeWidth:"2"}),(0,s.jsx)("line",{x1:"19",y1:"1",x2:"21",y2:"3",stroke:"currentColor",strokeWidth:"1"}),(0,s.jsx)("line",{x1:"21",y1:"1",x2:"19",y2:"3",stroke:"currentColor",strokeWidth:"1"})]}),i=({className:e,size:r=24})=>(0,s.jsxs)("svg",{width:r,height:r,viewBox:"0 0 24 24",fill:"none",xmlns:"http://www.w3.org/2000/svg",className:e,children:[(0,s.jsx)("circle",{cx:"12",cy:"12",r:"10",stroke:"currentColor",strokeWidth:"2",fill:"none"}),(0,s.jsx)("path",{d:"M8 12h8M12 8v8",stroke:"currentColor",strokeWidth:"2"}),(0,s.jsx)("path",{d:"M10 8h4c1.1 0 2 .9 2 2s-.9 2-2 2h-4",stroke:"currentColor",strokeWidth:"2",fill:"none"}),(0,s.jsx)("path",{d:"M10 14h4c1.1 0 2 .9 2 2s-.9 2-2 2h-4",stroke:"currentColor",strokeWidth:"2",fill:"none"}),(0,s.jsx)("line",{x1:"12",y1:"6",x2:"12",y2:"8",stroke:"currentColor",strokeWidth:"2"}),(0,s.jsx)("line",{x1:"12",y1:"16",x2:"12",y2:"18",stroke:"currentColor",strokeWidth:"2"})]}),n=({className:e,size:r=24})=>(0,s.jsxs)("svg",{width:r,height:r,viewBox:"0 0 24 24",fill:"none",xmlns:"http://www.w3.org/2000/svg",className:e,children:[(0,s.jsx)("path",{d:"M12 2C8 2 5 5 5 9c0 5 7 13 7 13s7-8 7-13c0-4-3-7-7-7z",stroke:"currentColor",strokeWidth:"2",fill:"none"}),(0,s.jsx)("path",{d:"M12 9c0-2-1-3-3-3s-3 1-3 3 1 3 3 3 3-1 3-3z",stroke:"currentColor",strokeWidth:"2",fill:"none"}),(0,s.jsx)("path",{d:"M8 9c0 1 1 2 2 2s2-1 2-2-1-2-2-2-2 1-2 2z",fill:"currentColor"})]}),c=({className:e,size:r=24})=>(0,s.jsxs)("svg",{width:r,height:r,viewBox:"0 0 24 24",fill:"none",xmlns:"http://www.w3.org/2000/svg",className:e,children:[(0,s.jsx)("line",{x1:"12",y1:"12",x2:"12",y2:"22",stroke:"currentColor",strokeWidth:"2"}),(0,s.jsx)("path",{d:"M12 12L8 4c-1-2 0-4 2-4s3 2 2 4l-2 8z",stroke:"currentColor",strokeWidth:"2",fill:"none"}),(0,s.jsx)("path",{d:"M12 12l8-4c2-1 4 0 4 2s-2 3-4 2l-8-2z",stroke:"currentColor",strokeWidth:"2",fill:"none"}),(0,s.jsx)("path",{d:"M12 12l-4 8c-1 2-3 2-4 0s0-3 2-4l8-2z",stroke:"currentColor",strokeWidth:"2",fill:"none"}),(0,s.jsx)("circle",{cx:"12",cy:"12",r:"1",fill:"currentColor"}),(0,s.jsx)("line",{x1:"10",y1:"22",x2:"14",y2:"22",stroke:"currentColor",strokeWidth:"2"})]})}};