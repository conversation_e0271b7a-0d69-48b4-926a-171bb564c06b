"use strict";(()=>{var e={};e.id=4356,e.ids=[4356],e.modules={3295:e=>{e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},12909:(e,r,t)=>{t.d(r,{DT:()=>y,DY:()=>g,HU:()=>u,Lx:()=>f,b9:()=>E,qc:()=>w});var a=t(85663),s=t(43205),o=t.n(s),i=t(6710);let n=process.env.JWT_SECRET||"fallback-secret-key",l=process.env.JWT_EXPIRES_IN||"30d",c=async e=>await a.Ay.hash(e,12),d=async(e,r)=>await a.Ay.compare(e,r),u=e=>o().sign(e,n,{expiresIn:l}),p=e=>{try{return o().verify(e,n)}catch(e){return null}},m=()=>{let e="ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789",r="HC";for(let t=0;t<8;t++)r+=e.charAt(Math.floor(Math.random()*e.length));return r},E=async e=>{let r=e.headers.get("authorization")?.replace("Bearer ","")||e.cookies.get("auth-token")?.value;if(!r)return{authenticated:!1,user:null};let t=p(r);if(!t)return{authenticated:!1,user:null};let a=await i.Gy.findByEmail(t.email);return a?{authenticated:!0,user:a}:{authenticated:!1,user:null}},g=async e=>{let r,a;if(await i.Gy.findByEmail(e.email))throw Error("User already exists with this email");if(e.referralCode){let t=await i.Gy.findByReferralId(e.referralCode);if(!t)throw Error("Invalid referral code");r=t.id}let s=await c(e.password),o=!1;do a=m(),o=!await i.Gy.findByReferralId(a);while(!o);let n=await i.Gy.create({email:e.email,firstName:e.firstName,lastName:e.lastName,password:s,referralId:a});if(r){let{placeUserByReferralType:a}=await t.e(2746).then(t.bind(t,2746)),s="general";"left"===e.placementSide?s="left":"right"===e.placementSide&&(s="right"),await a(r,n.id,s)}return{id:n.id,email:n.email,referralId:n.referralId,kycStatus:n.kycStatus}},f=async e=>{let r=await i.Gy.findByEmail(e.email);if(!r||!await d(e.password,r.password))throw Error("Invalid email or password");return{token:u({userId:r.id,email:r.email}),user:{id:r.id,email:r.email,referralId:r.referralId,kycStatus:r.kycStatus}}},y=e=>/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(e),w=async e=>{let r=await i.Gy.findById(e);return r?.role==="ADMIN"}},27910:e=>{e.exports=require("stream")},28354:e=>{e.exports=require("util")},29294:e=>{e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},44870:e=>{e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},55511:e=>{e.exports=require("crypto")},63033:e=>{e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},77156:(e,r,t)=>{t.r(r),t.d(r,{patchFetch:()=>w,routeModule:()=>E,serverHooks:()=>y,workAsyncStorage:()=>g,workUnitAsyncStorage:()=>f});var a={};t.r(a),t.d(a,{DELETE:()=>m,GET:()=>u,PUT:()=>p});var s=t(96559),o=t(48088),i=t(37719),n=t(32190),l=t(12909),c=t(6710),d=t(82629);async function u(e,{params:r}){try{let{authenticated:t,user:a}=await (0,l.b9)(e);if(!t||!a)return n.NextResponse.json({success:!1,error:"Unauthorized"},{status:401});if(!await (0,l.qc)(a.id))return n.NextResponse.json({success:!1,error:"Admin access required"},{status:403});let s=await c.vo.findByName(r.name);if(!s)return n.NextResponse.json({success:!1,error:"Template not found"},{status:404});return n.NextResponse.json({success:!0,data:s})}catch(r){return console.error("Get email template error:",r),await d.v5.logApiError(e,r,"GET_EMAIL_TEMPLATE_ERROR"),n.NextResponse.json({success:!1,error:"Internal server error"},{status:500})}}async function p(e,{params:r}){try{let{authenticated:t,user:a}=await (0,l.b9)(e);if(!t||!a)return n.NextResponse.json({success:!1,error:"Unauthorized"},{status:401});if(!await (0,l.qc)(a.id))return n.NextResponse.json({success:!1,error:"Admin access required"},{status:403});let{subject:s,htmlContent:o,textContent:i,isActive:d}=await e.json();if(!s||!o)return n.NextResponse.json({success:!1,error:"Subject and HTML content are required"},{status:400});if(!await c.vo.findByName(r.name))return n.NextResponse.json({success:!1,error:"Template not found"},{status:404});let u=await c.vo.update(r.name,{subject:s,htmlContent:o,textContent:i,isActive:d});return n.NextResponse.json({success:!0,message:"Email template updated successfully",data:u})}catch(r){return console.error("Update email template error:",r),await d.v5.logApiError(e,r,"UPDATE_EMAIL_TEMPLATE_ERROR"),n.NextResponse.json({success:!1,error:"Internal server error"},{status:500})}}async function m(e,{params:r}){try{let{authenticated:t,user:a}=await (0,l.b9)(e);if(!t||!a)return n.NextResponse.json({success:!1,error:"Unauthorized"},{status:401});if(!await (0,l.qc)(a.id))return n.NextResponse.json({success:!1,error:"Admin access required"},{status:403});if(!await c.vo.findByName(r.name))return n.NextResponse.json({success:!1,error:"Template not found"},{status:404});return await c.vo.delete(r.name),n.NextResponse.json({success:!0,message:"Email template deleted successfully"})}catch(r){return console.error("Delete email template error:",r),await d.v5.logApiError(e,r,"DELETE_EMAIL_TEMPLATE_ERROR"),n.NextResponse.json({success:!1,error:"Internal server error"},{status:500})}}let E=new s.AppRouteRouteModule({definition:{kind:o.RouteKind.APP_ROUTE,page:"/api/admin/email-templates/[name]/route",pathname:"/api/admin/email-templates/[name]",filename:"route",bundlePath:"app/api/admin/email-templates/[name]/route"},resolvedPagePath:"C:\\Users\\<USER>\\Desktop\\Hash_Minings\\hashcorex\\src\\app\\api\\admin\\email-templates\\[name]\\route.ts",nextConfigOutput:"",userland:a}),{workAsyncStorage:g,workUnitAsyncStorage:f,serverHooks:y}=E;function w(){return(0,i.patchFetch)({workAsyncStorage:g,workUnitAsyncStorage:f})}},79428:e=>{e.exports=require("buffer")},82629:(e,r,t)=>{t.d(r,{v5:()=>s});var a=t(6710);class s{static async logError(e){try{let r=e.error instanceof Error?e.error:Error(String(e.error)),t={action:e.action,userId:e.userId,adminId:e.adminId,details:{message:r.message,stack:r.stack,name:r.name,requestUrl:e.requestUrl,requestMethod:e.requestMethod,requestBody:e.requestBody,additionalData:e.additionalData,timestamp:new Date().toISOString()},ipAddress:e.ipAddress,userAgent:e.userAgent};await a.AJ.create(t),console.error(`[${e.action}] Error logged:`,{message:r.message,stack:r.stack,userId:e.userId,adminId:e.adminId,url:e.requestUrl})}catch(r){console.error("Failed to log error to database:",r),console.error("Original error:",e.error)}}static async logApiError(e,r,t,a,s,o){try{let i=null;try{if("GET"!==e.method&&e.headers.get("content-type")?.includes("application/json")){let r=e.clone();(i=await r.json()).password&&(i.password="[REDACTED]"),i.token&&(i.token="[REDACTED]"),i.apiKey&&(i.apiKey="[REDACTED]")}}catch(e){}await this.logError({action:t,error:r,userId:a,adminId:s,ipAddress:e.headers.get("x-forwarded-for")||e.headers.get("x-real-ip")||"unknown",userAgent:e.headers.get("user-agent")||"unknown",requestUrl:e.url,requestMethod:e.method,requestBody:i,additionalData:o})}catch(e){console.error("Failed to log API error:",e),console.error("Original error:",r)}}static async logClientError(e){try{await a.AJ.create({action:"CLIENT_ERROR",userId:e.userId,details:{message:e.message,stack:e.stack,url:e.url,timestamp:e.timestamp,additionalData:e.additionalData},userAgent:e.userAgent})}catch(e){console.error("Failed to log client error:",e)}}static async logAuthError(e,r,t,a){await this.logApiError(e,r,"AUTH_ERROR",void 0,void 0,{email:t,...a})}static async logDatabaseError(e,r,t,s,o){try{await a.AJ.create({action:"DATABASE_ERROR",userId:s,details:{message:e instanceof Error?e.message:String(e),stack:e instanceof Error?e.stack:void 0,operation:r,table:t,timestamp:new Date().toISOString(),additionalData:o}})}catch(r){console.error("Failed to log database error:",r),console.error("Original error:",e)}}static async logBusinessError(e,r,t,a,s){await this.logError({action:"BUSINESS_LOGIC_ERROR",error:e,userId:t,adminId:a,additionalData:{operation:r,...s}})}static async logExternalApiError(e,r,t,a,s){await this.logError({action:"EXTERNAL_API_ERROR",error:e,userId:a,additionalData:{service:r,endpoint:t,...s}})}static async logValidationError(e,r,t,a,s){await this.logError({action:"VALIDATION_ERROR",error:e,userId:a,additionalData:{field:r,value:t,...s}})}}},96330:e=>{e.exports=require("@prisma/client")}};var r=require("../../../../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),a=r.X(0,[4447,580,5315,3306],()=>t(77156));module.exports=a})();