"use strict";(()=>{var e={};e.id=8225,e.ids=[8225],e.modules={3295:e=>{e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},12909:(e,t,r)=>{r.d(t,{DT:()=>I,DY:()=>y,HU:()=>c,Lx:()=>P,b9:()=>A,qc:()=>S});var a=r(85663),n=r(43205),i=r.n(n),s=r(6710);let o=process.env.JWT_SECRET||"fallback-secret-key",l=process.env.JWT_EXPIRES_IN||"30d",d=async e=>await a.Ay.hash(e,12),u=async(e,t)=>await a.Ay.compare(e,t),c=e=>i().sign(e,o,{expiresIn:l}),p=e=>{try{return i().verify(e,o)}catch(e){return null}},m=()=>{let e="ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789",t="HC";for(let r=0;r<8;r++)t+=e.charAt(Math.floor(Math.random()*e.length));return t},A=async e=>{let t=e.headers.get("authorization")?.replace("Bearer ","")||e.cookies.get("auth-token")?.value;if(!t)return{authenticated:!1,user:null};let r=p(t);if(!r)return{authenticated:!1,user:null};let a=await s.Gy.findByEmail(r.email);return a?{authenticated:!0,user:a}:{authenticated:!1,user:null}},y=async e=>{let t,a;if(await s.Gy.findByEmail(e.email))throw Error("User already exists with this email");if(e.referralCode){let r=await s.Gy.findByReferralId(e.referralCode);if(!r)throw Error("Invalid referral code");t=r.id}let n=await d(e.password),i=!1;do a=m(),i=!await s.Gy.findByReferralId(a);while(!i);let o=await s.Gy.create({email:e.email,firstName:e.firstName,lastName:e.lastName,password:n,referralId:a});if(t){let{placeUserByReferralType:a}=await r.e(2746).then(r.bind(r,2746)),n="general";"left"===e.placementSide?n="left":"right"===e.placementSide&&(n="right"),await a(t,o.id,n)}return{id:o.id,email:o.email,referralId:o.referralId,kycStatus:o.kycStatus}},P=async e=>{let t=await s.Gy.findByEmail(e.email);if(!t||!await u(e.password,t.password))throw Error("Invalid email or password");return{token:c({userId:t.id,email:t.email}),user:{id:t.id,email:t.email,referralId:t.referralId,kycStatus:t.kycStatus}}},I=e=>/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(e),S=async e=>{let t=await s.Gy.findById(e);return t?.role==="ADMIN"}},27910:e=>{e.exports=require("stream")},28354:e=>{e.exports=require("util")},29294:e=>{e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},44870:e=>{e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},55511:e=>{e.exports=require("crypto")},60864:(e,t,r)=>{r.r(t),r.d(t,{patchFetch:()=>P,routeModule:()=>p,serverHooks:()=>y,workAsyncStorage:()=>m,workUnitAsyncStorage:()=>A});var a={};r.r(a),r.d(a,{GET:()=>u,PUT:()=>c});var n=r(96559),i=r(48088),s=r(37719),o=r(32190),l=r(12909),d=r(6710);async function u(e){try{let{authenticated:t,user:r}=await (0,l.b9)(e);if(!t||!r)return o.NextResponse.json({error:"Not authenticated"},{status:401});if(!await (0,l.qc)(r.id))return o.NextResponse.json({error:"Admin access required"},{status:403});let a=await d.rs.getAll(),n={};a.forEach(e=>{try{n[e.key]=JSON.parse(e.value)}catch{n[e.key]=e.value}}),n.MAX_BINARY_POINTS_PER_SIDE&&(n.maxBinaryPointsPerSide=parseFloat(n.MAX_BINARY_POINTS_PER_SIDE),console.log(`Mapped MAX_BINARY_POINTS_PER_SIDE: ${n.MAX_BINARY_POINTS_PER_SIDE} → maxBinaryPointsPerSide: ${n.maxBinaryPointsPerSide}`),delete n.MAX_BINARY_POINTS_PER_SIDE),n.BINARY_POINT_VALUE&&(n.binaryPointValue=parseFloat(n.BINARY_POINT_VALUE),delete n.BINARY_POINT_VALUE),n.BINARY_MATCHING_ENABLED&&(n.binaryMatchingEnabled="true"===n.BINARY_MATCHING_ENABLED,delete n.BINARY_MATCHING_ENABLED),n.BINARY_MATCHING_SCHEDULE&&(n.binaryMatchingSchedule=n.BINARY_MATCHING_SCHEDULE,delete n.BINARY_MATCHING_SCHEDULE),n.THS_PRICE&&(n.thsPriceUSD=parseFloat(n.THS_PRICE),delete n.THS_PRICE),n.MINIMUM_PURCHASE&&(n.minPurchaseAmount=parseFloat(n.MINIMUM_PURCHASE),delete n.MINIMUM_PURCHASE),n.MAXIMUM_PURCHASE&&(n.maxPurchaseAmount=parseFloat(n.MAXIMUM_PURCHASE),delete n.MAXIMUM_PURCHASE),console.log("Settings object after mapping:",{maxBinaryPointsPerSide:n.maxBinaryPointsPerSide});let i={thsPriceUSD:50,minPurchaseAmount:100,maxPurchaseAmount:1e4,earningsRanges:[{minTHS:0,maxTHS:10,dailyReturnMin:.3,dailyReturnMax:.5,monthlyReturnMin:10,monthlyReturnMax:15},{minTHS:10,maxTHS:50,dailyReturnMin:.4,dailyReturnMax:.6,monthlyReturnMin:10,monthlyReturnMax:15},{minTHS:50,maxTHS:999999,dailyReturnMin:.5,dailyReturnMax:.7,monthlyReturnMin:10,monthlyReturnMax:15}],binaryBonusPercentage:10,referralBonusPercentage:5,maxBinaryPointsPerSide:10,binaryPointValue:10,binaryMatchingEnabled:!0,binaryMatchingSchedule:"Weekly at 15:00 UTC",usdtDepositAddress:"",minDepositAmount:10,maxDepositAmount:1e4,depositEnabled:!0,minConfirmations:1,depositFeePercentage:0,tronNetwork:"testnet",tronMainnetApiUrl:"https://api.trongrid.io",tronTestnetApiUrl:"https://api.shasta.trongrid.io",usdtMainnetContract:"TR7NHqjeKQxGTCi8q8ZY4pL8otSzgjLj6t",usdtTestnetContract:"TG3XXyExBkPp9nzdajDZsozEu4BkaSJozs",minWithdrawalAmount:50,withdrawalFeeFixed:3,withdrawalFeePercentage:1,withdrawalProcessingDays:3,platformFeePercentage:1,maintenanceMode:!1,registrationEnabled:!0,kycRequired:!0,...n};return o.NextResponse.json({success:!0,data:i})}catch(e){return console.error("Admin settings fetch error:",e),o.NextResponse.json({success:!1,error:"Failed to fetch settings"},{status:500})}}async function c(e){try{let{authenticated:t,user:r}=await (0,l.b9)(e);if(!t||!r)return o.NextResponse.json({error:"Not authenticated"},{status:401});if(!await (0,l.qc)(r.id))return o.NextResponse.json({error:"Admin access required"},{status:403});let a=await e.json();console.log("Received settings for update:",a),console.log("Received maxBinaryPointsPerSide:",a.maxBinaryPointsPerSide,typeof a.maxBinaryPointsPerSide);let n={...a};["MAX_BINARY_POINTS_PER_SIDE","BINARY_POINT_VALUE","BINARY_MATCHING_ENABLED","BINARY_MATCHING_SCHEDULE","THS_PRICE","MINIMUM_PURCHASE","MAXIMUM_PURCHASE"].forEach(e=>{n[e]&&(console.log(`Removing conflicting database key: ${e} = ${n[e]}`),delete n[e])}),console.log("Cleaned settings for processing:",{maxBinaryPointsPerSide:n.maxBinaryPointsPerSide,binaryPointValue:n.binaryPointValue,binaryMatchingEnabled:n.binaryMatchingEnabled,binaryMatchingSchedule:n.binaryMatchingSchedule});let i={maxBinaryPointsPerSide:"MAX_BINARY_POINTS_PER_SIDE",binaryPointValue:"BINARY_POINT_VALUE",binaryMatchingEnabled:"BINARY_MATCHING_ENABLED",binaryMatchingSchedule:"BINARY_MATCHING_SCHEDULE",thsPriceUSD:"THS_PRICE",minPurchaseAmount:"MINIMUM_PURCHASE",maxPurchaseAmount:"MAXIMUM_PURCHASE"},s=Object.entries(n).map(async([e,t])=>{let a=i[e]||e;if(!i[e])return d.rs.set(a,JSON.stringify(t),r.id);{console.log(`Mapping ${e} (${t}) → ${a} (${String(t)})`);let n=await d.rs.set(a,String(t),r.id);return console.log(`Database update result for ${a}:`,n),n}});return await Promise.all(s),console.log("All settings updates completed"),await d.AJ.create({action:"SYSTEM_SETTINGS_UPDATED",userId:r.id,details:{updatedSettings:Object.keys(n)},ipAddress:e.headers.get("x-forwarded-for")||"unknown",userAgent:e.headers.get("user-agent")||"unknown"}),o.NextResponse.json({success:!0,message:"Settings updated successfully"})}catch(e){return console.error("Admin settings update error:",e),o.NextResponse.json({success:!1,error:"Failed to update settings"},{status:500})}}let p=new n.AppRouteRouteModule({definition:{kind:i.RouteKind.APP_ROUTE,page:"/api/admin/settings/route",pathname:"/api/admin/settings",filename:"route",bundlePath:"app/api/admin/settings/route"},resolvedPagePath:"C:\\Users\\<USER>\\Desktop\\Hash_Minings\\hashcorex\\src\\app\\api\\admin\\settings\\route.ts",nextConfigOutput:"",userland:a}),{workAsyncStorage:m,workUnitAsyncStorage:A,serverHooks:y}=p;function P(){return(0,s.patchFetch)({workAsyncStorage:m,workUnitAsyncStorage:A})}},63033:e=>{e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},79428:e=>{e.exports=require("buffer")},96330:e=>{e.exports=require("@prisma/client")}};var t=require("../../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),a=t.X(0,[4447,580,5315,3306],()=>r(60864));module.exports=a})();