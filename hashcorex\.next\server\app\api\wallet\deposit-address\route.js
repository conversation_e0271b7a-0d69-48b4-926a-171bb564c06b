"use strict";(()=>{var e={};e.id=809,e.ids=[809],e.modules={3295:e=>{e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},12909:(e,r,t)=>{t.d(r,{DT:()=>h,DY:()=>m,HU:()=>p,Lx:()=>w,b9:()=>y,qc:()=>x});var a=t(85663),s=t(43205),i=t.n(s),n=t(6710);let o=process.env.JWT_SECRET||"fallback-secret-key",d=process.env.JWT_EXPIRES_IN||"30d",l=async e=>await a.Ay.hash(e,12),u=async(e,r)=>await a.Ay.compare(e,r),p=e=>i().sign(e,o,{expiresIn:d}),c=e=>{try{return i().verify(e,o)}catch(e){return null}},f=()=>{let e="ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789",r="HC";for(let t=0;t<8;t++)r+=e.charAt(Math.floor(Math.random()*e.length));return r},y=async e=>{let r=e.headers.get("authorization")?.replace("Bearer ","")||e.cookies.get("auth-token")?.value;if(!r)return{authenticated:!1,user:null};let t=c(r);if(!t)return{authenticated:!1,user:null};let a=await n.Gy.findByEmail(t.email);return a?{authenticated:!0,user:a}:{authenticated:!1,user:null}},m=async e=>{let r,a;if(await n.Gy.findByEmail(e.email))throw Error("User already exists with this email");if(e.referralCode){let t=await n.Gy.findByReferralId(e.referralCode);if(!t)throw Error("Invalid referral code");r=t.id}let s=await l(e.password),i=!1;do a=f(),i=!await n.Gy.findByReferralId(a);while(!i);let o=await n.Gy.create({email:e.email,firstName:e.firstName,lastName:e.lastName,password:s,referralId:a});if(r){let{placeUserByReferralType:a}=await t.e(2746).then(t.bind(t,2746)),s="general";"left"===e.placementSide?s="left":"right"===e.placementSide&&(s="right"),await a(r,o.id,s)}return{id:o.id,email:o.email,referralId:o.referralId,kycStatus:o.kycStatus}},w=async e=>{let r=await n.Gy.findByEmail(e.email);if(!r||!await u(e.password,r.password))throw Error("Invalid email or password");return{token:p({userId:r.id,email:r.email}),user:{id:r.id,email:r.email,referralId:r.referralId,kycStatus:r.kycStatus}}},h=e=>/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(e),x=async e=>{let r=await n.Gy.findById(e);return r?.role==="ADMIN"}},27910:e=>{e.exports=require("stream")},28354:e=>{e.exports=require("util")},29294:e=>{e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},44870:e=>{e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},55511:e=>{e.exports=require("crypto")},63033:e=>{e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},71371:(e,r,t)=>{t.r(r),t.d(r,{patchFetch:()=>m,routeModule:()=>p,serverHooks:()=>y,workAsyncStorage:()=>c,workUnitAsyncStorage:()=>f});var a={};t.r(a),t.d(a,{GET:()=>u});var s=t(96559),i=t(48088),n=t(37719),o=t(32190),d=t(12909),l=t(6710);async function u(e){try{let{authenticated:r,user:t}=await (0,d.b9)(e);if(!r||!t)return o.NextResponse.json({error:"Not authenticated"},{status:401});let a=await l.rs.get("usdtDepositAddress");if(a||(a=await l.rs.get("USDT_DEPOSIT_ADDRESS")),a&&(a=a.replace(/['"]/g,"").trim()),!a)return o.NextResponse.json({success:!1,error:"Deposit address not configured. Please contact support."},{status:503});return o.NextResponse.json({success:!0,data:{address:a,network:"TRC20",currency:"USDT"}})}catch(e){return console.error("Deposit address error:",e),o.NextResponse.json({success:!1,error:"Failed to get deposit address"},{status:500})}}let p=new s.AppRouteRouteModule({definition:{kind:i.RouteKind.APP_ROUTE,page:"/api/wallet/deposit-address/route",pathname:"/api/wallet/deposit-address",filename:"route",bundlePath:"app/api/wallet/deposit-address/route"},resolvedPagePath:"C:\\Users\\<USER>\\Desktop\\Hash_Minings\\hashcorex\\src\\app\\api\\wallet\\deposit-address\\route.ts",nextConfigOutput:"",userland:a}),{workAsyncStorage:c,workUnitAsyncStorage:f,serverHooks:y}=p;function m(){return(0,n.patchFetch)({workAsyncStorage:c,workUnitAsyncStorage:f})}},79428:e=>{e.exports=require("buffer")},96330:e=>{e.exports=require("@prisma/client")}};var r=require("../../../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),a=r.X(0,[4447,580,5315,3306],()=>t(71371));module.exports=a})();