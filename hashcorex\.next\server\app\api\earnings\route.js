"use strict";(()=>{var e={};e.id=3555,e.ids=[3555],e.modules={3295:e=>{e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},12909:(e,t,r)=>{r.d(t,{DT:()=>y,DY:()=>g,HU:()=>c,Lx:()=>E,b9:()=>f,qc:()=>w});var a=r(85663),n=r(43205),i=r.n(n),s=r(6710);let o=process.env.JWT_SECRET||"fallback-secret-key",l=process.env.JWT_EXPIRES_IN||"30d",u=async e=>await a.Ay.hash(e,12),d=async(e,t)=>await a.Ay.compare(e,t),c=e=>i().sign(e,o,{expiresIn:l}),m=e=>{try{return i().verify(e,o)}catch(e){return null}},p=()=>{let e="ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789",t="HC";for(let r=0;r<8;r++)t+=e.charAt(Math.floor(Math.random()*e.length));return t},f=async e=>{let t=e.headers.get("authorization")?.replace("Bearer ","")||e.cookies.get("auth-token")?.value;if(!t)return{authenticated:!1,user:null};let r=m(t);if(!r)return{authenticated:!1,user:null};let a=await s.Gy.findByEmail(r.email);return a?{authenticated:!0,user:a}:{authenticated:!1,user:null}},g=async e=>{let t,a;if(await s.Gy.findByEmail(e.email))throw Error("User already exists with this email");if(e.referralCode){let r=await s.Gy.findByReferralId(e.referralCode);if(!r)throw Error("Invalid referral code");t=r.id}let n=await u(e.password),i=!1;do a=p(),i=!await s.Gy.findByReferralId(a);while(!i);let o=await s.Gy.create({email:e.email,firstName:e.firstName,lastName:e.lastName,password:n,referralId:a});if(t){let{placeUserByReferralType:a}=await r.e(2746).then(r.bind(r,2746)),n="general";"left"===e.placementSide?n="left":"right"===e.placementSide&&(n="right"),await a(t,o.id,n)}return{id:o.id,email:o.email,referralId:o.referralId,kycStatus:o.kycStatus}},E=async e=>{let t=await s.Gy.findByEmail(e.email);if(!t||!await d(e.password,t.password))throw Error("Invalid email or password");return{token:c({userId:t.id,email:t.email}),user:{id:t.id,email:t.email,referralId:t.referralId,kycStatus:t.kycStatus}}},y=e=>/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(e),w=async e=>{let t=await s.Gy.findById(e);return t?.role==="ADMIN"}},27910:e=>{e.exports=require("stream")},28354:e=>{e.exports=require("util")},29294:e=>{e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},39794:(e,t,r)=>{r.d(t,{Py:()=>s,k8:()=>u,kp:()=>l});var a=r(31183),n=r(6710);async function i(e){return await a.prisma.miningUnit.findMany({where:{userId:e,status:"ACTIVE",expiryDate:{gt:new Date}},orderBy:{createdAt:"asc"}})}async function s(e,t,r,s,l){let u=await i(e);if(0===u.length)throw Error("No active mining units found for earnings allocation");let d=[],c=t;for(let e of u){var m;if(c<=0)break;let t=Math.max(0,5*(m=e).investmentAmount-(m.miningEarnings+m.referralEarnings+m.binaryEarnings));if(t<=0)continue;let n=Math.min(c,t);if(n>0){let i={};switch(r){case"MINING_EARNINGS":i.miningEarnings={increment:n};break;case"DIRECT_REFERRAL":i.referralEarnings={increment:n};break;case"BINARY_BONUS":i.binaryEarnings={increment:n}}i.totalEarned={increment:n},await a.prisma.miningUnit.update({where:{id:e.id},data:i}),await a.prisma.miningUnitEarningsAllocation.create({data:{miningUnitId:e.id,transactionId:s,earningType:r,amount:n,description:l}}),d.push({miningUnitId:e.id,amount:n,remainingCapacity:t-n}),c-=n;let u=await a.prisma.miningUnit.findUnique({where:{id:e.id}});u&&function(e){let t=5*e.investmentAmount;return e.miningEarnings+e.referralEarnings+e.binaryEarnings>=t}(u)&&await o(e.id,"5x_investment_reached")}}return c>0&&(console.warn(`Unable to allocate ${c} to mining units - all units at capacity`),await n.AJ.create({action:"EARNINGS_ALLOCATION_OVERFLOW",userId:e,details:{totalAmount:t,allocatedAmount:t-c,overflowAmount:c,earningType:r,reason:"all_units_at_capacity"}})),d}async function o(e,t){let r=await a.prisma.miningUnit.findUnique({where:{id:e}});if(!r)throw Error(`Mining unit ${e} not found`);await a.prisma.miningUnit.update({where:{id:e},data:{status:"EXPIRED"}});let i=r.miningEarnings+r.referralEarnings+r.binaryEarnings;await n.AJ.create({action:"MINING_UNIT_EXPIRED",userId:r.userId,details:{miningUnitId:e,reason:t,totalEarned:i,miningEarnings:r.miningEarnings,referralEarnings:r.referralEarnings,binaryEarnings:r.binaryEarnings,investmentAmount:r.investmentAmount,multiplier:i/r.investmentAmount}}),console.log(`Mining unit ${e} expired due to ${t}. Total earnings: ${i}`)}async function l(e){return await a.prisma.miningUnit.findMany({where:{userId:e},orderBy:{createdAt:"asc"}})}async function u(e){return await a.prisma.miningUnitEarningsAllocation.findMany({where:{miningUnitId:e},include:{transaction:!0},orderBy:{allocatedAt:"desc"}})}},44870:e=>{e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},55511:e=>{e.exports=require("crypto")},63033:e=>{e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},71299:(e,t,r)=>{r.r(t),r.d(t,{patchFetch:()=>E,routeModule:()=>m,serverHooks:()=>g,workAsyncStorage:()=>p,workUnitAsyncStorage:()=>f});var a={};r.r(a),r.d(a,{GET:()=>c});var n=r(96559),i=r(48088),s=r(37719),o=r(32190),l=r(12909),u=r(6710),d=r(92731);async function c(e){try{let{authenticated:t,user:r}=await (0,l.b9)(e);if(!t||!r)return o.NextResponse.json({success:!1,error:"Not authenticated"},{status:401});let a=(await u.DR.findByUserId(r.id,100)).filter(e=>"MINING_EARNINGS"===e.type||"DIRECT_REFERRAL"===e.type||"BINARY_BONUS"===e.type),n=a.filter(e=>"COMPLETED"===e.status).reduce((e,t)=>e+t.amount,0),i=a.filter(e=>"PENDING"===e.status).reduce((e,t)=>e+t.amount,0),s=a.filter(e=>"MINING_EARNINGS"===e.type&&"COMPLETED"===e.status).reduce((e,t)=>e+t.amount,0),c=a.filter(e=>("DIRECT_REFERRAL"===e.type||"BINARY_BONUS"===e.type)&&"COMPLETED"===e.status).reduce((e,t)=>e+t.amount,0),m=await (0,d.IT)(r.id),p=new Date;p.setDate(p.getDate()-30);let f=a.filter(e=>new Date(e.createdAt)>=p&&"COMPLETED"===e.status);return o.NextResponse.json({success:!0,data:{totalEarnings:n,pendingEarnings:i,miningEarnings:s,referralEarnings:c,estimatedEarnings:m,recentEarnings:f.map(e=>({id:e.id,type:e.type,amount:e.amount,description:e.description,createdAt:e.createdAt})),earningsBreakdown:{mining:s,directReferral:a.filter(e=>"DIRECT_REFERRAL"===e.type&&"COMPLETED"===e.status).reduce((e,t)=>e+t.amount,0),binaryBonus:a.filter(e=>"BINARY_BONUS"===e.type&&"COMPLETED"===e.status).reduce((e,t)=>e+t.amount,0)}}})}catch(e){return console.error("Earnings fetch error:",e),o.NextResponse.json({success:!1,error:"Failed to fetch earnings data"},{status:500})}}let m=new n.AppRouteRouteModule({definition:{kind:i.RouteKind.APP_ROUTE,page:"/api/earnings/route",pathname:"/api/earnings",filename:"route",bundlePath:"app/api/earnings/route"},resolvedPagePath:"C:\\Users\\<USER>\\Desktop\\Hash_Minings\\hashcorex\\src\\app\\api\\earnings\\route.ts",nextConfigOutput:"",userland:a}),{workAsyncStorage:p,workUnitAsyncStorage:f,serverHooks:g}=m;function E(){return(0,s.patchFetch)({workAsyncStorage:p,workUnitAsyncStorage:f})}},79428:e=>{e.exports=require("buffer")},96330:e=>{e.exports=require("@prisma/client")}};var t=require("../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),a=t.X(0,[4447,580,5315,3306,5112],()=>r(71299));module.exports=a})();