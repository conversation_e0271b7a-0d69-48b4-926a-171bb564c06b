"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/admin/page",{

/***/ "(app-pages-browser)/./src/components/admin/EmailSettings.tsx":
/*!************************************************!*\
  !*** ./src/components/admin/EmailSettings.tsx ***!
  \************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   EmailSettings: () => (/* binding */ EmailSettings)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_ui__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui */ \"(app-pages-browser)/./src/components/ui/index.ts\");\n/* harmony import */ var _barrel_optimize_names_Edit_Eye_EyeOff_FileText_Mail_Plus_Send_Settings_Shield_TestTube_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=Edit,Eye,EyeOff,FileText,Mail,Plus,Send,Settings,Shield,TestTube,Trash2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/settings.js\");\n/* harmony import */ var _barrel_optimize_names_Edit_Eye_EyeOff_FileText_Mail_Plus_Send_Settings_Shield_TestTube_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=Edit,Eye,EyeOff,FileText,Mail,Plus,Send,Settings,Shield,TestTube,Trash2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/file-text.js\");\n/* harmony import */ var _barrel_optimize_names_Edit_Eye_EyeOff_FileText_Mail_Plus_Send_Settings_Shield_TestTube_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=Edit,Eye,EyeOff,FileText,Mail,Plus,Send,Settings,Shield,TestTube,Trash2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/eye-off.js\");\n/* harmony import */ var _barrel_optimize_names_Edit_Eye_EyeOff_FileText_Mail_Plus_Send_Settings_Shield_TestTube_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=Edit,Eye,EyeOff,FileText,Mail,Plus,Send,Settings,Shield,TestTube,Trash2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/eye.js\");\n/* harmony import */ var _barrel_optimize_names_Edit_Eye_EyeOff_FileText_Mail_Plus_Send_Settings_Shield_TestTube_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=Edit,Eye,EyeOff,FileText,Mail,Plus,Send,Settings,Shield,TestTube,Trash2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/mail.js\");\n/* harmony import */ var _barrel_optimize_names_Edit_Eye_EyeOff_FileText_Mail_Plus_Send_Settings_Shield_TestTube_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=Edit,Eye,EyeOff,FileText,Mail,Plus,Send,Settings,Shield,TestTube,Trash2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/test-tube.js\");\n/* harmony import */ var _barrel_optimize_names_Edit_Eye_EyeOff_FileText_Mail_Plus_Send_Settings_Shield_TestTube_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=Edit,Eye,EyeOff,FileText,Mail,Plus,Send,Settings,Shield,TestTube,Trash2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/send.js\");\n/* harmony import */ var _barrel_optimize_names_Edit_Eye_EyeOff_FileText_Mail_Plus_Send_Settings_Shield_TestTube_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=Edit,Eye,EyeOff,FileText,Mail,Plus,Send,Settings,Shield,TestTube,Trash2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/shield.js\");\n/* harmony import */ var _barrel_optimize_names_Edit_Eye_EyeOff_FileText_Mail_Plus_Send_Settings_Shield_TestTube_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=Edit,Eye,EyeOff,FileText,Mail,Plus,Send,Settings,Shield,TestTube,Trash2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/plus.js\");\n/* harmony import */ var _barrel_optimize_names_Edit_Eye_EyeOff_FileText_Mail_Plus_Send_Settings_Shield_TestTube_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=Edit,Eye,EyeOff,FileText,Mail,Plus,Send,Settings,Shield,TestTube,Trash2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/square-pen.js\");\n/* harmony import */ var _barrel_optimize_names_Edit_Eye_EyeOff_FileText_Mail_Plus_Send_Settings_Shield_TestTube_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=Edit,Eye,EyeOff,FileText,Mail,Plus,Send,Settings,Shield,TestTube,Trash2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/trash-2.js\");\n/* harmony import */ var _EmailTemplateModal__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./EmailTemplateModal */ \"(app-pages-browser)/./src/components/admin/EmailTemplateModal.tsx\");\n/* __next_internal_client_entry_do_not_use__ EmailSettings auto */ \nvar _s = $RefreshSig$();\n\n\n\n\nconst EmailSettings = ()=>{\n    _s();\n    const [activeTab, setActiveTab] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('settings');\n    const [settings, setSettings] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        smtpHost: '',\n        smtpPort: 587,\n        smtpSecure: false,\n        smtpUser: '',\n        smtpPassword: '',\n        fromName: 'HashCoreX',\n        fromEmail: '',\n        emailEnabled: true\n    });\n    const [templates, setTemplates] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [saving, setSaving] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [testing, setTesting] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [seedingTemplates, setSeedingTemplates] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [showPassword, setShowPassword] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [testEmail, setTestEmail] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [editingTemplate, setEditingTemplate] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [showTemplateModal, setShowTemplateModal] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const { showMessage, MessageBoxComponent } = (0,_components_ui__WEBPACK_IMPORTED_MODULE_2__.useMessageBox)();\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"EmailSettings.useEffect\": ()=>{\n            fetchEmailSettings();\n            if (activeTab === 'templates') {\n                fetchEmailTemplates();\n            }\n        }\n    }[\"EmailSettings.useEffect\"], [\n        activeTab\n    ]);\n    const fetchEmailSettings = async ()=>{\n        try {\n            setLoading(true);\n            const response = await fetch('/api/admin/email-settings', {\n                credentials: 'include'\n            });\n            if (response.ok) {\n                const data = await response.json();\n                if (data.success) {\n                    // Ensure all values are properly initialized to prevent uncontrolled input errors\n                    setSettings({\n                        smtpHost: data.data.smtpHost || '',\n                        smtpPort: data.data.smtpPort || 587,\n                        smtpSecure: Boolean(data.data.smtpSecure),\n                        smtpUser: data.data.smtpUser || '',\n                        smtpPassword: data.data.smtpPassword || '',\n                        fromName: data.data.fromName || 'HashCoreX',\n                        fromEmail: data.data.fromEmail || '',\n                        emailEnabled: Boolean(data.data.emailEnabled)\n                    });\n                }\n            }\n        } catch (error) {\n            console.error('Failed to fetch email settings:', error);\n            showMessage({\n                title: 'Error',\n                message: 'Failed to load email settings',\n                variant: 'error'\n            });\n        } finally{\n            setLoading(false);\n        }\n    };\n    const handleSave = async ()=>{\n        try {\n            setSaving(true);\n            const response = await fetch('/api/admin/email-settings', {\n                method: 'PUT',\n                headers: {\n                    'Content-Type': 'application/json'\n                },\n                credentials: 'include',\n                body: JSON.stringify(settings)\n            });\n            const data = await response.json();\n            if (data.success) {\n                showMessage({\n                    title: 'Success',\n                    message: 'Email settings saved successfully',\n                    variant: 'success'\n                });\n            } else {\n                throw new Error(data.error || 'Failed to save settings');\n            }\n        } catch (error) {\n            console.error('Failed to save email settings:', error);\n            showMessage({\n                title: 'Error',\n                message: error instanceof Error ? error.message : 'Failed to save email settings',\n                variant: 'error'\n            });\n        } finally{\n            setSaving(false);\n        }\n    };\n    const handleTestEmail = async ()=>{\n        if (!testEmail) {\n            showMessage({\n                title: 'Error',\n                message: 'Please enter a test email address',\n                variant: 'error'\n            });\n            return;\n        }\n        try {\n            setTesting(true);\n            const response = await fetch('/api/admin/email-settings', {\n                method: 'POST',\n                headers: {\n                    'Content-Type': 'application/json'\n                },\n                credentials: 'include',\n                body: JSON.stringify({\n                    testEmail\n                })\n            });\n            const data = await response.json();\n            if (data.success) {\n                showMessage({\n                    title: 'Success',\n                    message: 'Test email sent successfully! Check your inbox.',\n                    variant: 'success'\n                });\n            } else {\n                throw new Error(data.error || 'Failed to send test email');\n            }\n        } catch (error) {\n            console.error('Failed to send test email:', error);\n            showMessage({\n                title: 'Error',\n                message: error instanceof Error ? error.message : 'Failed to send test email',\n                variant: 'error'\n            });\n        } finally{\n            setTesting(false);\n        }\n    };\n    const handleInputChange = (field, value)=>{\n        setSettings((prev)=>({\n                ...prev,\n                [field]: value\n            }));\n    };\n    // Template management functions\n    const fetchEmailTemplates = async ()=>{\n        try {\n            const response = await fetch('/api/admin/email-templates', {\n                credentials: 'include'\n            });\n            const data = await response.json();\n            if (data.success) {\n                setTemplates(data.data);\n            } else {\n                throw new Error(data.error || 'Failed to fetch templates');\n            }\n        } catch (error) {\n            console.error('Failed to fetch email templates:', error);\n            showMessage({\n                title: 'Error',\n                message: 'Failed to fetch email templates',\n                variant: 'error'\n            });\n        }\n    };\n    const handleCreateTemplate = ()=>{\n        setEditingTemplate(null);\n        setShowTemplateModal(true);\n    };\n    const handleEditTemplate = (template)=>{\n        setEditingTemplate(template);\n        setShowTemplateModal(true);\n    };\n    const handleDeleteTemplate = async (templateName)=>{\n        if (!confirm('Are you sure you want to delete this template?')) {\n            return;\n        }\n        try {\n            const response = await fetch(\"/api/admin/email-templates/\".concat(templateName), {\n                method: 'DELETE',\n                credentials: 'include'\n            });\n            const data = await response.json();\n            if (data.success) {\n                showMessage({\n                    title: 'Success',\n                    message: 'Template deleted successfully',\n                    variant: 'success'\n                });\n                fetchEmailTemplates();\n            } else {\n                throw new Error(data.error || 'Failed to delete template');\n            }\n        } catch (error) {\n            console.error('Failed to delete template:', error);\n            showMessage({\n                title: 'Error',\n                message: error instanceof Error ? error.message : 'Failed to delete template',\n                variant: 'error'\n            });\n        }\n    };\n    if (loading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex items-center justify-center h-64\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\admin\\\\EmailSettings.tsx\",\n                lineNumber: 255,\n                columnNumber: 9\n            }, undefined)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\admin\\\\EmailSettings.tsx\",\n            lineNumber: 254,\n            columnNumber: 7\n        }, undefined);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"space-y-6\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(MessageBoxComponent, {}, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\admin\\\\EmailSettings.tsx\",\n                lineNumber: 262,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center justify-between\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                className: \"text-2xl font-bold text-white\",\n                                children: \"Email Settings\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\admin\\\\EmailSettings.tsx\",\n                                lineNumber: 267,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-slate-400 mt-1\",\n                                children: \"Configure SMTP settings and email templates\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\admin\\\\EmailSettings.tsx\",\n                                lineNumber: 268,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\admin\\\\EmailSettings.tsx\",\n                        lineNumber: 266,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center space-x-3\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                            className: \"flex items-center space-x-2\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                    type: \"checkbox\",\n                                    checked: settings.emailEnabled,\n                                    onChange: (e)=>handleInputChange('emailEnabled', e.target.checked),\n                                    className: \"rounded border-slate-600 text-blue-600 focus:ring-blue-500\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\admin\\\\EmailSettings.tsx\",\n                                    lineNumber: 272,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"text-sm text-slate-300\",\n                                    children: \"Email Enabled\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\admin\\\\EmailSettings.tsx\",\n                                    lineNumber: 278,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\admin\\\\EmailSettings.tsx\",\n                            lineNumber: 271,\n                            columnNumber: 11\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\admin\\\\EmailSettings.tsx\",\n                        lineNumber: 270,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\admin\\\\EmailSettings.tsx\",\n                lineNumber: 265,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex space-x-1 bg-slate-800 p-1 rounded-lg\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        onClick: ()=>setActiveTab('settings'),\n                        className: \"flex items-center px-4 py-2 rounded-md text-sm font-medium transition-colors \".concat(activeTab === 'settings' ? 'bg-blue-600 text-white' : 'text-slate-400 hover:text-white hover:bg-slate-700'),\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Edit_Eye_EyeOff_FileText_Mail_Plus_Send_Settings_Shield_TestTube_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                className: \"w-4 h-4 mr-2\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\admin\\\\EmailSettings.tsx\",\n                                lineNumber: 293,\n                                columnNumber: 11\n                            }, undefined),\n                            \"SMTP Settings\"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\admin\\\\EmailSettings.tsx\",\n                        lineNumber: 285,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        onClick: ()=>setActiveTab('templates'),\n                        className: \"flex items-center px-4 py-2 rounded-md text-sm font-medium transition-colors \".concat(activeTab === 'templates' ? 'bg-blue-600 text-white' : 'text-slate-400 hover:text-white hover:bg-slate-700'),\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Edit_Eye_EyeOff_FileText_Mail_Plus_Send_Settings_Shield_TestTube_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                className: \"w-4 h-4 mr-2\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\admin\\\\EmailSettings.tsx\",\n                                lineNumber: 304,\n                                columnNumber: 11\n                            }, undefined),\n                            \"Email Templates\"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\admin\\\\EmailSettings.tsx\",\n                        lineNumber: 296,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\admin\\\\EmailSettings.tsx\",\n                lineNumber: 284,\n                columnNumber: 7\n            }, undefined),\n            activeTab === 'settings' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                        className: \"bg-slate-800 border-slate-700\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_2__.CardHeader, {\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_2__.CardTitle, {\n                                    className: \"flex items-center gap-2 text-white\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Edit_Eye_EyeOff_FileText_Mail_Plus_Send_Settings_Shield_TestTube_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                            className: \"h-5 w-5\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\admin\\\\EmailSettings.tsx\",\n                                            lineNumber: 315,\n                                            columnNumber: 13\n                                        }, undefined),\n                                        \"SMTP Configuration\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\admin\\\\EmailSettings.tsx\",\n                                    lineNumber: 314,\n                                    columnNumber: 11\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\admin\\\\EmailSettings.tsx\",\n                                lineNumber: 313,\n                                columnNumber: 9\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                                className: \"space-y-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"grid grid-cols-1 md:grid-cols-2 gap-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                        className: \"block text-sm font-medium text-slate-300 mb-2\",\n                                                        children: \"SMTP Host *\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\admin\\\\EmailSettings.tsx\",\n                                                        lineNumber: 322,\n                                                        columnNumber: 15\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_2__.Input, {\n                                                        type: \"text\",\n                                                        value: settings.smtpHost,\n                                                        onChange: (e)=>handleInputChange('smtpHost', e.target.value),\n                                                        placeholder: \"smtp.gmail.com\",\n                                                        className: \"bg-slate-700 border-slate-600 text-white\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\admin\\\\EmailSettings.tsx\",\n                                                        lineNumber: 325,\n                                                        columnNumber: 15\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\admin\\\\EmailSettings.tsx\",\n                                                lineNumber: 321,\n                                                columnNumber: 13\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                        className: \"block text-sm font-medium text-slate-300 mb-2\",\n                                                        children: \"SMTP Port\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\admin\\\\EmailSettings.tsx\",\n                                                        lineNumber: 334,\n                                                        columnNumber: 15\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_2__.Input, {\n                                                        type: \"number\",\n                                                        value: settings.smtpPort,\n                                                        onChange: (e)=>handleInputChange('smtpPort', parseInt(e.target.value)),\n                                                        placeholder: \"587\",\n                                                        className: \"bg-slate-700 border-slate-600 text-white\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\admin\\\\EmailSettings.tsx\",\n                                                        lineNumber: 337,\n                                                        columnNumber: 15\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\admin\\\\EmailSettings.tsx\",\n                                                lineNumber: 333,\n                                                columnNumber: 13\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\admin\\\\EmailSettings.tsx\",\n                                        lineNumber: 320,\n                                        columnNumber: 11\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"grid grid-cols-1 md:grid-cols-2 gap-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                        className: \"block text-sm font-medium text-slate-300 mb-2\",\n                                                        children: \"SMTP User *\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\admin\\\\EmailSettings.tsx\",\n                                                        lineNumber: 349,\n                                                        columnNumber: 15\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_2__.Input, {\n                                                        type: \"text\",\n                                                        value: settings.smtpUser,\n                                                        onChange: (e)=>handleInputChange('smtpUser', e.target.value),\n                                                        placeholder: \"<EMAIL>\",\n                                                        className: \"bg-slate-700 border-slate-600 text-white\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\admin\\\\EmailSettings.tsx\",\n                                                        lineNumber: 352,\n                                                        columnNumber: 15\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\admin\\\\EmailSettings.tsx\",\n                                                lineNumber: 348,\n                                                columnNumber: 13\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                        className: \"block text-sm font-medium text-slate-300 mb-2\",\n                                                        children: \"SMTP Password *\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\admin\\\\EmailSettings.tsx\",\n                                                        lineNumber: 361,\n                                                        columnNumber: 15\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"relative\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_2__.Input, {\n                                                                type: showPassword ? 'text' : 'password',\n                                                                value: settings.smtpPassword,\n                                                                onChange: (e)=>handleInputChange('smtpPassword', e.target.value),\n                                                                placeholder: \"App password or SMTP password\",\n                                                                className: \"bg-slate-700 border-slate-600 text-white pr-10\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\admin\\\\EmailSettings.tsx\",\n                                                                lineNumber: 365,\n                                                                columnNumber: 17\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                type: \"button\",\n                                                                onClick: ()=>setShowPassword(!showPassword),\n                                                                className: \"absolute inset-y-0 right-0 pr-3 flex items-center\",\n                                                                children: showPassword ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Edit_Eye_EyeOff_FileText_Mail_Plus_Send_Settings_Shield_TestTube_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                                                    className: \"h-4 w-4 text-slate-400\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\admin\\\\EmailSettings.tsx\",\n                                                                    lineNumber: 378,\n                                                                    columnNumber: 21\n                                                                }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Edit_Eye_EyeOff_FileText_Mail_Plus_Send_Settings_Shield_TestTube_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                                                    className: \"h-4 w-4 text-slate-400\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\admin\\\\EmailSettings.tsx\",\n                                                                    lineNumber: 380,\n                                                                    columnNumber: 21\n                                                                }, undefined)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\admin\\\\EmailSettings.tsx\",\n                                                                lineNumber: 372,\n                                                                columnNumber: 17\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\admin\\\\EmailSettings.tsx\",\n                                                        lineNumber: 364,\n                                                        columnNumber: 15\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\admin\\\\EmailSettings.tsx\",\n                                                lineNumber: 360,\n                                                columnNumber: 13\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\admin\\\\EmailSettings.tsx\",\n                                        lineNumber: 347,\n                                        columnNumber: 11\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center space-x-4\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            className: \"flex items-center space-x-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                    type: \"checkbox\",\n                                                    checked: settings.smtpSecure,\n                                                    onChange: (e)=>handleInputChange('smtpSecure', e.target.checked),\n                                                    className: \"rounded border-slate-600 text-blue-600 focus:ring-blue-500\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\admin\\\\EmailSettings.tsx\",\n                                                    lineNumber: 389,\n                                                    columnNumber: 15\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-sm text-slate-300\",\n                                                    children: \"Use SSL/TLS\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\admin\\\\EmailSettings.tsx\",\n                                                    lineNumber: 395,\n                                                    columnNumber: 15\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\admin\\\\EmailSettings.tsx\",\n                                            lineNumber: 388,\n                                            columnNumber: 13\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\admin\\\\EmailSettings.tsx\",\n                                        lineNumber: 387,\n                                        columnNumber: 11\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\admin\\\\EmailSettings.tsx\",\n                                lineNumber: 319,\n                                columnNumber: 9\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\admin\\\\EmailSettings.tsx\",\n                        lineNumber: 312,\n                        columnNumber: 7\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                        className: \"bg-slate-800 border-slate-700\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_2__.CardHeader, {\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_2__.CardTitle, {\n                                    className: \"flex items-center gap-2 text-white\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Edit_Eye_EyeOff_FileText_Mail_Plus_Send_Settings_Shield_TestTube_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                            className: \"h-5 w-5\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\admin\\\\EmailSettings.tsx\",\n                                            lineNumber: 405,\n                                            columnNumber: 13\n                                        }, undefined),\n                                        \"Email Configuration\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\admin\\\\EmailSettings.tsx\",\n                                    lineNumber: 404,\n                                    columnNumber: 11\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\admin\\\\EmailSettings.tsx\",\n                                lineNumber: 403,\n                                columnNumber: 9\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                                className: \"space-y-4\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"grid grid-cols-1 md:grid-cols-2 gap-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                    className: \"block text-sm font-medium text-slate-300 mb-2\",\n                                                    children: \"From Name\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\admin\\\\EmailSettings.tsx\",\n                                                    lineNumber: 412,\n                                                    columnNumber: 15\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_2__.Input, {\n                                                    type: \"text\",\n                                                    value: settings.fromName,\n                                                    onChange: (e)=>handleInputChange('fromName', e.target.value),\n                                                    placeholder: \"HashCoreX\",\n                                                    className: \"bg-slate-700 border-slate-600 text-white\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\admin\\\\EmailSettings.tsx\",\n                                                    lineNumber: 415,\n                                                    columnNumber: 15\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\admin\\\\EmailSettings.tsx\",\n                                            lineNumber: 411,\n                                            columnNumber: 13\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                    className: \"block text-sm font-medium text-slate-300 mb-2\",\n                                                    children: \"From Email *\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\admin\\\\EmailSettings.tsx\",\n                                                    lineNumber: 424,\n                                                    columnNumber: 15\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_2__.Input, {\n                                                    type: \"email\",\n                                                    value: settings.fromEmail,\n                                                    onChange: (e)=>handleInputChange('fromEmail', e.target.value),\n                                                    placeholder: \"<EMAIL>\",\n                                                    className: \"bg-slate-700 border-slate-600 text-white\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\admin\\\\EmailSettings.tsx\",\n                                                    lineNumber: 427,\n                                                    columnNumber: 15\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\admin\\\\EmailSettings.tsx\",\n                                            lineNumber: 423,\n                                            columnNumber: 13\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\admin\\\\EmailSettings.tsx\",\n                                    lineNumber: 410,\n                                    columnNumber: 11\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\admin\\\\EmailSettings.tsx\",\n                                lineNumber: 409,\n                                columnNumber: 9\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\admin\\\\EmailSettings.tsx\",\n                        lineNumber: 402,\n                        columnNumber: 7\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                        className: \"bg-slate-800 border-slate-700\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_2__.CardHeader, {\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_2__.CardTitle, {\n                                    className: \"flex items-center gap-2 text-white\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Edit_Eye_EyeOff_FileText_Mail_Plus_Send_Settings_Shield_TestTube_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                            className: \"h-5 w-5\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\admin\\\\EmailSettings.tsx\",\n                                            lineNumber: 443,\n                                            columnNumber: 13\n                                        }, undefined),\n                                        \"Test Email Configuration\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\admin\\\\EmailSettings.tsx\",\n                                    lineNumber: 442,\n                                    columnNumber: 11\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\admin\\\\EmailSettings.tsx\",\n                                lineNumber: 441,\n                                columnNumber: 9\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                                className: \"space-y-4\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-end space-x-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex-1\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                    className: \"block text-sm font-medium text-slate-300 mb-2\",\n                                                    children: \"Test Email Address\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\admin\\\\EmailSettings.tsx\",\n                                                    lineNumber: 450,\n                                                    columnNumber: 15\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_2__.Input, {\n                                                    type: \"email\",\n                                                    value: testEmail,\n                                                    onChange: (e)=>setTestEmail(e.target.value),\n                                                    placeholder: \"<EMAIL>\",\n                                                    className: \"bg-slate-700 border-slate-600 text-white\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\admin\\\\EmailSettings.tsx\",\n                                                    lineNumber: 453,\n                                                    columnNumber: 15\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\admin\\\\EmailSettings.tsx\",\n                                            lineNumber: 449,\n                                            columnNumber: 13\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                            onClick: handleTestEmail,\n                                            disabled: testing || !testEmail,\n                                            variant: \"outline\",\n                                            className: \"border-blue-600 text-blue-400 hover:bg-blue-600 hover:text-white\",\n                                            children: [\n                                                testing ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"animate-spin rounded-full h-4 w-4 border-b-2 border-current mr-2\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\admin\\\\EmailSettings.tsx\",\n                                                    lineNumber: 468,\n                                                    columnNumber: 17\n                                                }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Edit_Eye_EyeOff_FileText_Mail_Plus_Send_Settings_Shield_TestTube_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                    className: \"h-4 w-4 mr-2\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\admin\\\\EmailSettings.tsx\",\n                                                    lineNumber: 470,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                \"Send Test\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\admin\\\\EmailSettings.tsx\",\n                                            lineNumber: 461,\n                                            columnNumber: 13\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\admin\\\\EmailSettings.tsx\",\n                                    lineNumber: 448,\n                                    columnNumber: 11\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\admin\\\\EmailSettings.tsx\",\n                                lineNumber: 447,\n                                columnNumber: 9\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\admin\\\\EmailSettings.tsx\",\n                        lineNumber: 440,\n                        columnNumber: 7\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex justify-end\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                            onClick: handleSave,\n                            disabled: saving,\n                            className: \"bg-blue-600 hover:bg-blue-700 text-white\",\n                            children: [\n                                saving ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\admin\\\\EmailSettings.tsx\",\n                                    lineNumber: 486,\n                                    columnNumber: 13\n                                }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Edit_Eye_EyeOff_FileText_Mail_Plus_Send_Settings_Shield_TestTube_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                    className: \"h-4 w-4 mr-2\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\admin\\\\EmailSettings.tsx\",\n                                    lineNumber: 488,\n                                    columnNumber: 13\n                                }, undefined),\n                                \"Save Settings\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\admin\\\\EmailSettings.tsx\",\n                            lineNumber: 480,\n                            columnNumber: 9\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\admin\\\\EmailSettings.tsx\",\n                        lineNumber: 479,\n                        columnNumber: 7\n                    }, undefined)\n                ]\n            }, void 0, true),\n            activeTab === 'templates' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"space-y-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center justify-between\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                        className: \"text-xl font-semibold text-white\",\n                                        children: \"Email Templates\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\admin\\\\EmailSettings.tsx\",\n                                        lineNumber: 501,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-slate-400 text-sm\",\n                                        children: [\n                                            \"Manage email templates with variables like \",\n                                            \"{{firstName}}, {{otp}}, etc.\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\admin\\\\EmailSettings.tsx\",\n                                        lineNumber: 502,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\admin\\\\EmailSettings.tsx\",\n                                lineNumber: 500,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                onClick: handleCreateTemplate,\n                                className: \"bg-green-600 hover:bg-green-700 text-white\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Edit_Eye_EyeOff_FileText_Mail_Plus_Send_Settings_Shield_TestTube_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                        className: \"w-4 h-4 mr-2\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\admin\\\\EmailSettings.tsx\",\n                                        lineNumber: 508,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    \"New Template\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\admin\\\\EmailSettings.tsx\",\n                                lineNumber: 504,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\admin\\\\EmailSettings.tsx\",\n                        lineNumber: 499,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"grid gap-4\",\n                        children: [\n                            templates.map((template)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                                    className: \"bg-slate-800 border-slate-700\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                                        className: \"p-4\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center justify-between\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex-1\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center gap-3\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                    className: \"font-medium text-white\",\n                                                                    children: template.name\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\admin\\\\EmailSettings.tsx\",\n                                                                    lineNumber: 521,\n                                                                    columnNumber: 25\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"px-2 py-1 rounded-full text-xs \".concat(template.isActive ? 'bg-green-100 text-green-800' : 'bg-gray-100 text-gray-800'),\n                                                                    children: template.isActive ? 'Active' : 'Inactive'\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\admin\\\\EmailSettings.tsx\",\n                                                                    lineNumber: 522,\n                                                                    columnNumber: 25\n                                                                }, undefined)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\admin\\\\EmailSettings.tsx\",\n                                                            lineNumber: 520,\n                                                            columnNumber: 23\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-slate-400 text-sm mt-1\",\n                                                            children: template.subject\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\admin\\\\EmailSettings.tsx\",\n                                                            lineNumber: 530,\n                                                            columnNumber: 23\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-slate-500 text-xs mt-1\",\n                                                            children: [\n                                                                \"Updated: \",\n                                                                new Date(template.updatedAt).toLocaleDateString()\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\admin\\\\EmailSettings.tsx\",\n                                                            lineNumber: 531,\n                                                            columnNumber: 23\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\admin\\\\EmailSettings.tsx\",\n                                                    lineNumber: 519,\n                                                    columnNumber: 21\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center gap-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                                            onClick: ()=>handleEditTemplate(template),\n                                                            variant: \"outline\",\n                                                            size: \"sm\",\n                                                            className: \"border-slate-600 text-slate-300 hover:bg-slate-700\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Edit_Eye_EyeOff_FileText_Mail_Plus_Send_Settings_Shield_TestTube_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                                className: \"w-4 h-4\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\admin\\\\EmailSettings.tsx\",\n                                                                lineNumber: 542,\n                                                                columnNumber: 25\n                                                            }, undefined)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\admin\\\\EmailSettings.tsx\",\n                                                            lineNumber: 536,\n                                                            columnNumber: 23\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                                            onClick: ()=>handleDeleteTemplate(template.name),\n                                                            variant: \"outline\",\n                                                            size: \"sm\",\n                                                            className: \"border-red-600 text-red-400 hover:bg-red-900/20\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Edit_Eye_EyeOff_FileText_Mail_Plus_Send_Settings_Shield_TestTube_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                                className: \"w-4 h-4\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\admin\\\\EmailSettings.tsx\",\n                                                                lineNumber: 550,\n                                                                columnNumber: 25\n                                                            }, undefined)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\admin\\\\EmailSettings.tsx\",\n                                                            lineNumber: 544,\n                                                            columnNumber: 23\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\admin\\\\EmailSettings.tsx\",\n                                                    lineNumber: 535,\n                                                    columnNumber: 21\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\admin\\\\EmailSettings.tsx\",\n                                            lineNumber: 518,\n                                            columnNumber: 19\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\admin\\\\EmailSettings.tsx\",\n                                        lineNumber: 517,\n                                        columnNumber: 17\n                                    }, undefined)\n                                }, template.id, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\admin\\\\EmailSettings.tsx\",\n                                    lineNumber: 516,\n                                    columnNumber: 15\n                                }, undefined)),\n                            templates.length === 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-center py-12\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Edit_Eye_EyeOff_FileText_Mail_Plus_Send_Settings_Shield_TestTube_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                        className: \"w-12 h-12 text-slate-600 mx-auto mb-4\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\admin\\\\EmailSettings.tsx\",\n                                        lineNumber: 560,\n                                        columnNumber: 17\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"text-lg font-medium text-slate-400 mb-2\",\n                                        children: \"No templates found\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\admin\\\\EmailSettings.tsx\",\n                                        lineNumber: 561,\n                                        columnNumber: 17\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-slate-500 mb-4\",\n                                        children: \"Create your first email template to get started.\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\admin\\\\EmailSettings.tsx\",\n                                        lineNumber: 562,\n                                        columnNumber: 17\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                        onClick: handleCreateTemplate,\n                                        className: \"bg-blue-600 hover:bg-blue-700 text-white\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Edit_Eye_EyeOff_FileText_Mail_Plus_Send_Settings_Shield_TestTube_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                className: \"w-4 h-4 mr-2\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\admin\\\\EmailSettings.tsx\",\n                                                lineNumber: 567,\n                                                columnNumber: 19\n                                            }, undefined),\n                                            \"Create Template\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\admin\\\\EmailSettings.tsx\",\n                                        lineNumber: 563,\n                                        columnNumber: 17\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\admin\\\\EmailSettings.tsx\",\n                                lineNumber: 559,\n                                columnNumber: 15\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\admin\\\\EmailSettings.tsx\",\n                        lineNumber: 514,\n                        columnNumber: 11\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\admin\\\\EmailSettings.tsx\",\n                lineNumber: 497,\n                columnNumber: 9\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_EmailTemplateModal__WEBPACK_IMPORTED_MODULE_3__.EmailTemplateModal, {\n                isOpen: showTemplateModal,\n                onClose: ()=>setShowTemplateModal(false),\n                template: editingTemplate,\n                onSave: fetchEmailTemplates\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\admin\\\\EmailSettings.tsx\",\n                lineNumber: 577,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\admin\\\\EmailSettings.tsx\",\n        lineNumber: 261,\n        columnNumber: 5\n    }, undefined);\n};\n_s(EmailSettings, \"DrfEAFfaeFrxmR5v9IlTupGL0tA=\", false, function() {\n    return [\n        _components_ui__WEBPACK_IMPORTED_MODULE_2__.useMessageBox\n    ];\n});\n_c = EmailSettings;\nvar _c;\n$RefreshReg$(_c, \"EmailSettings\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/admin/EmailSettings.tsx\n"));

/***/ })

});