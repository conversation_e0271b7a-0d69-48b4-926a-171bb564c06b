"use strict";(()=>{var e={};e.id=1776,e.ids=[1776],e.modules={3295:e=>{e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},12909:(e,t,r)=>{r.d(t,{DT:()=>h,DY:()=>y,HU:()=>u,Lx:()=>g,b9:()=>f,qc:()=>w});var a=r(85663),i=r(43205),s=r.n(i),n=r(6710);let o=process.env.JWT_SECRET||"fallback-secret-key",d=process.env.JWT_EXPIRES_IN||"30d",l=async e=>await a.Ay.hash(e,12),c=async(e,t)=>await a.Ay.compare(e,t),u=e=>s().sign(e,o,{expiresIn:d}),p=e=>{try{return s().verify(e,o)}catch(e){return null}},m=()=>{let e="ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789",t="HC";for(let r=0;r<8;r++)t+=e.charAt(Math.floor(Math.random()*e.length));return t},f=async e=>{let t=e.headers.get("authorization")?.replace("Bearer ","")||e.cookies.get("auth-token")?.value;if(!t)return{authenticated:!1,user:null};let r=p(t);if(!r)return{authenticated:!1,user:null};let a=await n.Gy.findByEmail(r.email);return a?{authenticated:!0,user:a}:{authenticated:!1,user:null}},y=async e=>{let t,a;if(await n.Gy.findByEmail(e.email))throw Error("User already exists with this email");if(e.referralCode){let r=await n.Gy.findByReferralId(e.referralCode);if(!r)throw Error("Invalid referral code");t=r.id}let i=await l(e.password),s=!1;do a=m(),s=!await n.Gy.findByReferralId(a);while(!s);let o=await n.Gy.create({email:e.email,firstName:e.firstName,lastName:e.lastName,password:i,referralId:a});if(t){let{placeUserByReferralType:a}=await r.e(2746).then(r.bind(r,2746)),i="general";"left"===e.placementSide?i="left":"right"===e.placementSide&&(i="right"),await a(t,o.id,i)}return{id:o.id,email:o.email,referralId:o.referralId,kycStatus:o.kycStatus}},g=async e=>{let t=await n.Gy.findByEmail(e.email);if(!t||!await c(e.password,t.password))throw Error("Invalid email or password");return{token:u({userId:t.id,email:t.email}),user:{id:t.id,email:t.email,referralId:t.referralId,kycStatus:t.kycStatus}}},h=e=>/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(e),w=async e=>{let t=await n.Gy.findById(e);return t?.role==="ADMIN"}},27910:e=>{e.exports=require("stream")},28354:e=>{e.exports=require("util")},29294:e=>{e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},38974:(e,t,r)=>{r.r(t),r.d(t,{patchFetch:()=>y,routeModule:()=>u,serverHooks:()=>f,workAsyncStorage:()=>p,workUnitAsyncStorage:()=>m});var a={};r.r(a),r.d(a,{GET:()=>c});var i=r(96559),s=r(48088),n=r(37719),o=r(32190),d=r(12909),l=r(31183);async function c(e){try{let{authenticated:t,user:r}=await (0,d.b9)(e);if(!t||!r)return o.NextResponse.json({error:"Not authenticated"},{status:401});if(!await (0,d.qc)(r.id))return o.NextResponse.json({error:"Admin access required"},{status:403});let{searchParams:a}=new URL(e.url),i=a.get("search")||"",s=a.get("action")||"",n=a.get("userId")||"",c=a.get("dateFrom")||"",u=a.get("dateTo")||"",p={};if(i&&(p.OR=[{action:{contains:i,mode:"insensitive"}},{details:{contains:i,mode:"insensitive"}},{ipAddress:{contains:i,mode:"insensitive"}},{userAgent:{contains:i,mode:"insensitive"}}]),s&&"all"!==s&&("ERROR"===s?p.action={contains:"ERROR",mode:"insensitive"}:"LOGIN"===s?p.action={contains:"LOGIN",mode:"insensitive"}:"ADMIN"===s?p.action={contains:"ADMIN",mode:"insensitive"}:"PAYMENT"===s?p.OR=[{action:{contains:"PAYMENT",mode:"insensitive"}},{action:{contains:"WALLET",mode:"insensitive"}},{action:{contains:"DEPOSIT",mode:"insensitive"}},{action:{contains:"WITHDRAWAL",mode:"insensitive"}}]:p.action={contains:s,mode:"insensitive"}),n&&(p.userId=n),(c||u)&&(p.createdAt={},c&&(p.createdAt.gte=new Date(c)),u)){let e=new Date(u);e.setHours(23,59,59,999),p.createdAt.lte=e}let m=(await l.prisma.systemLog.findMany({where:p,include:{user:{select:{firstName:!0,lastName:!0,email:!0}}},orderBy:{createdAt:"desc"}})).map(e=>[e.createdAt.toISOString(),e.action,e.user?`${e.user.firstName} ${e.user.lastName}`:"System",e.user?.email||"N/A",e.details||"",e.ipAddress||"",e.userAgent||""]),f=["Date,Action,User,Email,Details,IP Address,User Agent",...m.map(e=>e.map(e=>`"${String(e).replace(/"/g,'""')}"`).join(","))].join("\n");return new o.NextResponse(f,{headers:{"Content-Type":"text/csv","Content-Disposition":`attachment; filename="system-logs-${new Date().toISOString().split("T")[0]}.csv"`}})}catch(e){return console.error("Admin logs export error:",e),o.NextResponse.json({success:!1,error:"Failed to export logs"},{status:500})}}let u=new i.AppRouteRouteModule({definition:{kind:s.RouteKind.APP_ROUTE,page:"/api/admin/logs/export/route",pathname:"/api/admin/logs/export",filename:"route",bundlePath:"app/api/admin/logs/export/route"},resolvedPagePath:"C:\\Users\\<USER>\\Desktop\\Hash_Minings\\hashcorex\\src\\app\\api\\admin\\logs\\export\\route.ts",nextConfigOutput:"",userland:a}),{workAsyncStorage:p,workUnitAsyncStorage:m,serverHooks:f}=u;function y(){return(0,n.patchFetch)({workAsyncStorage:p,workUnitAsyncStorage:m})}},44870:e=>{e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},55511:e=>{e.exports=require("crypto")},63033:e=>{e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},79428:e=>{e.exports=require("buffer")},96330:e=>{e.exports=require("@prisma/client")}};var t=require("../../../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),a=t.X(0,[4447,580,5315,3306],()=>r(38974));module.exports=a})();