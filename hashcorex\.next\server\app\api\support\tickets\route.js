"use strict";(()=>{var e={};e.id=5409,e.ids=[5409],e.modules={3295:e=>{e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},12909:(e,r,t)=>{t.d(r,{DT:()=>w,DY:()=>m,HU:()=>d,Lx:()=>h,b9:()=>y,qc:()=>x});var a=t(85663),s=t(43205),i=t.n(s),n=t(6710);let o=process.env.JWT_SECRET||"fallback-secret-key",u=process.env.JWT_EXPIRES_IN||"30d",l=async e=>await a.Ay.hash(e,12),c=async(e,r)=>await a.Ay.compare(e,r),d=e=>i().sign(e,o,{expiresIn:u}),p=e=>{try{return i().verify(e,o)}catch(e){return null}},f=()=>{let e="ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789",r="HC";for(let t=0;t<8;t++)r+=e.charAt(Math.floor(Math.random()*e.length));return r},y=async e=>{let r=e.headers.get("authorization")?.replace("Bearer ","")||e.cookies.get("auth-token")?.value;if(!r)return{authenticated:!1,user:null};let t=p(r);if(!t)return{authenticated:!1,user:null};let a=await n.Gy.findByEmail(t.email);return a?{authenticated:!0,user:a}:{authenticated:!1,user:null}},m=async e=>{let r,a;if(await n.Gy.findByEmail(e.email))throw Error("User already exists with this email");if(e.referralCode){let t=await n.Gy.findByReferralId(e.referralCode);if(!t)throw Error("Invalid referral code");r=t.id}let s=await l(e.password),i=!1;do a=f(),i=!await n.Gy.findByReferralId(a);while(!i);let o=await n.Gy.create({email:e.email,firstName:e.firstName,lastName:e.lastName,password:s,referralId:a});if(r){let{placeUserByReferralType:a}=await t.e(2746).then(t.bind(t,2746)),s="general";"left"===e.placementSide?s="left":"right"===e.placementSide&&(s="right"),await a(r,o.id,s)}return{id:o.id,email:o.email,referralId:o.referralId,kycStatus:o.kycStatus}},h=async e=>{let r=await n.Gy.findByEmail(e.email);if(!r||!await c(e.password,r.password))throw Error("Invalid email or password");return{token:d({userId:r.id,email:r.email}),user:{id:r.id,email:r.email,referralId:r.referralId,kycStatus:r.kycStatus}}},w=e=>/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(e),x=async e=>{let r=await n.Gy.findById(e);return r?.role==="ADMIN"}},27910:e=>{e.exports=require("stream")},28354:e=>{e.exports=require("util")},29294:e=>{e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},44870:e=>{e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},55511:e=>{e.exports=require("crypto")},63033:e=>{e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},72463:(e,r,t)=>{t.r(r),t.d(r,{patchFetch:()=>h,routeModule:()=>p,serverHooks:()=>m,workAsyncStorage:()=>f,workUnitAsyncStorage:()=>y});var a={};t.r(a),t.d(a,{GET:()=>c,POST:()=>d});var s=t(96559),i=t(48088),n=t(37719),o=t(32190),u=t(12909),l=t(6710);async function c(e){try{let{authenticated:r,user:t}=await (0,u.b9)(e);if(!r||!t)return o.NextResponse.json({success:!1,error:"Not authenticated"},{status:401});let a=await l.OZ.findByUserId(t.id);return o.NextResponse.json({success:!0,data:a})}catch(e){return console.error("Support tickets fetch error:",e),o.NextResponse.json({success:!1,error:"Failed to fetch support tickets"},{status:500})}}async function d(e){try{let{authenticated:r,user:t}=await (0,u.b9)(e);if(!r||!t)return o.NextResponse.json({success:!1,error:"Not authenticated"},{status:401});let{subject:a,message:s,priority:i="MEDIUM",category:n}=await e.json();if(!a||!s)return o.NextResponse.json({success:!1,error:"Subject and message are required"},{status:400});if(!["LOW","MEDIUM","HIGH","URGENT"].includes(i))return o.NextResponse.json({success:!1,error:"Invalid priority level"},{status:400});let c=await l.OZ.create({userId:t.id,subject:a.trim(),message:s.trim(),priority:i,category:n?.trim()||null});return o.NextResponse.json({success:!0,data:c,message:"Support ticket created successfully"})}catch(e){return console.error("Support ticket creation error:",e),o.NextResponse.json({success:!1,error:"Failed to create support ticket"},{status:500})}}let p=new s.AppRouteRouteModule({definition:{kind:i.RouteKind.APP_ROUTE,page:"/api/support/tickets/route",pathname:"/api/support/tickets",filename:"route",bundlePath:"app/api/support/tickets/route"},resolvedPagePath:"C:\\Users\\<USER>\\Desktop\\Hash_Minings\\hashcorex\\src\\app\\api\\support\\tickets\\route.ts",nextConfigOutput:"",userland:a}),{workAsyncStorage:f,workUnitAsyncStorage:y,serverHooks:m}=p;function h(){return(0,n.patchFetch)({workAsyncStorage:f,workUnitAsyncStorage:y})}},79428:e=>{e.exports=require("buffer")},96330:e=>{e.exports=require("@prisma/client")}};var r=require("../../../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),a=r.X(0,[4447,580,5315,3306],()=>t(72463));module.exports=a})();