"use strict";(()=>{var e={};e.id=1177,e.ids=[1177],e.modules={3295:e=>{e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},12909:(e,t,r)=>{r.d(t,{DT:()=>w,DY:()=>y,HU:()=>l,Lx:()=>h,b9:()=>m,qc:()=>x});var s=r(85663),a=r(43205),i=r.n(a),n=r(6710);let o=process.env.JWT_SECRET||"fallback-secret-key",u=process.env.JWT_EXPIRES_IN||"30d",c=async e=>await s.Ay.hash(e,12),d=async(e,t)=>await s.Ay.compare(e,t),l=e=>i().sign(e,o,{expiresIn:u}),p=e=>{try{return i().verify(e,o)}catch(e){return null}},f=()=>{let e="ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789",t="HC";for(let r=0;r<8;r++)t+=e.charAt(Math.floor(Math.random()*e.length));return t},m=async e=>{let t=e.headers.get("authorization")?.replace("Bearer ","")||e.cookies.get("auth-token")?.value;if(!t)return{authenticated:!1,user:null};let r=p(t);if(!r)return{authenticated:!1,user:null};let s=await n.Gy.findByEmail(r.email);return s?{authenticated:!0,user:s}:{authenticated:!1,user:null}},y=async e=>{let t,s;if(await n.Gy.findByEmail(e.email))throw Error("User already exists with this email");if(e.referralCode){let r=await n.Gy.findByReferralId(e.referralCode);if(!r)throw Error("Invalid referral code");t=r.id}let a=await c(e.password),i=!1;do s=f(),i=!await n.Gy.findByReferralId(s);while(!i);let o=await n.Gy.create({email:e.email,firstName:e.firstName,lastName:e.lastName,password:a,referralId:s});if(t){let{placeUserByReferralType:s}=await r.e(2746).then(r.bind(r,2746)),a="general";"left"===e.placementSide?a="left":"right"===e.placementSide&&(a="right"),await s(t,o.id,a)}return{id:o.id,email:o.email,referralId:o.referralId,kycStatus:o.kycStatus}},h=async e=>{let t=await n.Gy.findByEmail(e.email);if(!t||!await d(e.password,t.password))throw Error("Invalid email or password");return{token:l({userId:t.id,email:t.email}),user:{id:t.id,email:t.email,referralId:t.referralId,kycStatus:t.kycStatus}}},w=e=>/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(e),x=async e=>{let t=await n.Gy.findById(e);return t?.role==="ADMIN"}},27910:e=>{e.exports=require("stream")},28354:e=>{e.exports=require("util")},29294:e=>{e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},44870:e=>{e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},55511:e=>{e.exports=require("crypto")},63033:e=>{e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},76768:(e,t,r)=>{r.r(t),r.d(t,{patchFetch:()=>h,routeModule:()=>p,serverHooks:()=>y,workAsyncStorage:()=>f,workUnitAsyncStorage:()=>m});var s={};r.r(s),r.d(s,{GET:()=>d,PUT:()=>l});var a=r(96559),i=r(48088),n=r(37719),o=r(32190),u=r(12909),c=r(6710);async function d(e,{params:t}){try{let{authenticated:r,user:s}=await (0,u.b9)(e);if(!r||!s)return o.NextResponse.json({success:!1,error:"Not authenticated"},{status:401});if("ADMIN"!==s.role)return o.NextResponse.json({success:!1,error:"Access denied"},{status:403});let{ticketId:a}=await t,i=await c.OZ.findById(a);if(!i)return o.NextResponse.json({success:!1,error:"Ticket not found"},{status:404});return o.NextResponse.json({success:!0,data:i})}catch(e){return console.error("Admin ticket fetch error:",e),o.NextResponse.json({success:!1,error:"Failed to fetch ticket"},{status:500})}}async function l(e,{params:t}){try{let{authenticated:r,user:s}=await (0,u.b9)(e);if(!r||!s)return o.NextResponse.json({success:!1,error:"Not authenticated"},{status:401});if("ADMIN"!==s.role)return o.NextResponse.json({success:!1,error:"Access denied"},{status:403});let{ticketId:a}=await t,{status:i}=await e.json();if(!["OPEN","IN_PROGRESS","RESOLVED","CLOSED"].includes(i))return o.NextResponse.json({success:!1,error:"Invalid status"},{status:400});if(!await c.OZ.findById(a))return o.NextResponse.json({success:!1,error:"Ticket not found"},{status:404});let n=await c.OZ.updateStatus(a,i);return o.NextResponse.json({success:!0,data:n,message:"Ticket status updated successfully"})}catch(e){return console.error("Admin ticket update error:",e),o.NextResponse.json({success:!1,error:"Failed to update ticket"},{status:500})}}let p=new a.AppRouteRouteModule({definition:{kind:i.RouteKind.APP_ROUTE,page:"/api/admin/support/tickets/[ticketId]/route",pathname:"/api/admin/support/tickets/[ticketId]",filename:"route",bundlePath:"app/api/admin/support/tickets/[ticketId]/route"},resolvedPagePath:"C:\\Users\\<USER>\\Desktop\\Hash_Minings\\hashcorex\\src\\app\\api\\admin\\support\\tickets\\[ticketId]\\route.ts",nextConfigOutput:"",userland:s}),{workAsyncStorage:f,workUnitAsyncStorage:m,serverHooks:y}=p;function h(){return(0,n.patchFetch)({workAsyncStorage:f,workUnitAsyncStorage:m})}},79428:e=>{e.exports=require("buffer")},96330:e=>{e.exports=require("@prisma/client")}};var t=require("../../../../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),s=t.X(0,[4447,580,5315,3306],()=>r(76768));module.exports=s})();