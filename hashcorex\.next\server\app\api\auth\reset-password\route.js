"use strict";(()=>{var e={};e.id=6478,e.ids=[6478],e.modules={3295:e=>{e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},10974:(e,r,t)=>{t.d(r,{Oj:()=>a});function a(e){let r=[],t=[],a=e.length>=8;t.push({valid:a,message:"At least 8 characters long"}),a||r.push("Password must be at least 8 characters long");let s=/[A-Z]/.test(e);t.push({valid:s,message:"At least one uppercase letter"}),s||r.push("Password must contain at least one uppercase letter");let i=/[a-z]/.test(e);t.push({valid:i,message:"At least one lowercase letter"}),i||r.push("Password must contain at least one lowercase letter");let o=/\d/.test(e);t.push({valid:o,message:"At least one number"}),o||r.push("Password must contain at least one number");let n=/[!@#$%^&*(),.?":{}|<>]/.test(e);return t.push({valid:n,message:"At least one special character"}),n||r.push("Password must contain at least one special character"),{isValid:0===r.length,errors:r,checks:t}}},12909:(e,r,t)=>{t.d(r,{DT:()=>f,DY:()=>g,HU:()=>u,Lx:()=>w,b9:()=>h,qc:()=>y});var a=t(85663),s=t(43205),i=t.n(s),o=t(6710);let n=process.env.JWT_SECRET||"fallback-secret-key",l=process.env.JWT_EXPIRES_IN||"30d",d=async e=>await a.Ay.hash(e,12),c=async(e,r)=>await a.Ay.compare(e,r),u=e=>i().sign(e,n,{expiresIn:l}),p=e=>{try{return i().verify(e,n)}catch(e){return null}},m=()=>{let e="ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789",r="HC";for(let t=0;t<8;t++)r+=e.charAt(Math.floor(Math.random()*e.length));return r},h=async e=>{let r=e.headers.get("authorization")?.replace("Bearer ","")||e.cookies.get("auth-token")?.value;if(!r)return{authenticated:!1,user:null};let t=p(r);if(!t)return{authenticated:!1,user:null};let a=await o.Gy.findByEmail(t.email);return a?{authenticated:!0,user:a}:{authenticated:!1,user:null}},g=async e=>{let r,a;if(await o.Gy.findByEmail(e.email))throw Error("User already exists with this email");if(e.referralCode){let t=await o.Gy.findByReferralId(e.referralCode);if(!t)throw Error("Invalid referral code");r=t.id}let s=await d(e.password),i=!1;do a=m(),i=!await o.Gy.findByReferralId(a);while(!i);let n=await o.Gy.create({email:e.email,firstName:e.firstName,lastName:e.lastName,password:s,referralId:a});if(r){let{placeUserByReferralType:a}=await t.e(2746).then(t.bind(t,2746)),s="general";"left"===e.placementSide?s="left":"right"===e.placementSide&&(s="right"),await a(r,n.id,s)}return{id:n.id,email:n.email,referralId:n.referralId,kycStatus:n.kycStatus}},w=async e=>{let r=await o.Gy.findByEmail(e.email);if(!r||!await c(e.password,r.password))throw Error("Invalid email or password");return{token:u({userId:r.id,email:r.email}),user:{id:r.id,email:r.email,referralId:r.referralId,kycStatus:r.kycStatus}}},f=e=>/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(e),y=async e=>{let r=await o.Gy.findById(e);return r?.role==="ADMIN"}},24476:(e,r,t)=>{t.r(r),t.d(r,{patchFetch:()=>y,routeModule:()=>h,serverHooks:()=>f,workAsyncStorage:()=>g,workUnitAsyncStorage:()=>w});var a={};t.r(a),t.d(a,{POST:()=>m});var s=t(96559),i=t(48088),o=t(37719),n=t(32190),l=t(12909),d=t(10974),c=t(6710),u=t(82629),p=t(85663);async function m(e){try{let{email:r,newPassword:t,confirmPassword:a}=await e.json();if(!r||!t||!a)return n.NextResponse.json({success:!1,error:"All fields are required"},{status:400});if(!(0,l.DT)(r))return n.NextResponse.json({success:!1,error:"Invalid email format"},{status:400});if(t!==a)return n.NextResponse.json({success:!1,error:"Passwords do not match"},{status:400});let s=(0,d.Oj)(t);if(!s.isValid)return n.NextResponse.json({success:!1,error:s.errors.join(", ")},{status:400});let i=await c.Gy.findByEmail(r);if(!i)return n.NextResponse.json({success:!1,error:"User not found"},{status:400});if(!await c.oV.findVerified(r,"password_reset"))return n.NextResponse.json({success:!1,error:"No verified OTP found. Please complete email verification first."},{status:400});let o=await p.Ay.hash(t,12);await c.Gy.updatePassword(i.id,o),await c.oV.cleanup(),await c.AJ.create({action:"PASSWORD_RESET",userId:i.id,details:{email:i.email,resetTime:new Date().toISOString()},ipAddress:e.headers.get("x-forwarded-for")||"unknown",userAgent:e.headers.get("user-agent")||"unknown"});let u=(0,l.HU)({userId:i.id,email:i.email}),m=n.NextResponse.json({success:!0,message:"Password reset successful",data:{user:{id:i.id,email:i.email,firstName:i.firstName,lastName:i.lastName,referralId:i.referralId,role:i.role,kycStatus:i.kycStatus,isActive:i.isActive,profilePicture:i.profilePicture,createdAt:i.createdAt,updatedAt:i.updatedAt},token:u}});return m.cookies.set("auth-token",u,{httpOnly:!0,secure:!0,sameSite:"strict",maxAge:2592e3,path:"/"}),m}catch(r){return console.error("Password reset error:",r),await u.v5.logApiError(e,r,"PASSWORD_RESET_ERROR"),n.NextResponse.json({success:!1,error:r.message||"Password reset failed"},{status:500})}}let h=new s.AppRouteRouteModule({definition:{kind:i.RouteKind.APP_ROUTE,page:"/api/auth/reset-password/route",pathname:"/api/auth/reset-password",filename:"route",bundlePath:"app/api/auth/reset-password/route"},resolvedPagePath:"C:\\Users\\<USER>\\Desktop\\Hash_Minings\\hashcorex\\src\\app\\api\\auth\\reset-password\\route.ts",nextConfigOutput:"",userland:a}),{workAsyncStorage:g,workUnitAsyncStorage:w,serverHooks:f}=h;function y(){return(0,o.patchFetch)({workAsyncStorage:g,workUnitAsyncStorage:w})}},27910:e=>{e.exports=require("stream")},28354:e=>{e.exports=require("util")},29294:e=>{e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},44870:e=>{e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},55511:e=>{e.exports=require("crypto")},63033:e=>{e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},79428:e=>{e.exports=require("buffer")},82629:(e,r,t)=>{t.d(r,{v5:()=>s});var a=t(6710);class s{static async logError(e){try{let r=e.error instanceof Error?e.error:Error(String(e.error)),t={action:e.action,userId:e.userId,adminId:e.adminId,details:{message:r.message,stack:r.stack,name:r.name,requestUrl:e.requestUrl,requestMethod:e.requestMethod,requestBody:e.requestBody,additionalData:e.additionalData,timestamp:new Date().toISOString()},ipAddress:e.ipAddress,userAgent:e.userAgent};await a.AJ.create(t),console.error(`[${e.action}] Error logged:`,{message:r.message,stack:r.stack,userId:e.userId,adminId:e.adminId,url:e.requestUrl})}catch(r){console.error("Failed to log error to database:",r),console.error("Original error:",e.error)}}static async logApiError(e,r,t,a,s,i){try{let o=null;try{if("GET"!==e.method&&e.headers.get("content-type")?.includes("application/json")){let r=e.clone();(o=await r.json()).password&&(o.password="[REDACTED]"),o.token&&(o.token="[REDACTED]"),o.apiKey&&(o.apiKey="[REDACTED]")}}catch(e){}await this.logError({action:t,error:r,userId:a,adminId:s,ipAddress:e.headers.get("x-forwarded-for")||e.headers.get("x-real-ip")||"unknown",userAgent:e.headers.get("user-agent")||"unknown",requestUrl:e.url,requestMethod:e.method,requestBody:o,additionalData:i})}catch(e){console.error("Failed to log API error:",e),console.error("Original error:",r)}}static async logClientError(e){try{await a.AJ.create({action:"CLIENT_ERROR",userId:e.userId,details:{message:e.message,stack:e.stack,url:e.url,timestamp:e.timestamp,additionalData:e.additionalData},userAgent:e.userAgent})}catch(e){console.error("Failed to log client error:",e)}}static async logAuthError(e,r,t,a){await this.logApiError(e,r,"AUTH_ERROR",void 0,void 0,{email:t,...a})}static async logDatabaseError(e,r,t,s,i){try{await a.AJ.create({action:"DATABASE_ERROR",userId:s,details:{message:e instanceof Error?e.message:String(e),stack:e instanceof Error?e.stack:void 0,operation:r,table:t,timestamp:new Date().toISOString(),additionalData:i}})}catch(r){console.error("Failed to log database error:",r),console.error("Original error:",e)}}static async logBusinessError(e,r,t,a,s){await this.logError({action:"BUSINESS_LOGIC_ERROR",error:e,userId:t,adminId:a,additionalData:{operation:r,...s}})}static async logExternalApiError(e,r,t,a,s){await this.logError({action:"EXTERNAL_API_ERROR",error:e,userId:a,additionalData:{service:r,endpoint:t,...s}})}static async logValidationError(e,r,t,a,s){await this.logError({action:"VALIDATION_ERROR",error:e,userId:a,additionalData:{field:r,value:t,...s}})}}},96330:e=>{e.exports=require("@prisma/client")}};var r=require("../../../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),a=r.X(0,[4447,580,5315,3306],()=>t(24476));module.exports=a})();