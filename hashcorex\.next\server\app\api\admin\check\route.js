"use strict";(()=>{var e={};e.id=7718,e.ids=[7718],e.modules={3295:e=>{e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},12909:(e,r,t)=>{t.d(r,{DT:()=>w,DY:()=>f,HU:()=>c,Lx:()=>y,b9:()=>h,qc:()=>x});var a=t(85663),i=t(43205),s=t.n(i),n=t(6710);let o=process.env.JWT_SECRET||"fallback-secret-key",l=process.env.JWT_EXPIRES_IN||"30d",d=async e=>await a.Ay.hash(e,12),u=async(e,r)=>await a.Ay.compare(e,r),c=e=>s().sign(e,o,{expiresIn:l}),p=e=>{try{return s().verify(e,o)}catch(e){return null}},m=()=>{let e="ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789",r="HC";for(let t=0;t<8;t++)r+=e.charAt(Math.floor(Math.random()*e.length));return r},h=async e=>{let r=e.headers.get("authorization")?.replace("Bearer ","")||e.cookies.get("auth-token")?.value;if(!r)return{authenticated:!1,user:null};let t=p(r);if(!t)return{authenticated:!1,user:null};let a=await n.Gy.findByEmail(t.email);return a?{authenticated:!0,user:a}:{authenticated:!1,user:null}},f=async e=>{let r,a;if(await n.Gy.findByEmail(e.email))throw Error("User already exists with this email");if(e.referralCode){let t=await n.Gy.findByReferralId(e.referralCode);if(!t)throw Error("Invalid referral code");r=t.id}let i=await d(e.password),s=!1;do a=m(),s=!await n.Gy.findByReferralId(a);while(!s);let o=await n.Gy.create({email:e.email,firstName:e.firstName,lastName:e.lastName,password:i,referralId:a});if(r){let{placeUserByReferralType:a}=await t.e(2746).then(t.bind(t,2746)),i="general";"left"===e.placementSide?i="left":"right"===e.placementSide&&(i="right"),await a(r,o.id,i)}return{id:o.id,email:o.email,referralId:o.referralId,kycStatus:o.kycStatus}},y=async e=>{let r=await n.Gy.findByEmail(e.email);if(!r||!await u(e.password,r.password))throw Error("Invalid email or password");return{token:c({userId:r.id,email:r.email}),user:{id:r.id,email:r.email,referralId:r.referralId,kycStatus:r.kycStatus}}},w=e=>/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(e),x=async e=>{let r=await n.Gy.findById(e);return r?.role==="ADMIN"}},27910:e=>{e.exports=require("stream")},28354:e=>{e.exports=require("util")},29294:e=>{e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},44870:e=>{e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},55511:e=>{e.exports=require("crypto")},63033:e=>{e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},79428:e=>{e.exports=require("buffer")},85111:(e,r,t)=>{t.r(r),t.d(r,{patchFetch:()=>h,routeModule:()=>u,serverHooks:()=>m,workAsyncStorage:()=>c,workUnitAsyncStorage:()=>p});var a={};t.r(a),t.d(a,{GET:()=>d});var i=t(96559),s=t(48088),n=t(37719),o=t(32190),l=t(12909);async function d(e){try{let{authenticated:r,user:t}=await (0,l.b9)(e);if(!r||!t)return o.NextResponse.json({success:!1,error:"Not authenticated",isAdmin:!1},{status:401});let a=await (0,l.qc)(t.id);return o.NextResponse.json({success:!0,isAdmin:a,user:{id:t.id,email:t.email}})}catch(e){return console.error("Admin check error:",e),o.NextResponse.json({success:!1,error:"Admin check failed",isAdmin:!1},{status:500})}}let u=new i.AppRouteRouteModule({definition:{kind:s.RouteKind.APP_ROUTE,page:"/api/admin/check/route",pathname:"/api/admin/check",filename:"route",bundlePath:"app/api/admin/check/route"},resolvedPagePath:"C:\\Users\\<USER>\\Desktop\\Hash_Minings\\hashcorex\\src\\app\\api\\admin\\check\\route.ts",nextConfigOutput:"",userland:a}),{workAsyncStorage:c,workUnitAsyncStorage:p,serverHooks:m}=u;function h(){return(0,n.patchFetch)({workAsyncStorage:c,workUnitAsyncStorage:p})}},96330:e=>{e.exports=require("@prisma/client")}};var r=require("../../../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),a=r.X(0,[4447,580,5315,3306],()=>t(85111));module.exports=a})();