"use strict";(()=>{var e={};e.id=1376,e.ids=[1376],e.modules={3295:e=>{e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},12909:(e,r,t)=>{t.d(r,{DT:()=>y,DY:()=>w,HU:()=>c,Lx:()=>h,b9:()=>f,qc:()=>g});var a=t(85663),s=t(43205),i=t.n(s),n=t(6710);let o=process.env.JWT_SECRET||"fallback-secret-key",l=process.env.JWT_EXPIRES_IN||"30d",d=async e=>await a.Ay.hash(e,12),u=async(e,r)=>await a.Ay.compare(e,r),c=e=>i().sign(e,o,{expiresIn:l}),m=e=>{try{return i().verify(e,o)}catch(e){return null}},p=()=>{let e="ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789",r="HC";for(let t=0;t<8;t++)r+=e.charAt(Math.floor(Math.random()*e.length));return r},f=async e=>{let r=e.headers.get("authorization")?.replace("Bearer ","")||e.cookies.get("auth-token")?.value;if(!r)return{authenticated:!1,user:null};let t=m(r);if(!t)return{authenticated:!1,user:null};let a=await n.Gy.findByEmail(t.email);return a?{authenticated:!0,user:a}:{authenticated:!1,user:null}},w=async e=>{let r,a;if(await n.Gy.findByEmail(e.email))throw Error("User already exists with this email");if(e.referralCode){let t=await n.Gy.findByReferralId(e.referralCode);if(!t)throw Error("Invalid referral code");r=t.id}let s=await d(e.password),i=!1;do a=p(),i=!await n.Gy.findByReferralId(a);while(!i);let o=await n.Gy.create({email:e.email,firstName:e.firstName,lastName:e.lastName,password:s,referralId:a});if(r){let{placeUserByReferralType:a}=await t.e(2746).then(t.bind(t,2746)),s="general";"left"===e.placementSide?s="left":"right"===e.placementSide&&(s="right"),await a(r,o.id,s)}return{id:o.id,email:o.email,referralId:o.referralId,kycStatus:o.kycStatus}},h=async e=>{let r=await n.Gy.findByEmail(e.email);if(!r||!await u(e.password,r.password))throw Error("Invalid email or password");return{token:c({userId:r.id,email:r.email}),user:{id:r.id,email:r.email,referralId:r.referralId,kycStatus:r.kycStatus}}},y=e=>/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(e),g=async e=>{let r=await n.Gy.findById(e);return r?.role==="ADMIN"}},16918:(e,r,t)=>{t.r(r),t.d(r,{patchFetch:()=>w,routeModule:()=>c,serverHooks:()=>f,workAsyncStorage:()=>m,workUnitAsyncStorage:()=>p});var a={};t.r(a),t.d(a,{GET:()=>u});var s=t(96559),i=t(48088),n=t(37719),o=t(32190),l=t(12909),d=t(31183);async function u(e){try{let{authenticated:r,user:t}=await (0,l.b9)(e);if(!r||!t)return o.NextResponse.json({success:!1,error:"Authentication required"},{status:401});if("ADMIN"!==t.role)return o.NextResponse.json({success:!1,error:"Admin access required"},{status:403});let{searchParams:a}=new URL(e.url),s=a.get("dateRange")||"30d";a.get("type");let i=a.get("status")||"all",n=parseInt(a.get("page")||"1"),u=parseInt(a.get("limit")||"20"),c=(n-1)*u,m={};if("all"!==s){let e=parseInt(s.replace("d",""));m={gte:new Date(Date.now()-24*e*36e5)}}let p={type:"DIRECT_REFERRAL"};"all"!==s&&(p.createdAt=m),"all"!==i&&(p.status=i);let f=await d.prisma.transaction.findMany({where:p,include:{user:{select:{id:!0,email:!0,firstName:!0,lastName:!0}}},orderBy:{createdAt:"desc"},take:u,skip:c}),w=await Promise.all(f.map(async e=>{let r=null;if(e.reference&&e.reference.startsWith("from_user:")){let t=e.reference.replace("from_user:","");r=await d.prisma.user.findUnique({where:{id:t},select:{id:!0,email:!0,firstName:!0,lastName:!0}})}if(!r&&e.user)for(let t of(await d.prisma.user.findMany({where:{referrerId:e.userId},select:{id:!0,email:!0,firstName:!0,lastName:!0,miningUnits:{select:{id:!0,investmentAmount:!0,createdAt:!0},orderBy:{createdAt:"desc"}}}}))){for(let a of t.miningUnits){let s=.1*a.investmentAmount,i=Math.abs(new Date(e.createdAt).getTime()-new Date(a.createdAt).getTime());if(.01>Math.abs(s-e.amount)&&i<=18e5){r={id:t.id,email:t.email,firstName:t.firstName,lastName:t.lastName};break}}if(r)break}let t=e.amount/.1;return{id:e.id,fromUserId:r?.id||"unknown",toUserId:e.userId,fromUser:r||{id:"unknown",email:"Unknown",firstName:"Unknown",lastName:"User"},toUser:e.user,amount:e.amount,commissionRate:10,originalAmount:t,type:"DIRECT_REFERRAL",description:e.description||"",createdAt:e.createdAt,status:e.status}})),h=await d.prisma.transaction.count({where:p});return o.NextResponse.json({success:!0,data:w,pagination:{total:h,limit:u,offset:c,hasMore:c+u<h}})}catch(e){return console.error("Referral commissions fetch error:",e),o.NextResponse.json({success:!1,error:"Failed to fetch referral commissions"},{status:500})}}let c=new s.AppRouteRouteModule({definition:{kind:i.RouteKind.APP_ROUTE,page:"/api/admin/referral-commissions/route",pathname:"/api/admin/referral-commissions",filename:"route",bundlePath:"app/api/admin/referral-commissions/route"},resolvedPagePath:"C:\\Users\\<USER>\\Desktop\\Hash_Minings\\hashcorex\\src\\app\\api\\admin\\referral-commissions\\route.ts",nextConfigOutput:"",userland:a}),{workAsyncStorage:m,workUnitAsyncStorage:p,serverHooks:f}=c;function w(){return(0,n.patchFetch)({workAsyncStorage:m,workUnitAsyncStorage:p})}},27910:e=>{e.exports=require("stream")},28354:e=>{e.exports=require("util")},29294:e=>{e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},44870:e=>{e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},55511:e=>{e.exports=require("crypto")},63033:e=>{e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},79428:e=>{e.exports=require("buffer")},96330:e=>{e.exports=require("@prisma/client")}};var r=require("../../../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),a=r.X(0,[4447,580,5315,3306],()=>t(16918));module.exports=a})();