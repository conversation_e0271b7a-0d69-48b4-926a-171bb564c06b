"use strict";(()=>{var e={};e.id=2400,e.ids=[2400],e.modules={3295:e=>{e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},12909:(e,r,t)=>{t.d(r,{DT:()=>h,DY:()=>w,HU:()=>c,Lx:()=>y,b9:()=>f,qc:()=>x});var a=t(85663),s=t(43205),i=t.n(s),n=t(6710);let o=process.env.JWT_SECRET||"fallback-secret-key",u=process.env.JWT_EXPIRES_IN||"30d",l=async e=>await a.Ay.hash(e,12),d=async(e,r)=>await a.Ay.compare(e,r),c=e=>i().sign(e,o,{expiresIn:u}),m=e=>{try{return i().verify(e,o)}catch(e){return null}},p=()=>{let e="ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789",r="HC";for(let t=0;t<8;t++)r+=e.charAt(Math.floor(Math.random()*e.length));return r},f=async e=>{let r=e.headers.get("authorization")?.replace("Bearer ","")||e.cookies.get("auth-token")?.value;if(!r)return{authenticated:!1,user:null};let t=m(r);if(!t)return{authenticated:!1,user:null};let a=await n.Gy.findByEmail(t.email);return a?{authenticated:!0,user:a}:{authenticated:!1,user:null}},w=async e=>{let r,a;if(await n.Gy.findByEmail(e.email))throw Error("User already exists with this email");if(e.referralCode){let t=await n.Gy.findByReferralId(e.referralCode);if(!t)throw Error("Invalid referral code");r=t.id}let s=await l(e.password),i=!1;do a=p(),i=!await n.Gy.findByReferralId(a);while(!i);let o=await n.Gy.create({email:e.email,firstName:e.firstName,lastName:e.lastName,password:s,referralId:a});if(r){let{placeUserByReferralType:a}=await t.e(2746).then(t.bind(t,2746)),s="general";"left"===e.placementSide?s="left":"right"===e.placementSide&&(s="right"),await a(r,o.id,s)}return{id:o.id,email:o.email,referralId:o.referralId,kycStatus:o.kycStatus}},y=async e=>{let r=await n.Gy.findByEmail(e.email);if(!r||!await d(e.password,r.password))throw Error("Invalid email or password");return{token:c({userId:r.id,email:r.email}),user:{id:r.id,email:r.email,referralId:r.referralId,kycStatus:r.kycStatus}}},h=e=>/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(e),x=async e=>{let r=await n.Gy.findById(e);return r?.role==="ADMIN"}},27910:e=>{e.exports=require("stream")},28354:e=>{e.exports=require("util")},29294:e=>{e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},32167:(e,r,t)=>{t.r(r),t.d(r,{patchFetch:()=>w,routeModule:()=>c,serverHooks:()=>f,workAsyncStorage:()=>m,workUnitAsyncStorage:()=>p});var a={};t.r(a),t.d(a,{GET:()=>d});var s=t(96559),i=t(48088),n=t(37719),o=t(32190),u=t(12909),l=t(31183);async function d(e){try{let{authenticated:r,user:t}=await (0,u.b9)(e);if(!r||!t)return o.NextResponse.json({success:!1,error:"Authentication required"},{status:401});if("ADMIN"!==t.role)return o.NextResponse.json({success:!1,error:"Admin access required"},{status:403});let{searchParams:a}=new URL(e.url),s=a.get("dateRange")||"30d",i={};if("all"!==s){let e=parseInt(s.replace("d",""));i={gte:new Date(Date.now()-24*e*36e5)}}let n={type:"DIRECT_REFERRAL",status:"COMPLETED"};"all"!==s&&(n.createdAt=i);let d=await l.prisma.transaction.aggregate({where:n,_count:{id:!0},_sum:{amount:!0},_avg:{amount:!0}}),c=new Date(Date.now()-6048e5),m=await l.prisma.transaction.count({where:{...n,createdAt:{gte:c}}}),p=await l.prisma.transaction.groupBy({by:["userId"],where:n,_sum:{amount:!0},_count:{id:!0},orderBy:{_sum:{amount:"desc"}},take:10}),f=await Promise.all(p.map(async e=>{let r=await l.prisma.user.findUnique({where:{id:e.userId},select:{id:!0,email:!0,firstName:!0,lastName:!0}});return{userId:e.userId,user:r||{id:e.userId,email:"Unknown",firstName:"Unknown",lastName:"User"},totalEarned:e._sum.amount||0,commissionCount:e._count.id}})),w={totalCommissions:d._count.id||0,totalAmount:d._sum.amount||0,averageCommission:d._avg.amount||0,topEarners:f,recentActivity:m};return o.NextResponse.json({success:!0,data:w})}catch(e){return console.error("Referral commission stats fetch error:",e),o.NextResponse.json({success:!1,error:"Failed to fetch referral commission stats"},{status:500})}}let c=new s.AppRouteRouteModule({definition:{kind:i.RouteKind.APP_ROUTE,page:"/api/admin/referral-commissions/stats/route",pathname:"/api/admin/referral-commissions/stats",filename:"route",bundlePath:"app/api/admin/referral-commissions/stats/route"},resolvedPagePath:"C:\\Users\\<USER>\\Desktop\\Hash_Minings\\hashcorex\\src\\app\\api\\admin\\referral-commissions\\stats\\route.ts",nextConfigOutput:"",userland:a}),{workAsyncStorage:m,workUnitAsyncStorage:p,serverHooks:f}=c;function w(){return(0,n.patchFetch)({workAsyncStorage:m,workUnitAsyncStorage:p})}},44870:e=>{e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},55511:e=>{e.exports=require("crypto")},63033:e=>{e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},79428:e=>{e.exports=require("buffer")},96330:e=>{e.exports=require("@prisma/client")}};var r=require("../../../../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),a=r.X(0,[4447,580,5315,3306],()=>t(32167));module.exports=a})();