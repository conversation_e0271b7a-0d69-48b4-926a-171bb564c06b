{"version": 3, "middleware": {"/": {"files": ["server/edge-runtime-webpack.js", "server/src/middleware.js"], "name": "src/middleware", "page": "/", "matchers": [{"regexp": "^(?:\\/(_next\\/data\\/[^/]{1,}))?(?:\\/((?!api|_next\\/static|_next\\/image|favicon.ico|uploads|crypto-icons).*))(\\.json)?[\\/#\\?]?$", "originalSource": "/((?!api|_next/static|_next/image|favicon.ico|uploads|crypto-icons).*)"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "shusEG9Yi4gbf0m6rgwdK", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "JbkaL9WKBPyo3Ykt65oQt4RvobSFsc6k70HnVTZ4FfQ=", "__NEXT_PREVIEW_MODE_ID": "9cf2eb2e0524398f7012193ef496bdbe", "__NEXT_PREVIEW_MODE_ENCRYPTION_KEY": "af0d0f021e512d6bbc1dc2d33d43b7e186dbb4c842b5a6a58fd0914622760d7b", "__NEXT_PREVIEW_MODE_SIGNING_KEY": "4ce2dbf8066e022c2a8f1138bcf0670cc1baa60e5a00f4140d20f2e248a9a347"}}}, "functions": {}, "sortedMiddleware": ["/"]}