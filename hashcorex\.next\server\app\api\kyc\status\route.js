"use strict";(()=>{var e={};e.id=9946,e.ids=[9946],e.modules={3295:e=>{e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},12909:(e,t,r)=>{r.d(t,{DT:()=>w,DY:()=>f,HU:()=>c,Lx:()=>h,b9:()=>y,qc:()=>x});var a=r(85663),s=r(43205),i=r.n(s),n=r(6710);let o=process.env.JWT_SECRET||"fallback-secret-key",d=process.env.JWT_EXPIRES_IN||"30d",u=async e=>await a.Ay.hash(e,12),l=async(e,t)=>await a.Ay.compare(e,t),c=e=>i().sign(e,o,{expiresIn:d}),p=e=>{try{return i().verify(e,o)}catch(e){return null}},m=()=>{let e="ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789",t="HC";for(let r=0;r<8;r++)t+=e.charAt(Math.floor(Math.random()*e.length));return t},y=async e=>{let t=e.headers.get("authorization")?.replace("Bearer ","")||e.cookies.get("auth-token")?.value;if(!t)return{authenticated:!1,user:null};let r=p(t);if(!r)return{authenticated:!1,user:null};let a=await n.Gy.findByEmail(r.email);return a?{authenticated:!0,user:a}:{authenticated:!1,user:null}},f=async e=>{let t,a;if(await n.Gy.findByEmail(e.email))throw Error("User already exists with this email");if(e.referralCode){let r=await n.Gy.findByReferralId(e.referralCode);if(!r)throw Error("Invalid referral code");t=r.id}let s=await u(e.password),i=!1;do a=m(),i=!await n.Gy.findByReferralId(a);while(!i);let o=await n.Gy.create({email:e.email,firstName:e.firstName,lastName:e.lastName,password:s,referralId:a});if(t){let{placeUserByReferralType:a}=await r.e(2746).then(r.bind(r,2746)),s="general";"left"===e.placementSide?s="left":"right"===e.placementSide&&(s="right"),await a(t,o.id,s)}return{id:o.id,email:o.email,referralId:o.referralId,kycStatus:o.kycStatus}},h=async e=>{let t=await n.Gy.findByEmail(e.email);if(!t||!await l(e.password,t.password))throw Error("Invalid email or password");return{token:c({userId:t.id,email:t.email}),user:{id:t.id,email:t.email,referralId:t.referralId,kycStatus:t.kycStatus}}},w=e=>/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(e),x=async e=>{let t=await n.Gy.findById(e);return t?.role==="ADMIN"}},27910:e=>{e.exports=require("stream")},28354:e=>{e.exports=require("util")},29294:e=>{e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},44130:(e,t,r)=>{r.r(t),r.d(t,{patchFetch:()=>f,routeModule:()=>c,serverHooks:()=>y,workAsyncStorage:()=>p,workUnitAsyncStorage:()=>m});var a={};r.r(a),r.d(a,{GET:()=>l});var s=r(96559),i=r(48088),n=r(37719),o=r(32190),d=r(12909),u=r(31183);async function l(e){try{let{authenticated:t,user:r}=await (0,d.b9)(e);if(!t||!r)return o.NextResponse.json({success:!1,error:"Not authenticated"},{status:401});let a=await u.prisma.kYCDocument.findMany({where:{userId:r.id},orderBy:{createdAt:"desc"}}),s=a.some(e=>"SELFIE"===e.documentType),i=a.filter(e=>"ID_DOCUMENT"===e.documentType),n=!1,l=null,c=[];if(i.length>0)if(l=i[0].idType,"PASSPORT"===l)n=i.some(e=>"FRONT"===e.documentSide),c=["passport_front","selfie"];else{let e=i.some(e=>"FRONT"===e.documentSide),t=i.some(e=>"BACK"===e.documentSide);n=e&&t,c=["id_front","id_back","selfie"]}let p=c.length,m=0;s&&m++,i.some(e=>"FRONT"===e.documentSide)&&m++,"PASSPORT"!==l&&i.some(e=>"BACK"===e.documentSide)&&m++;let y=p>0?Math.round(m/p*100):0,f=n&&s&&"PENDING"!==r.kycStatus&&"APPROVED"!==r.kycStatus,h=a.filter(e=>"REJECTED"===e.status&&e.rejectionReason);return o.NextResponse.json({success:!0,data:{status:r.kycStatus,completionPercentage:y,readyForSubmission:f,documents:a.map(e=>({id:e.id,documentType:e.documentType,idType:e.idType,documentSide:e.documentSide,status:e.status,rejectionReason:e.rejectionReason,createdAt:e.createdAt,reviewedAt:e.reviewedAt})),requirements:{idType:l,requiredDocuments:c,hasSelfie:s,hasCompleteIdDocuments:n},rejectionReasons:h.map(e=>({documentType:e.documentType,idType:e.idType,documentSide:e.documentSide,reason:e.rejectionReason}))}})}catch(e){return console.error("KYC status fetch error:",e),o.NextResponse.json({success:!1,error:"Failed to fetch KYC status"},{status:500})}}let c=new s.AppRouteRouteModule({definition:{kind:i.RouteKind.APP_ROUTE,page:"/api/kyc/status/route",pathname:"/api/kyc/status",filename:"route",bundlePath:"app/api/kyc/status/route"},resolvedPagePath:"C:\\Users\\<USER>\\Desktop\\Hash_Minings\\hashcorex\\src\\app\\api\\kyc\\status\\route.ts",nextConfigOutput:"",userland:a}),{workAsyncStorage:p,workUnitAsyncStorage:m,serverHooks:y}=c;function f(){return(0,n.patchFetch)({workAsyncStorage:p,workUnitAsyncStorage:m})}},44870:e=>{e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},55511:e=>{e.exports=require("crypto")},63033:e=>{e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},79428:e=>{e.exports=require("buffer")},96330:e=>{e.exports=require("@prisma/client")}};var t=require("../../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),a=t.X(0,[4447,580,5315,3306],()=>r(44130));module.exports=a})();