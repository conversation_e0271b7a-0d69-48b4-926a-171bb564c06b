"use strict";(()=>{var e={};e.id=2681,e.ids=[2681],e.modules={3295:e=>{e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},12909:(e,r,t)=>{t.d(r,{DT:()=>w,DY:()=>f,HU:()=>c,Lx:()=>h,b9:()=>m,qc:()=>I});var a=t(85663),s=t(43205),i=t.n(s),n=t(6710);let o=process.env.JWT_SECRET||"fallback-secret-key",d=process.env.JWT_EXPIRES_IN||"30d",u=async e=>await a.Ay.hash(e,12),l=async(e,r)=>await a.Ay.compare(e,r),c=e=>i().sign(e,o,{expiresIn:d}),p=e=>{try{return i().verify(e,o)}catch(e){return null}},y=()=>{let e="ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789",r="HC";for(let t=0;t<8;t++)r+=e.charAt(Math.floor(Math.random()*e.length));return r},m=async e=>{let r=e.headers.get("authorization")?.replace("Bearer ","")||e.cookies.get("auth-token")?.value;if(!r)return{authenticated:!1,user:null};let t=p(r);if(!t)return{authenticated:!1,user:null};let a=await n.Gy.findByEmail(t.email);return a?{authenticated:!0,user:a}:{authenticated:!1,user:null}},f=async e=>{let r,a;if(await n.Gy.findByEmail(e.email))throw Error("User already exists with this email");if(e.referralCode){let t=await n.Gy.findByReferralId(e.referralCode);if(!t)throw Error("Invalid referral code");r=t.id}let s=await u(e.password),i=!1;do a=y(),i=!await n.Gy.findByReferralId(a);while(!i);let o=await n.Gy.create({email:e.email,firstName:e.firstName,lastName:e.lastName,password:s,referralId:a});if(r){let{placeUserByReferralType:a}=await t.e(2746).then(t.bind(t,2746)),s="general";"left"===e.placementSide?s="left":"right"===e.placementSide&&(s="right"),await a(r,o.id,s)}return{id:o.id,email:o.email,referralId:o.referralId,kycStatus:o.kycStatus}},h=async e=>{let r=await n.Gy.findByEmail(e.email);if(!r||!await l(e.password,r.password))throw Error("Invalid email or password");return{token:c({userId:r.id,email:r.email}),user:{id:r.id,email:r.email,referralId:r.referralId,kycStatus:r.kycStatus}}},w=e=>/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(e),I=async e=>{let r=await n.Gy.findById(e);return r?.role==="ADMIN"}},27910:e=>{e.exports=require("stream")},28354:e=>{e.exports=require("util")},29294:e=>{e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},39461:(e,r,t)=>{t.r(r),t.d(r,{patchFetch:()=>f,routeModule:()=>c,serverHooks:()=>m,workAsyncStorage:()=>p,workUnitAsyncStorage:()=>y});var a={};t.r(a),t.d(a,{GET:()=>l});var s=t(96559),i=t(48088),n=t(37719),o=t(32190),d=t(31183),u=t(12909);async function l(e,{params:r}){try{let{authenticated:t,user:a}=await (0,u.b9)(e);if(!t||!a)return o.NextResponse.json({success:!1,error:"Unauthorized"},{status:401});if(!await (0,u.qc)(a.id))return o.NextResponse.json({success:!1,error:"Admin access required"},{status:403});let{userId:s}=await r,i=await d.prisma.transaction.findMany({where:{userId:s,type:"BINARY_BONUS",status:"COMPLETED"},include:{user:{select:{email:!0,firstName:!0,lastName:!0}}},orderBy:{createdAt:"desc"}}),n=await Promise.all(i.map(async e=>{let r=await d.prisma.systemLog.findFirst({where:{action:{in:["BINARY_MATCHING_PROCESSED","MANUAL_BINARY_MATCHING_TRIGGERED"]},userId:s,createdAt:{gte:new Date(e.createdAt.getTime()-6e4),lte:new Date(e.createdAt.getTime()+6e4)}},orderBy:{createdAt:"desc"}}),t=e.description?.includes("Manual")||r?.action==="MANUAL_BINARY_MATCHING_TRIGGERED";return{id:e.id,userId:e.userId,user:e.user,matchedPoints:e.amount/10,payout:e.amount,matchDate:e.createdAt,type:t?"MANUAL":"WEEKLY",description:e.description,leftPointsBefore:0,rightPointsBefore:0,leftPointsAfter:0,rightPointsAfter:0}}));return o.NextResponse.json({success:!0,data:n})}catch(e){return console.error("Binary points user history fetch error:",e),o.NextResponse.json({success:!1,error:"Failed to fetch user binary match history"},{status:500})}}let c=new s.AppRouteRouteModule({definition:{kind:i.RouteKind.APP_ROUTE,page:"/api/admin/binary-points/user-history/[userId]/route",pathname:"/api/admin/binary-points/user-history/[userId]",filename:"route",bundlePath:"app/api/admin/binary-points/user-history/[userId]/route"},resolvedPagePath:"C:\\Users\\<USER>\\Desktop\\Hash_Minings\\hashcorex\\src\\app\\api\\admin\\binary-points\\user-history\\[userId]\\route.ts",nextConfigOutput:"",userland:a}),{workAsyncStorage:p,workUnitAsyncStorage:y,serverHooks:m}=c;function f(){return(0,n.patchFetch)({workAsyncStorage:p,workUnitAsyncStorage:y})}},44870:e=>{e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},55511:e=>{e.exports=require("crypto")},63033:e=>{e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},79428:e=>{e.exports=require("buffer")},96330:e=>{e.exports=require("@prisma/client")}};var r=require("../../../../../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),a=r.X(0,[4447,580,5315,3306],()=>t(39461));module.exports=a})();