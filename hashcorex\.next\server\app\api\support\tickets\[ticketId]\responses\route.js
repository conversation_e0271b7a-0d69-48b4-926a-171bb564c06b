"use strict";(()=>{var e={};e.id=6124,e.ids=[6124],e.modules={3295:e=>{e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},12909:(e,r,t)=>{t.d(r,{DT:()=>w,DY:()=>y,HU:()=>c,Lx:()=>h,b9:()=>m,qc:()=>x});var s=t(85663),a=t(43205),i=t.n(a),n=t(6710);let o=process.env.JWT_SECRET||"fallback-secret-key",u=process.env.JWT_EXPIRES_IN||"30d",d=async e=>await s.Ay.hash(e,12),l=async(e,r)=>await s.Ay.compare(e,r),c=e=>i().sign(e,o,{expiresIn:u}),p=e=>{try{return i().verify(e,o)}catch(e){return null}},f=()=>{let e="ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789",r="HC";for(let t=0;t<8;t++)r+=e.charAt(Math.floor(Math.random()*e.length));return r},m=async e=>{let r=e.headers.get("authorization")?.replace("Bearer ","")||e.cookies.get("auth-token")?.value;if(!r)return{authenticated:!1,user:null};let t=p(r);if(!t)return{authenticated:!1,user:null};let s=await n.Gy.findByEmail(t.email);return s?{authenticated:!0,user:s}:{authenticated:!1,user:null}},y=async e=>{let r,s;if(await n.Gy.findByEmail(e.email))throw Error("User already exists with this email");if(e.referralCode){let t=await n.Gy.findByReferralId(e.referralCode);if(!t)throw Error("Invalid referral code");r=t.id}let a=await d(e.password),i=!1;do s=f(),i=!await n.Gy.findByReferralId(s);while(!i);let o=await n.Gy.create({email:e.email,firstName:e.firstName,lastName:e.lastName,password:a,referralId:s});if(r){let{placeUserByReferralType:s}=await t.e(2746).then(t.bind(t,2746)),a="general";"left"===e.placementSide?a="left":"right"===e.placementSide&&(a="right"),await s(r,o.id,a)}return{id:o.id,email:o.email,referralId:o.referralId,kycStatus:o.kycStatus}},h=async e=>{let r=await n.Gy.findByEmail(e.email);if(!r||!await l(e.password,r.password))throw Error("Invalid email or password");return{token:c({userId:r.id,email:r.email}),user:{id:r.id,email:r.email,referralId:r.referralId,kycStatus:r.kycStatus}}},w=e=>/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(e),x=async e=>{let r=await n.Gy.findById(e);return r?.role==="ADMIN"}},27820:(e,r,t)=>{t.r(r),t.d(r,{patchFetch:()=>y,routeModule:()=>c,serverHooks:()=>m,workAsyncStorage:()=>p,workUnitAsyncStorage:()=>f});var s={};t.r(s),t.d(s,{POST:()=>l});var a=t(96559),i=t(48088),n=t(37719),o=t(32190),u=t(12909),d=t(6710);async function l(e,{params:r}){try{let{authenticated:t,user:s}=await (0,u.b9)(e);if(!t||!s)return o.NextResponse.json({success:!1,error:"Not authenticated"},{status:401});let{ticketId:a}=await r,{message:i}=await e.json();if(!i||!i.trim())return o.NextResponse.json({success:!1,error:"Message is required"},{status:400});let n=await d.OZ.findById(a);if(!n)return o.NextResponse.json({success:!1,error:"Ticket not found"},{status:404});if(n.userId!==s.id)return o.NextResponse.json({success:!1,error:"Access denied"},{status:403});if("CLOSED"===n.status)return o.NextResponse.json({success:!1,error:"Cannot add response to closed ticket"},{status:400});let l=await d.o1.create({ticketId:a,userId:s.id,message:i.trim(),isAdmin:!1});return"OPEN"===n.status&&await d.OZ.updateStatus(a,"IN_PROGRESS"),o.NextResponse.json({success:!0,data:l,message:"Response added successfully"})}catch(e){return console.error("Ticket response creation error:",e),o.NextResponse.json({success:!1,error:"Failed to add response"},{status:500})}}let c=new a.AppRouteRouteModule({definition:{kind:i.RouteKind.APP_ROUTE,page:"/api/support/tickets/[ticketId]/responses/route",pathname:"/api/support/tickets/[ticketId]/responses",filename:"route",bundlePath:"app/api/support/tickets/[ticketId]/responses/route"},resolvedPagePath:"C:\\Users\\<USER>\\Desktop\\Hash_Minings\\hashcorex\\src\\app\\api\\support\\tickets\\[ticketId]\\responses\\route.ts",nextConfigOutput:"",userland:s}),{workAsyncStorage:p,workUnitAsyncStorage:f,serverHooks:m}=c;function y(){return(0,n.patchFetch)({workAsyncStorage:p,workUnitAsyncStorage:f})}},27910:e=>{e.exports=require("stream")},28354:e=>{e.exports=require("util")},29294:e=>{e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},44870:e=>{e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},55511:e=>{e.exports=require("crypto")},63033:e=>{e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},79428:e=>{e.exports=require("buffer")},96330:e=>{e.exports=require("@prisma/client")}};var r=require("../../../../../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),s=r.X(0,[4447,580,5315,3306],()=>t(27820));module.exports=s})();