"use strict";(()=>{var e={};e.id=1612,e.ids=[1612],e.modules={3295:e=>{e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},10974:(e,r,t)=>{t.d(r,{Oj:()=>a});function a(e){let r=[],t=[],a=e.length>=8;t.push({valid:a,message:"At least 8 characters long"}),a||r.push("Password must be at least 8 characters long");let s=/[A-Z]/.test(e);t.push({valid:s,message:"At least one uppercase letter"}),s||r.push("Password must contain at least one uppercase letter");let i=/[a-z]/.test(e);t.push({valid:i,message:"At least one lowercase letter"}),i||r.push("Password must contain at least one lowercase letter");let n=/\d/.test(e);t.push({valid:n,message:"At least one number"}),n||r.push("Password must contain at least one number");let o=/[!@#$%^&*(),.?":{}|<>]/.test(e);return t.push({valid:o,message:"At least one special character"}),o||r.push("Password must contain at least one special character"),{isValid:0===r.length,errors:r,checks:t}}},12909:(e,r,t)=>{t.d(r,{DT:()=>g,DY:()=>f,HU:()=>c,Lx:()=>w,b9:()=>h,qc:()=>y});var a=t(85663),s=t(43205),i=t.n(s),n=t(6710);let o=process.env.JWT_SECRET||"fallback-secret-key",l=process.env.JWT_EXPIRES_IN||"30d",u=async e=>await a.Ay.hash(e,12),d=async(e,r)=>await a.Ay.compare(e,r),c=e=>i().sign(e,o,{expiresIn:l}),p=e=>{try{return i().verify(e,o)}catch(e){return null}},m=()=>{let e="ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789",r="HC";for(let t=0;t<8;t++)r+=e.charAt(Math.floor(Math.random()*e.length));return r},h=async e=>{let r=e.headers.get("authorization")?.replace("Bearer ","")||e.cookies.get("auth-token")?.value;if(!r)return{authenticated:!1,user:null};let t=p(r);if(!t)return{authenticated:!1,user:null};let a=await n.Gy.findByEmail(t.email);return a?{authenticated:!0,user:a}:{authenticated:!1,user:null}},f=async e=>{let r,a;if(await n.Gy.findByEmail(e.email))throw Error("User already exists with this email");if(e.referralCode){let t=await n.Gy.findByReferralId(e.referralCode);if(!t)throw Error("Invalid referral code");r=t.id}let s=await u(e.password),i=!1;do a=m(),i=!await n.Gy.findByReferralId(a);while(!i);let o=await n.Gy.create({email:e.email,firstName:e.firstName,lastName:e.lastName,password:s,referralId:a});if(r){let{placeUserByReferralType:a}=await t.e(2746).then(t.bind(t,2746)),s="general";"left"===e.placementSide?s="left":"right"===e.placementSide&&(s="right"),await a(r,o.id,s)}return{id:o.id,email:o.email,referralId:o.referralId,kycStatus:o.kycStatus}},w=async e=>{let r=await n.Gy.findByEmail(e.email);if(!r||!await d(e.password,r.password))throw Error("Invalid email or password");return{token:c({userId:r.id,email:r.email}),user:{id:r.id,email:r.email,referralId:r.referralId,kycStatus:r.kycStatus}}},g=e=>/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(e),y=async e=>{let r=await n.Gy.findById(e);return r?.role==="ADMIN"}},27910:e=>{e.exports=require("stream")},28354:e=>{e.exports=require("util")},29294:e=>{e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},44870:e=>{e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},55511:e=>{e.exports=require("crypto")},63033:e=>{e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},79428:e=>{e.exports=require("buffer")},96330:e=>{e.exports=require("@prisma/client")},97133:(e,r,t)=>{t.r(r),t.d(r,{patchFetch:()=>w,routeModule:()=>p,serverHooks:()=>f,workAsyncStorage:()=>m,workUnitAsyncStorage:()=>h});var a={};t.r(a),t.d(a,{POST:()=>c});var s=t(96559),i=t(48088),n=t(37719),o=t(32190),l=t(12909),u=t(10974),d=t(6710);async function c(e){try{let{email:r,firstName:t,lastName:a,password:s,confirmPassword:i,referralCode:n,otp:c}=await e.json();if(!r||!t||!a||!s||!i||!c)return o.NextResponse.json({success:!1,error:"All fields including OTP are required"},{status:400});if(!(0,l.DT)(r))return o.NextResponse.json({success:!1,error:"Invalid email format"},{status:400});if(s!==i)return o.NextResponse.json({success:!1,error:"Passwords do not match"},{status:400});let p=(0,u.Oj)(s);if(!p.isValid)return o.NextResponse.json({success:!1,error:p.errors.join(", ")},{status:400});let m=await d.oV.findValid(r,"email_verification");if(!m||m.otp!==c)return o.NextResponse.json({success:!1,error:"Invalid or expired OTP. Please request a new one."},{status:400});await d.oV.verify(m.id);let h=new URL(e.url).searchParams.get("side"),f=await (0,l.DY)({email:r,firstName:t,lastName:a,password:s,referralCode:n,placementSide:h||void 0});await d.AJ.create({action:"USER_REGISTERED",userId:f.id,details:{email:f.email,referralCode:n||null},ipAddress:e.headers.get("x-forwarded-for")||"unknown",userAgent:e.headers.get("user-agent")||"unknown"});let w=(0,l.HU)({userId:f.id,email:f.email}),g=o.NextResponse.json({success:!0,message:"Registration successful",data:{user:f,token:w}});return g.cookies.set("auth-token",w,{httpOnly:!0,secure:!0,sameSite:"strict",maxAge:2592e3,path:"/"}),g}catch(e){return console.error("Registration error:",e),o.NextResponse.json({success:!1,error:e.message||"Registration failed"},{status:400})}}let p=new s.AppRouteRouteModule({definition:{kind:i.RouteKind.APP_ROUTE,page:"/api/auth/register/route",pathname:"/api/auth/register",filename:"route",bundlePath:"app/api/auth/register/route"},resolvedPagePath:"C:\\Users\\<USER>\\Desktop\\Hash_Minings\\hashcorex\\src\\app\\api\\auth\\register\\route.ts",nextConfigOutput:"",userland:a}),{workAsyncStorage:m,workUnitAsyncStorage:h,serverHooks:f}=p;function w(){return(0,n.patchFetch)({workAsyncStorage:m,workUnitAsyncStorage:h})}}};var r=require("../../../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),a=r.X(0,[4447,580,5315,3306],()=>t(97133));module.exports=a})();