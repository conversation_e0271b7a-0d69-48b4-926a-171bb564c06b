"use strict";(()=>{var e={};e.id=5212,e.ids=[5212],e.modules={3295:e=>{e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},29294:e=>{e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},44870:e=>{e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},63033:e=>{e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},78459:(e,r,t)=>{t.r(r),t.d(r,{patchFetch:()=>m,routeModule:()=>u,serverHooks:()=>E,workAsyncStorage:()=>p,workUnitAsyncStorage:()=>g});var a={};t.r(a),t.d(a,{POST:()=>l});var s=t(96559),o=t(48088),i=t(37719),n=t(32190),d=t(6710),c=t(82629);async function l(e){try{let{email:r,otp:t,purpose:a="email_verification"}=await e.json();if(!r||!t)return n.NextResponse.json({success:!1,error:"Email and OTP are required"},{status:400});let s=await d.oV.findValid(r,a);if(!s)return n.NextResponse.json({success:!1,error:"Invalid or expired OTP"},{status:400});if(s.otp!==t)return n.NextResponse.json({success:!1,error:"Invalid OTP"},{status:400});return await d.oV.verify(s.id),n.NextResponse.json({success:!0,message:"OTP verified successfully",data:{email:r,verified:!0}})}catch(r){return console.error("Verify OTP error:",r),await c.v5.logApiError(e,r,"VERIFY_OTP_ERROR"),n.NextResponse.json({success:!1,error:"Internal server error"},{status:500})}}let u=new s.AppRouteRouteModule({definition:{kind:o.RouteKind.APP_ROUTE,page:"/api/auth/verify-otp/route",pathname:"/api/auth/verify-otp",filename:"route",bundlePath:"app/api/auth/verify-otp/route"},resolvedPagePath:"C:\\Users\\<USER>\\Desktop\\Hash_Minings\\hashcorex\\src\\app\\api\\auth\\verify-otp\\route.ts",nextConfigOutput:"",userland:a}),{workAsyncStorage:p,workUnitAsyncStorage:g,serverHooks:E}=u;function m(){return(0,i.patchFetch)({workAsyncStorage:p,workUnitAsyncStorage:g})}},82629:(e,r,t)=>{t.d(r,{v5:()=>s});var a=t(6710);class s{static async logError(e){try{let r=e.error instanceof Error?e.error:Error(String(e.error)),t={action:e.action,userId:e.userId,adminId:e.adminId,details:{message:r.message,stack:r.stack,name:r.name,requestUrl:e.requestUrl,requestMethod:e.requestMethod,requestBody:e.requestBody,additionalData:e.additionalData,timestamp:new Date().toISOString()},ipAddress:e.ipAddress,userAgent:e.userAgent};await a.AJ.create(t),console.error(`[${e.action}] Error logged:`,{message:r.message,stack:r.stack,userId:e.userId,adminId:e.adminId,url:e.requestUrl})}catch(r){console.error("Failed to log error to database:",r),console.error("Original error:",e.error)}}static async logApiError(e,r,t,a,s,o){try{let i=null;try{if("GET"!==e.method&&e.headers.get("content-type")?.includes("application/json")){let r=e.clone();(i=await r.json()).password&&(i.password="[REDACTED]"),i.token&&(i.token="[REDACTED]"),i.apiKey&&(i.apiKey="[REDACTED]")}}catch(e){}await this.logError({action:t,error:r,userId:a,adminId:s,ipAddress:e.headers.get("x-forwarded-for")||e.headers.get("x-real-ip")||"unknown",userAgent:e.headers.get("user-agent")||"unknown",requestUrl:e.url,requestMethod:e.method,requestBody:i,additionalData:o})}catch(e){console.error("Failed to log API error:",e),console.error("Original error:",r)}}static async logClientError(e){try{await a.AJ.create({action:"CLIENT_ERROR",userId:e.userId,details:{message:e.message,stack:e.stack,url:e.url,timestamp:e.timestamp,additionalData:e.additionalData},userAgent:e.userAgent})}catch(e){console.error("Failed to log client error:",e)}}static async logAuthError(e,r,t,a){await this.logApiError(e,r,"AUTH_ERROR",void 0,void 0,{email:t,...a})}static async logDatabaseError(e,r,t,s,o){try{await a.AJ.create({action:"DATABASE_ERROR",userId:s,details:{message:e instanceof Error?e.message:String(e),stack:e instanceof Error?e.stack:void 0,operation:r,table:t,timestamp:new Date().toISOString(),additionalData:o}})}catch(r){console.error("Failed to log database error:",r),console.error("Original error:",e)}}static async logBusinessError(e,r,t,a,s){await this.logError({action:"BUSINESS_LOGIC_ERROR",error:e,userId:t,adminId:a,additionalData:{operation:r,...s}})}static async logExternalApiError(e,r,t,a,s){await this.logError({action:"EXTERNAL_API_ERROR",error:e,userId:a,additionalData:{service:r,endpoint:t,...s}})}static async logValidationError(e,r,t,a,s){await this.logError({action:"VALIDATION_ERROR",error:e,userId:a,additionalData:{field:r,value:t,...s}})}}},96330:e=>{e.exports=require("@prisma/client")}};var r=require("../../../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),a=r.X(0,[4447,580,3306],()=>t(78459));module.exports=a})();