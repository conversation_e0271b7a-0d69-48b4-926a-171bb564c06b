(()=>{var e={};e.id=5890,e.ids=[5890],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},7650:(e,r,t)=>{"use strict";t.r(r),t.d(r,{GlobalError:()=>a.a,__next_app__:()=>l,pages:()=>c,routeModule:()=>p,tree:()=>u});var s=t(65239),n=t(48088),o=t(88170),a=t.n(o),i=t(30893),d={};for(let e in i)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(d[e]=()=>i[e]);t.d(r,d);let u={children:["",{children:["(auth)",{children:["forgot-password",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(t.bind(t,75990)),"C:\\Users\\<USER>\\Desktop\\Hash_Minings\\hashcorex\\src\\app\\(auth)\\forgot-password\\page.tsx"]}]},{}]},{"not-found":[()=>Promise.resolve().then(t.t.bind(t,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(t.t.bind(t,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(t.t.bind(t,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(t.bind(t,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(t.bind(t,94431)),"C:\\Users\\<USER>\\Desktop\\Hash_Minings\\hashcorex\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(t.t.bind(t,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(t.t.bind(t,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(t.t.bind(t,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(t.bind(t,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,c=["C:\\Users\\<USER>\\Desktop\\Hash_Minings\\hashcorex\\src\\app\\(auth)\\forgot-password\\page.tsx"],l={require:t,loadChunk:()=>Promise.resolve()},p=new s.AppPageRouteModule({definition:{kind:n.RouteKind.APP_PAGE,page:"/(auth)/forgot-password/page",pathname:"/forgot-password",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:u}})},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},16189:(e,r,t)=>{"use strict";var s=t(65773);t.o(s,"usePathname")&&t.d(r,{usePathname:function(){return s.usePathname}}),t.o(s,"useRouter")&&t.d(r,{useRouter:function(){return s.useRouter}}),t.o(s,"useSearchParams")&&t.d(r,{useSearchParams:function(){return s.useSearchParams}})},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},20666:(e,r,t)=>{"use strict";t.d(r,{CU:()=>u});var s=t(60687),n=t(43210),o=t(16189),a=t(57445),i=t(77849);let d=({children:e,requireAuth:r=!1,requireGuest:t=!1,redirectTo:d})=>{let{user:u,loading:c}=(0,a.A)(),l=(0,o.useRouter)(),p=(0,o.usePathname)();return((0,n.useEffect)(()=>{if(!c){if(r&&!u){let e=`/login?redirect=${encodeURIComponent(p)}`;l.replace(e);return}if(t&&u){let e=d||"/dashboard";l.replace(e);return}}},[u,c,r,t,l,p,d]),c)?(0,s.jsx)("div",{className:"min-h-screen flex items-center justify-center bg-gray-50",children:(0,s.jsxs)("div",{className:"text-center",children:[(0,s.jsx)(i.Rh,{}),(0,s.jsx)("p",{className:"mt-4 text-gray-600",children:"Checking authentication..."})]})}):r&&!u||t&&u?null:(0,s.jsx)(s.Fragment,{children:e})},u=({children:e,redirectTo:r})=>(0,s.jsx)(d,{requireGuest:!0,redirectTo:r,children:e})},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},31607:(e,r,t)=>{Promise.resolve().then(t.bind(t,82364))},33873:e=>{"use strict";e.exports=require("path")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},71359:(e,r,t)=>{Promise.resolve().then(t.bind(t,75990))},75990:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>s});let s=(0,t(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\app\\\\(auth)\\\\forgot-password\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Desktop\\Hash_Minings\\hashcorex\\src\\app\\(auth)\\forgot-password\\page.tsx","default")},79551:e=>{"use strict";e.exports=require("url")},82364:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>u});var s=t(60687),n=t(43210),o=t(16189),a=t(4780),i=t(20666);function d(){(0,o.useRouter)();let[e,r]=(0,n.useState)("email"),[t,i]=(0,n.useState)(""),[d,u]=(0,n.useState)(""),[c,l]=(0,n.useState)(""),[p,h]=(0,n.useState)(!1),[m,x]=(0,n.useState)(!1),[f,g]=(0,n.useState)(!1),[v,P]=(0,n.useState)(""),[b,j]=(0,n.useState)("");return(0,a.Oj)(d),(0,s.jsx)("div",{className:"min-h-screen",children:(0,s.jsxs)("div",{className:"container mx-auto p-4",children:[(0,s.jsx)("h1",{children:"Forgot Password"}),(0,s.jsx)("p",{children:"This page is under construction."})]})})}function u(){return(0,s.jsx)(i.CU,{redirectTo:"/dashboard",children:(0,s.jsx)(d,{})})}}};var r=require("../../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),s=r.X(0,[4447,1771,2356,3988],()=>t(7650));module.exports=s})();