/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/(auth)/forgot-password/page";
exports.ids = ["app/(auth)/forgot-password/page"];
exports.modules = {

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2F(auth)%2Fforgot-password%2Fpage&page=%2F(auth)%2Fforgot-password%2Fpage&appPaths=%2F(auth)%2Fforgot-password%2Fpage&pagePath=private-next-app-dir%2F(auth)%2Fforgot-password%2Fpage.tsx&appDir=C%3A%5CUsers%5Cdream%5CDesktop%5CHash_Minings%5Chashcorex%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cdream%5CDesktop%5CHash_Minings%5Chashcorex&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2F(auth)%2Fforgot-password%2Fpage&page=%2F(auth)%2Fforgot-password%2Fpage&appPaths=%2F(auth)%2Fforgot-password%2Fpage&pagePath=private-next-app-dir%2F(auth)%2Fforgot-password%2Fpage.tsx&appDir=C%3A%5CUsers%5Cdream%5CDesktop%5CHash_Minings%5Chashcorex%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cdream%5CDesktop%5CHash_Minings%5Chashcorex&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GlobalError: () => (/* reexport default from dynamic */ next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default.a),\n/* harmony export */   __next_app__: () => (/* binding */ __next_app__),\n/* harmony export */   pages: () => (/* binding */ pages),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   tree: () => (/* binding */ tree)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/route-modules/app-page/module.compiled */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/module.compiled.js?cc4a\");\n/* harmony import */ var next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/route-kind */ \"(rsc)/./node_modules/next/dist/server/route-kind.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/client/components/error-boundary */ \"(rsc)/./node_modules/next/dist/client/components/error-boundary.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/dist/server/app-render/entry-base */ \"(rsc)/./node_modules/next/dist/server/app-render/entry-base.js\");\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};\n/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__) if([\"default\",\"tree\",\"pages\",\"GlobalError\",\"__next_app__\",\"routeModule\"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__[__WEBPACK_IMPORT_KEY__]\n/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);\nconst module0 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/layout.tsx */ \"(rsc)/./src/app/layout.tsx\"));\nconst module1 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/not-found-error */ \"(rsc)/./node_modules/next/dist/client/components/not-found-error.js\", 23));\nconst module2 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/forbidden-error */ \"(rsc)/./node_modules/next/dist/client/components/forbidden-error.js\", 23));\nconst module3 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/unauthorized-error */ \"(rsc)/./node_modules/next/dist/client/components/unauthorized-error.js\", 23));\nconst module4 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/not-found-error */ \"(rsc)/./node_modules/next/dist/client/components/not-found-error.js\", 23));\nconst module5 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/forbidden-error */ \"(rsc)/./node_modules/next/dist/client/components/forbidden-error.js\", 23));\nconst module6 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/unauthorized-error */ \"(rsc)/./node_modules/next/dist/client/components/unauthorized-error.js\", 23));\nconst page7 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/(auth)/forgot-password/page.tsx */ \"(rsc)/./src/app/(auth)/forgot-password/page.tsx\"));\n\n\n// We inject the tree and pages here so that we can use them in the route\n// module.\nconst tree = {\n        children: [\n        '',\n        {\n        children: [\n        '(auth)',\n        {\n        children: [\n        'forgot-password',\n        {\n        children: ['__PAGE__', {}, {\n          page: [page7, \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\app\\\\(auth)\\\\forgot-password\\\\page.tsx\"],\n          \n        }]\n      },\n        {\n        \n        \n      }\n      ]\n      },\n        {\n        'not-found': [module4, \"next/dist/client/components/not-found-error\"],\n'forbidden': [module5, \"next/dist/client/components/forbidden-error\"],\n'unauthorized': [module6, \"next/dist/client/components/unauthorized-error\"],\n        metadata: {\n    icon: [(async (props) => (await Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! next-metadata-image-loader?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__ */ \"(rsc)/./node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__\"))).default(props))],\n    apple: [],\n    openGraph: [],\n    twitter: [],\n    manifest: undefined\n  }\n      }\n      ]\n      },\n        {\n        'layout': [module0, \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\app\\\\layout.tsx\"],\n'not-found': [module1, \"next/dist/client/components/not-found-error\"],\n'forbidden': [module2, \"next/dist/client/components/forbidden-error\"],\n'unauthorized': [module3, \"next/dist/client/components/unauthorized-error\"],\n        metadata: {\n    icon: [(async (props) => (await Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! next-metadata-image-loader?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__ */ \"(rsc)/./node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__\"))).default(props))],\n    apple: [],\n    openGraph: [],\n    twitter: [],\n    manifest: undefined\n  }\n      }\n      ]\n      }.children;\nconst pages = [\"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\app\\\\(auth)\\\\forgot-password\\\\page.tsx\"];\n\n\nconst __next_app_require__ = __webpack_require__\nconst __next_app_load_chunk__ = () => Promise.resolve()\nconst __next_app__ = {\n    require: __next_app_require__,\n    loadChunk: __next_app_load_chunk__\n};\n\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppPageRouteModule({\n    definition: {\n        kind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_PAGE,\n        page: \"/(auth)/forgot-password/page\",\n        pathname: \"/forgot-password\",\n        // The following aren't used in production.\n        bundlePath: '',\n        filename: '',\n        appPaths: []\n    },\n    userland: {\n        loaderTree: tree\n    }\n});\n\n//# sourceMappingURL=app-page.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2F(auth)%2Fforgot-password%2Fpage&page=%2F(auth)%2Fforgot-password%2Fpage&appPaths=%2F(auth)%2Fforgot-password%2Fpage&pagePath=private-next-app-dir%2F(auth)%2Fforgot-password%2Fpage.tsx&appDir=C%3A%5CUsers%5Cdream%5CDesktop%5CHash_Minings%5Chashcorex%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cdream%5CDesktop%5CHash_Minings%5Chashcorex&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cdream%5C%5CDesktop%5C%5CHash_Minings%5C%5Chashcorex%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cdream%5C%5CDesktop%5C%5CHash_Minings%5C%5Chashcorex%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cdream%5C%5CDesktop%5C%5CHash_Minings%5C%5Chashcorex%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cdream%5C%5CDesktop%5C%5CHash_Minings%5C%5Chashcorex%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cdream%5C%5CDesktop%5C%5CHash_Minings%5C%5Chashcorex%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cdream%5C%5CDesktop%5C%5CHash_Minings%5C%5Chashcorex%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cdream%5C%5CDesktop%5C%5CHash_Minings%5C%5Chashcorex%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cdream%5C%5CDesktop%5C%5CHash_Minings%5C%5Chashcorex%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!*************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cdream%5C%5CDesktop%5C%5CHash_Minings%5C%5Chashcorex%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cdream%5C%5CDesktop%5C%5CHash_Minings%5C%5Chashcorex%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cdream%5C%5CDesktop%5C%5CHash_Minings%5C%5Chashcorex%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cdream%5C%5CDesktop%5C%5CHash_Minings%5C%5Chashcorex%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cdream%5C%5CDesktop%5C%5CHash_Minings%5C%5Chashcorex%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cdream%5C%5CDesktop%5C%5CHash_Minings%5C%5Chashcorex%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cdream%5C%5CDesktop%5C%5CHash_Minings%5C%5Chashcorex%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cdream%5C%5CDesktop%5C%5CHash_Minings%5C%5Chashcorex%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \*************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-page.js */ \"(rsc)/./node_modules/next/dist/client/components/client-page.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-segment.js */ \"(rsc)/./node_modules/next/dist/client/components/client-segment.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/error-boundary.js */ \"(rsc)/./node_modules/next/dist/client/components/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js */ \"(rsc)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/layout-router.js */ \"(rsc)/./node_modules/next/dist/client/components/layout-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/async-metadata.js */ \"(rsc)/./node_modules/next/dist/client/components/metadata/async-metadata.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/metadata-boundary.js */ \"(rsc)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/render-from-template-context.js */ \"(rsc)/./node_modules/next/dist/client/components/render-from-template-context.js\", 23));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cdream%5C%5CDesktop%5C%5CHash_Minings%5C%5Chashcorex%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cdream%5C%5CDesktop%5C%5CHash_Minings%5C%5Chashcorex%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cdream%5C%5CDesktop%5C%5CHash_Minings%5C%5Chashcorex%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cdream%5C%5CDesktop%5C%5CHash_Minings%5C%5Chashcorex%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cdream%5C%5CDesktop%5C%5CHash_Minings%5C%5Chashcorex%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cdream%5C%5CDesktop%5C%5CHash_Minings%5C%5Chashcorex%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cdream%5C%5CDesktop%5C%5CHash_Minings%5C%5Chashcorex%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cdream%5C%5CDesktop%5C%5CHash_Minings%5C%5Chashcorex%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cdream%5C%5CDesktop%5C%5CHash_Minings%5C%5Chashcorex%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%2C%5C%22variable%5C%22%3A%5C%22--font-inter%5C%22%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cdream%5C%5CDesktop%5C%5CHash_Minings%5C%5Chashcorex%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cdream%5C%5CDesktop%5C%5CHash_Minings%5C%5Chashcorex%5C%5Csrc%5C%5Chooks%5C%5CuseAuth.tsx%22%2C%22ids%22%3A%5B%22AuthProvider%22%5D%7D&server=true!":
/*!**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cdream%5C%5CDesktop%5C%5CHash_Minings%5C%5Chashcorex%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%2C%5C%22variable%5C%22%3A%5C%22--font-inter%5C%22%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cdream%5C%5CDesktop%5C%5CHash_Minings%5C%5Chashcorex%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cdream%5C%5CDesktop%5C%5CHash_Minings%5C%5Chashcorex%5C%5Csrc%5C%5Chooks%5C%5CuseAuth.tsx%22%2C%22ids%22%3A%5B%22AuthProvider%22%5D%7D&server=true! ***!
  \**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/hooks/useAuth.tsx */ \"(rsc)/./src/hooks/useAuth.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkMlM0ElNUMlNUNVc2VycyU1QyU1Q2RyZWFtJTVDJTVDRGVza3RvcCU1QyU1Q0hhc2hfTWluaW5ncyU1QyU1Q2hhc2hjb3JleCU1QyU1Q25vZGVfbW9kdWxlcyU1QyU1Q25leHQlNUMlNUNmb250JTVDJTVDZ29vZ2xlJTVDJTVDdGFyZ2V0LmNzcyUzRiU3QiU1QyUyMnBhdGglNUMlMjIlM0ElNUMlMjJzcmMlNUMlNUMlNUMlNUNhcHAlNUMlNUMlNUMlNUNsYXlvdXQudHN4JTVDJTIyJTJDJTVDJTIyaW1wb3J0JTVDJTIyJTNBJTVDJTIySW50ZXIlNUMlMjIlMkMlNUMlMjJhcmd1bWVudHMlNUMlMjIlM0ElNUIlN0IlNUMlMjJzdWJzZXRzJTVDJTIyJTNBJTVCJTVDJTIybGF0aW4lNUMlMjIlNUQlMkMlNUMlMjJ2YXJpYWJsZSU1QyUyMiUzQSU1QyUyMi0tZm9udC1pbnRlciU1QyUyMiU3RCU1RCUyQyU1QyUyMnZhcmlhYmxlTmFtZSU1QyUyMiUzQSU1QyUyMmludGVyJTVDJTIyJTdEJTIyJTJDJTIyaWRzJTIyJTNBJTVCJTVEJTdEJm1vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkMlM0ElNUMlNUNVc2VycyU1QyU1Q2RyZWFtJTVDJTVDRGVza3RvcCU1QyU1Q0hhc2hfTWluaW5ncyU1QyU1Q2hhc2hjb3JleCU1QyU1Q3NyYyU1QyU1Q2FwcCU1QyU1Q2dsb2JhbHMuY3NzJTIyJTJDJTIyaWRzJTIyJTNBJTVCJTVEJTdEJm1vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkMlM0ElNUMlNUNVc2VycyU1QyU1Q2RyZWFtJTVDJTVDRGVza3RvcCU1QyU1Q0hhc2hfTWluaW5ncyU1QyU1Q2hhc2hjb3JleCU1QyU1Q3NyYyU1QyU1Q2hvb2tzJTVDJTVDdXNlQXV0aC50c3glMjIlMkMlMjJpZHMlMjIlM0ElNUIlMjJBdXRoUHJvdmlkZXIlMjIlNUQlN0Qmc2VydmVyPXRydWUhIiwibWFwcGluZ3MiOiJBQUFBLDBKQUFrSiIsInNvdXJjZXMiOlsiIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiLCB3ZWJwYWNrRXhwb3J0czogW1wiQXV0aFByb3ZpZGVyXCJdICovIFwiQzpcXFxcVXNlcnNcXFxcZHJlYW1cXFxcRGVza3RvcFxcXFxIYXNoX01pbmluZ3NcXFxcaGFzaGNvcmV4XFxcXHNyY1xcXFxob29rc1xcXFx1c2VBdXRoLnRzeFwiKTtcbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cdream%5C%5CDesktop%5C%5CHash_Minings%5C%5Chashcorex%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%2C%5C%22variable%5C%22%3A%5C%22--font-inter%5C%22%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cdream%5C%5CDesktop%5C%5CHash_Minings%5C%5Chashcorex%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cdream%5C%5CDesktop%5C%5CHash_Minings%5C%5Chashcorex%5C%5Csrc%5C%5Chooks%5C%5CuseAuth.tsx%22%2C%22ids%22%3A%5B%22AuthProvider%22%5D%7D&server=true!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cdream%5C%5CDesktop%5C%5CHash_Minings%5C%5Chashcorex%5C%5Csrc%5C%5Capp%5C%5C(auth)%5C%5Cforgot-password%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!*********************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cdream%5C%5CDesktop%5C%5CHash_Minings%5C%5Chashcorex%5C%5Csrc%5C%5Capp%5C%5C(auth)%5C%5Cforgot-password%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \*********************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/(auth)/forgot-password/page.tsx */ \"(rsc)/./src/app/(auth)/forgot-password/page.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkMlM0ElNUMlNUNVc2VycyU1QyU1Q2RyZWFtJTVDJTVDRGVza3RvcCU1QyU1Q0hhc2hfTWluaW5ncyU1QyU1Q2hhc2hjb3JleCU1QyU1Q3NyYyU1QyU1Q2FwcCU1QyU1QyhhdXRoKSU1QyU1Q2ZvcmdvdC1wYXNzd29yZCU1QyU1Q3BhZ2UudHN4JTIyJTJDJTIyaWRzJTIyJTNBJTVCJTVEJTdEJnNlcnZlcj10cnVlISIsIm1hcHBpbmdzIjoiQUFBQSw4TEFBb0kiLCJzb3VyY2VzIjpbIiJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQoLyogd2VicGFja01vZGU6IFwiZWFnZXJcIiAqLyBcIkM6XFxcXFVzZXJzXFxcXGRyZWFtXFxcXERlc2t0b3BcXFxcSGFzaF9NaW5pbmdzXFxcXGhhc2hjb3JleFxcXFxzcmNcXFxcYXBwXFxcXChhdXRoKVxcXFxmb3Jnb3QtcGFzc3dvcmRcXFxccGFnZS50c3hcIik7XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cdream%5C%5CDesktop%5C%5CHash_Minings%5C%5Chashcorex%5C%5Csrc%5C%5Capp%5C%5C(auth)%5C%5Cforgot-password%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__":
/*!**************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__ ***!
  \**************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var next_dist_lib_metadata_get_metadata_route__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/lib/metadata/get-metadata-route */ \"(rsc)/./node_modules/next/dist/lib/metadata/get-metadata-route.js\");\n/* harmony import */ var next_dist_lib_metadata_get_metadata_route__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_lib_metadata_get_metadata_route__WEBPACK_IMPORTED_MODULE_0__);\n  \n\n  /* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (async (props) => {\n    const imageData = {\"type\":\"image/x-icon\",\"sizes\":\"16x16\"}\n    const imageUrl = (0,next_dist_lib_metadata_get_metadata_route__WEBPACK_IMPORTED_MODULE_0__.fillMetadataSegment)(\".\", await props.params, \"favicon.ico\")\n\n    return [{\n      ...imageData,\n      url: imageUrl + \"\",\n    }]\n  });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LW1ldGFkYXRhLWltYWdlLWxvYWRlci5qcz90eXBlPWZhdmljb24mc2VnbWVudD0mYmFzZVBhdGg9JnBhZ2VFeHRlbnNpb25zPXRzeCZwYWdlRXh0ZW5zaW9ucz10cyZwYWdlRXh0ZW5zaW9ucz1qc3gmcGFnZUV4dGVuc2lvbnM9anMhLi9zcmMvYXBwL2Zhdmljb24uaWNvP19fbmV4dF9tZXRhZGF0YV9fIiwibWFwcGluZ3MiOiI7Ozs7OztBQUFBLEVBQWlGOztBQUVqRixFQUFFLGlFQUFlO0FBQ2pCLHVCQUF1QjtBQUN2QixxQkFBcUIsOEZBQW1COztBQUV4QztBQUNBO0FBQ0E7QUFDQSxLQUFLO0FBQ0wiLCJzb3VyY2VzIjpbIkM6XFxVc2Vyc1xcZHJlYW1cXERlc2t0b3BcXEhhc2hfTWluaW5nc1xcaGFzaGNvcmV4XFxzcmNcXGFwcFxcZmF2aWNvbi5pY28/X19uZXh0X21ldGFkYXRhX18iXSwic291cmNlc0NvbnRlbnQiOlsiICBpbXBvcnQgeyBmaWxsTWV0YWRhdGFTZWdtZW50IH0gZnJvbSAnbmV4dC9kaXN0L2xpYi9tZXRhZGF0YS9nZXQtbWV0YWRhdGEtcm91dGUnXG5cbiAgZXhwb3J0IGRlZmF1bHQgYXN5bmMgKHByb3BzKSA9PiB7XG4gICAgY29uc3QgaW1hZ2VEYXRhID0ge1widHlwZVwiOlwiaW1hZ2UveC1pY29uXCIsXCJzaXplc1wiOlwiMTZ4MTZcIn1cbiAgICBjb25zdCBpbWFnZVVybCA9IGZpbGxNZXRhZGF0YVNlZ21lbnQoXCIuXCIsIGF3YWl0IHByb3BzLnBhcmFtcywgXCJmYXZpY29uLmljb1wiKVxuXG4gICAgcmV0dXJuIFt7XG4gICAgICAuLi5pbWFnZURhdGEsXG4gICAgICB1cmw6IGltYWdlVXJsICsgXCJcIixcbiAgICB9XVxuICB9Il0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__\n");

/***/ }),

/***/ "(rsc)/./src/app/(auth)/forgot-password/page.tsx":
/*!*************************************************!*\
  !*** ./src/app/(auth)/forgot-password/page.tsx ***!
  \*************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react-server-dom-webpack/server.edge */ "(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-webpack-server-edge.js");
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__);

/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\app\\\\(auth)\\\\forgot-password\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"C:\\Users\\<USER>\\Desktop\\Hash_Minings\\hashcorex\\src\\app\\(auth)\\forgot-password\\page.tsx",
"default",
));


/***/ }),

/***/ "(rsc)/./src/app/globals.css":
/*!*****************************!*\
  !*** ./src/app/globals.css ***!
  \*****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"d1df8639729a\");\nif (false) {}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvYXBwL2dsb2JhbHMuY3NzIiwibWFwcGluZ3MiOiI7Ozs7QUFBQSxpRUFBZSxjQUFjO0FBQzdCLElBQUksS0FBVSxFQUFFLEVBQXVCIiwic291cmNlcyI6WyJDOlxcVXNlcnNcXGRyZWFtXFxEZXNrdG9wXFxIYXNoX01pbmluZ3NcXGhhc2hjb3JleFxcc3JjXFxhcHBcXGdsb2JhbHMuY3NzIl0sInNvdXJjZXNDb250ZW50IjpbImV4cG9ydCBkZWZhdWx0IFwiZDFkZjg2Mzk3MjlhXCJcbmlmIChtb2R1bGUuaG90KSB7IG1vZHVsZS5ob3QuYWNjZXB0KCkgfVxuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./src/app/globals.css\n");

/***/ }),

/***/ "(rsc)/./src/app/layout.tsx":
/*!****************************!*\
  !*** ./src/app/layout.tsx ***!
  \****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ RootLayout),\n/* harmony export */   metadata: () => (/* binding */ metadata)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_font_google_target_css_path_src_app_layout_tsx_import_Inter_arguments_subsets_latin_variable_font_inter_variableName_inter___WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/font/google/target.css?{\"path\":\"src\\\\app\\\\layout.tsx\",\"import\":\"Inter\",\"arguments\":[{\"subsets\":[\"latin\"],\"variable\":\"--font-inter\"}],\"variableName\":\"inter\"} */ \"(rsc)/./node_modules/next/font/google/target.css?{\\\"path\\\":\\\"src\\\\\\\\app\\\\\\\\layout.tsx\\\",\\\"import\\\":\\\"Inter\\\",\\\"arguments\\\":[{\\\"subsets\\\":[\\\"latin\\\"],\\\"variable\\\":\\\"--font-inter\\\"}],\\\"variableName\\\":\\\"inter\\\"}\");\n/* harmony import */ var next_font_google_target_css_path_src_app_layout_tsx_import_Inter_arguments_subsets_latin_variable_font_inter_variableName_inter___WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_font_google_target_css_path_src_app_layout_tsx_import_Inter_arguments_subsets_latin_variable_font_inter_variableName_inter___WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var _globals_css__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./globals.css */ \"(rsc)/./src/app/globals.css\");\n/* harmony import */ var _hooks_useAuth__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/hooks/useAuth */ \"(rsc)/./src/hooks/useAuth.tsx\");\n\n\n\n\nconst metadata = {\n    title: \"HashCoreX - Solar-Powered Cloud Mining\",\n    description: \"Sustainable cryptocurrency mining with solar energy. Earn daily ROI with our eco-friendly mining platform.\"\n};\nfunction RootLayout({ children }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"html\", {\n        lang: \"en\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"body\", {\n            className: `${(next_font_google_target_css_path_src_app_layout_tsx_import_Inter_arguments_subsets_latin_variable_font_inter_variableName_inter___WEBPACK_IMPORTED_MODULE_3___default().variable)} font-sans antialiased`,\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_hooks_useAuth__WEBPACK_IMPORTED_MODULE_2__.AuthProvider, {\n                children: children\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\app\\\\layout.tsx\",\n                lineNumber: 24,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\app\\\\layout.tsx\",\n            lineNumber: 23,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\app\\\\layout.tsx\",\n        lineNumber: 22,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvYXBwL2xheW91dC50c3giLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7O0FBS01BO0FBSGlCO0FBQ3dCO0FBT3hDLE1BQU1FLFdBQXFCO0lBQ2hDQyxPQUFPO0lBQ1BDLGFBQWE7QUFDZixFQUFFO0FBRWEsU0FBU0MsV0FBVyxFQUNqQ0MsUUFBUSxFQUdSO0lBQ0EscUJBQ0UsOERBQUNDO1FBQUtDLE1BQUs7a0JBQ1QsNEVBQUNDO1lBQUtDLFdBQVcsR0FBR1Ysa0xBQWMsQ0FBQyxzQkFBc0IsQ0FBQztzQkFDeEQsNEVBQUNDLHdEQUFZQTswQkFDVks7Ozs7Ozs7Ozs7Ozs7Ozs7QUFLWCIsInNvdXJjZXMiOlsiQzpcXFVzZXJzXFxkcmVhbVxcRGVza3RvcFxcSGFzaF9NaW5pbmdzXFxoYXNoY29yZXhcXHNyY1xcYXBwXFxsYXlvdXQudHN4Il0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB0eXBlIHsgTWV0YWRhdGEgfSBmcm9tIFwibmV4dFwiO1xuaW1wb3J0IHsgSW50ZXIgfSBmcm9tIFwibmV4dC9mb250L2dvb2dsZVwiO1xuaW1wb3J0IFwiLi9nbG9iYWxzLmNzc1wiO1xuaW1wb3J0IHsgQXV0aFByb3ZpZGVyIH0gZnJvbSBcIkAvaG9va3MvdXNlQXV0aFwiO1xuXG5jb25zdCBpbnRlciA9IEludGVyKHtcbiAgc3Vic2V0czogW1wibGF0aW5cIl0sXG4gIHZhcmlhYmxlOiBcIi0tZm9udC1pbnRlclwiLFxufSk7XG5cbmV4cG9ydCBjb25zdCBtZXRhZGF0YTogTWV0YWRhdGEgPSB7XG4gIHRpdGxlOiBcIkhhc2hDb3JlWCAtIFNvbGFyLVBvd2VyZWQgQ2xvdWQgTWluaW5nXCIsXG4gIGRlc2NyaXB0aW9uOiBcIlN1c3RhaW5hYmxlIGNyeXB0b2N1cnJlbmN5IG1pbmluZyB3aXRoIHNvbGFyIGVuZXJneS4gRWFybiBkYWlseSBST0kgd2l0aCBvdXIgZWNvLWZyaWVuZGx5IG1pbmluZyBwbGF0Zm9ybS5cIixcbn07XG5cbmV4cG9ydCBkZWZhdWx0IGZ1bmN0aW9uIFJvb3RMYXlvdXQoe1xuICBjaGlsZHJlbixcbn06IFJlYWRvbmx5PHtcbiAgY2hpbGRyZW46IFJlYWN0LlJlYWN0Tm9kZTtcbn0+KSB7XG4gIHJldHVybiAoXG4gICAgPGh0bWwgbGFuZz1cImVuXCI+XG4gICAgICA8Ym9keSBjbGFzc05hbWU9e2Ake2ludGVyLnZhcmlhYmxlfSBmb250LXNhbnMgYW50aWFsaWFzZWRgfT5cbiAgICAgICAgPEF1dGhQcm92aWRlcj5cbiAgICAgICAgICB7Y2hpbGRyZW59XG4gICAgICAgIDwvQXV0aFByb3ZpZGVyPlxuICAgICAgPC9ib2R5PlxuICAgIDwvaHRtbD5cbiAgKTtcbn1cbiJdLCJuYW1lcyI6WyJpbnRlciIsIkF1dGhQcm92aWRlciIsIm1ldGFkYXRhIiwidGl0bGUiLCJkZXNjcmlwdGlvbiIsIlJvb3RMYXlvdXQiLCJjaGlsZHJlbiIsImh0bWwiLCJsYW5nIiwiYm9keSIsImNsYXNzTmFtZSIsInZhcmlhYmxlIl0sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./src/app/layout.tsx\n");

/***/ }),

/***/ "(rsc)/./src/hooks/useAuth.tsx":
/*!*******************************!*\
  !*** ./src/hooks/useAuth.tsx ***!
  \*******************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   AuthProvider: () => (/* binding */ AuthProvider),
/* harmony export */   useAuth: () => (/* binding */ useAuth)
/* harmony export */ });
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react-server-dom-webpack/server.edge */ "(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-webpack-server-edge.js");
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__);

const AuthProvider = (0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call AuthProvider() from the server but AuthProvider is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"C:\\Users\\<USER>\\Desktop\\Hash_Minings\\hashcorex\\src\\hooks\\useAuth.tsx",
"AuthProvider",
);const useAuth = (0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call useAuth() from the server but useAuth is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"C:\\Users\\<USER>\\Desktop\\Hash_Minings\\hashcorex\\src\\hooks\\useAuth.tsx",
"useAuth",
);

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cdream%5C%5CDesktop%5C%5CHash_Minings%5C%5Chashcorex%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cdream%5C%5CDesktop%5C%5CHash_Minings%5C%5Chashcorex%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cdream%5C%5CDesktop%5C%5CHash_Minings%5C%5Chashcorex%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cdream%5C%5CDesktop%5C%5CHash_Minings%5C%5Chashcorex%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cdream%5C%5CDesktop%5C%5CHash_Minings%5C%5Chashcorex%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cdream%5C%5CDesktop%5C%5CHash_Minings%5C%5Chashcorex%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cdream%5C%5CDesktop%5C%5CHash_Minings%5C%5Chashcorex%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cdream%5C%5CDesktop%5C%5CHash_Minings%5C%5Chashcorex%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!*************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cdream%5C%5CDesktop%5C%5CHash_Minings%5C%5Chashcorex%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cdream%5C%5CDesktop%5C%5CHash_Minings%5C%5Chashcorex%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cdream%5C%5CDesktop%5C%5CHash_Minings%5C%5Chashcorex%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cdream%5C%5CDesktop%5C%5CHash_Minings%5C%5Chashcorex%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cdream%5C%5CDesktop%5C%5CHash_Minings%5C%5Chashcorex%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cdream%5C%5CDesktop%5C%5CHash_Minings%5C%5Chashcorex%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cdream%5C%5CDesktop%5C%5CHash_Minings%5C%5Chashcorex%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cdream%5C%5CDesktop%5C%5CHash_Minings%5C%5Chashcorex%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \*************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-page.js */ \"(ssr)/./node_modules/next/dist/client/components/client-page.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-segment.js */ \"(ssr)/./node_modules/next/dist/client/components/client-segment.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/error-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/layout-router.js */ \"(ssr)/./node_modules/next/dist/client/components/layout-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/async-metadata.js */ \"(ssr)/./node_modules/next/dist/client/components/metadata/async-metadata.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/metadata-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/render-from-template-context.js */ \"(ssr)/./node_modules/next/dist/client/components/render-from-template-context.js\", 23));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cdream%5C%5CDesktop%5C%5CHash_Minings%5C%5Chashcorex%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cdream%5C%5CDesktop%5C%5CHash_Minings%5C%5Chashcorex%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cdream%5C%5CDesktop%5C%5CHash_Minings%5C%5Chashcorex%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cdream%5C%5CDesktop%5C%5CHash_Minings%5C%5Chashcorex%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cdream%5C%5CDesktop%5C%5CHash_Minings%5C%5Chashcorex%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cdream%5C%5CDesktop%5C%5CHash_Minings%5C%5Chashcorex%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cdream%5C%5CDesktop%5C%5CHash_Minings%5C%5Chashcorex%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cdream%5C%5CDesktop%5C%5CHash_Minings%5C%5Chashcorex%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cdream%5C%5CDesktop%5C%5CHash_Minings%5C%5Chashcorex%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%2C%5C%22variable%5C%22%3A%5C%22--font-inter%5C%22%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cdream%5C%5CDesktop%5C%5CHash_Minings%5C%5Chashcorex%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cdream%5C%5CDesktop%5C%5CHash_Minings%5C%5Chashcorex%5C%5Csrc%5C%5Chooks%5C%5CuseAuth.tsx%22%2C%22ids%22%3A%5B%22AuthProvider%22%5D%7D&server=true!":
/*!**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cdream%5C%5CDesktop%5C%5CHash_Minings%5C%5Chashcorex%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%2C%5C%22variable%5C%22%3A%5C%22--font-inter%5C%22%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cdream%5C%5CDesktop%5C%5CHash_Minings%5C%5Chashcorex%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cdream%5C%5CDesktop%5C%5CHash_Minings%5C%5Chashcorex%5C%5Csrc%5C%5Chooks%5C%5CuseAuth.tsx%22%2C%22ids%22%3A%5B%22AuthProvider%22%5D%7D&server=true! ***!
  \**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/hooks/useAuth.tsx */ \"(ssr)/./src/hooks/useAuth.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkMlM0ElNUMlNUNVc2VycyU1QyU1Q2RyZWFtJTVDJTVDRGVza3RvcCU1QyU1Q0hhc2hfTWluaW5ncyU1QyU1Q2hhc2hjb3JleCU1QyU1Q25vZGVfbW9kdWxlcyU1QyU1Q25leHQlNUMlNUNmb250JTVDJTVDZ29vZ2xlJTVDJTVDdGFyZ2V0LmNzcyUzRiU3QiU1QyUyMnBhdGglNUMlMjIlM0ElNUMlMjJzcmMlNUMlNUMlNUMlNUNhcHAlNUMlNUMlNUMlNUNsYXlvdXQudHN4JTVDJTIyJTJDJTVDJTIyaW1wb3J0JTVDJTIyJTNBJTVDJTIySW50ZXIlNUMlMjIlMkMlNUMlMjJhcmd1bWVudHMlNUMlMjIlM0ElNUIlN0IlNUMlMjJzdWJzZXRzJTVDJTIyJTNBJTVCJTVDJTIybGF0aW4lNUMlMjIlNUQlMkMlNUMlMjJ2YXJpYWJsZSU1QyUyMiUzQSU1QyUyMi0tZm9udC1pbnRlciU1QyUyMiU3RCU1RCUyQyU1QyUyMnZhcmlhYmxlTmFtZSU1QyUyMiUzQSU1QyUyMmludGVyJTVDJTIyJTdEJTIyJTJDJTIyaWRzJTIyJTNBJTVCJTVEJTdEJm1vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkMlM0ElNUMlNUNVc2VycyU1QyU1Q2RyZWFtJTVDJTVDRGVza3RvcCU1QyU1Q0hhc2hfTWluaW5ncyU1QyU1Q2hhc2hjb3JleCU1QyU1Q3NyYyU1QyU1Q2FwcCU1QyU1Q2dsb2JhbHMuY3NzJTIyJTJDJTIyaWRzJTIyJTNBJTVCJTVEJTdEJm1vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkMlM0ElNUMlNUNVc2VycyU1QyU1Q2RyZWFtJTVDJTVDRGVza3RvcCU1QyU1Q0hhc2hfTWluaW5ncyU1QyU1Q2hhc2hjb3JleCU1QyU1Q3NyYyU1QyU1Q2hvb2tzJTVDJTVDdXNlQXV0aC50c3glMjIlMkMlMjJpZHMlMjIlM0ElNUIlMjJBdXRoUHJvdmlkZXIlMjIlNUQlN0Qmc2VydmVyPXRydWUhIiwibWFwcGluZ3MiOiJBQUFBLDBKQUFrSiIsInNvdXJjZXMiOlsiIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiLCB3ZWJwYWNrRXhwb3J0czogW1wiQXV0aFByb3ZpZGVyXCJdICovIFwiQzpcXFxcVXNlcnNcXFxcZHJlYW1cXFxcRGVza3RvcFxcXFxIYXNoX01pbmluZ3NcXFxcaGFzaGNvcmV4XFxcXHNyY1xcXFxob29rc1xcXFx1c2VBdXRoLnRzeFwiKTtcbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cdream%5C%5CDesktop%5C%5CHash_Minings%5C%5Chashcorex%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%2C%5C%22variable%5C%22%3A%5C%22--font-inter%5C%22%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cdream%5C%5CDesktop%5C%5CHash_Minings%5C%5Chashcorex%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cdream%5C%5CDesktop%5C%5CHash_Minings%5C%5Chashcorex%5C%5Csrc%5C%5Chooks%5C%5CuseAuth.tsx%22%2C%22ids%22%3A%5B%22AuthProvider%22%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cdream%5C%5CDesktop%5C%5CHash_Minings%5C%5Chashcorex%5C%5Csrc%5C%5Capp%5C%5C(auth)%5C%5Cforgot-password%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!*********************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cdream%5C%5CDesktop%5C%5CHash_Minings%5C%5Chashcorex%5C%5Csrc%5C%5Capp%5C%5C(auth)%5C%5Cforgot-password%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \*********************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/(auth)/forgot-password/page.tsx */ \"(ssr)/./src/app/(auth)/forgot-password/page.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkMlM0ElNUMlNUNVc2VycyU1QyU1Q2RyZWFtJTVDJTVDRGVza3RvcCU1QyU1Q0hhc2hfTWluaW5ncyU1QyU1Q2hhc2hjb3JleCU1QyU1Q3NyYyU1QyU1Q2FwcCU1QyU1QyhhdXRoKSU1QyU1Q2ZvcmdvdC1wYXNzd29yZCU1QyU1Q3BhZ2UudHN4JTIyJTJDJTIyaWRzJTIyJTNBJTVCJTVEJTdEJnNlcnZlcj10cnVlISIsIm1hcHBpbmdzIjoiQUFBQSw4TEFBb0kiLCJzb3VyY2VzIjpbIiJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQoLyogd2VicGFja01vZGU6IFwiZWFnZXJcIiAqLyBcIkM6XFxcXFVzZXJzXFxcXGRyZWFtXFxcXERlc2t0b3BcXFxcSGFzaF9NaW5pbmdzXFxcXGhhc2hjb3JleFxcXFxzcmNcXFxcYXBwXFxcXChhdXRoKVxcXFxmb3Jnb3QtcGFzc3dvcmRcXFxccGFnZS50c3hcIik7XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cdream%5C%5CDesktop%5C%5CHash_Minings%5C%5Chashcorex%5C%5Csrc%5C%5Capp%5C%5C(auth)%5C%5Cforgot-password%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./src/app/(auth)/forgot-password/page.tsx":
/*!*************************************************!*\
  !*** ./src/app/(auth)/forgot-password/page.tsx ***!
  \*************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ ForgotPasswordPage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/link */ \"(ssr)/./node_modules/next/dist/client/app-dir/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/navigation */ \"(ssr)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _components_ui__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui */ \"(ssr)/./src/components/ui/index.ts\");\n/* harmony import */ var _components_icons__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/icons */ \"(ssr)/./src/components/icons/index.ts\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_Check_Eye_EyeOff_Mail_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,Check,Eye,EyeOff,Mail!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/mail.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_Check_Eye_EyeOff_Mail_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,Check,Eye,EyeOff,Mail!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/arrow-left.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_Check_Eye_EyeOff_Mail_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,Check,Eye,EyeOff,Mail!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/check.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_Check_Eye_EyeOff_Mail_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,Check,Eye,EyeOff,Mail!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/eye-off.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_Check_Eye_EyeOff_Mail_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,Check,Eye,EyeOff,Mail!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/eye.js\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/lib/utils */ \"(ssr)/./src/lib/utils.ts\");\n/* harmony import */ var _components_auth_AuthGuard__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/auth/AuthGuard */ \"(ssr)/./src/components/auth/AuthGuard.tsx\");\n/* harmony import */ var _components_auth_OTPVerification__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/components/auth/OTPVerification */ \"(ssr)/./src/components/auth/OTPVerification.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n\n\n\n\n\n\nfunction ForgotPasswordForm() {\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_3__.useRouter)();\n    const [step, setStep] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('email');\n    const [email, setEmail] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [newPassword, setNewPassword] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [confirmPassword, setConfirmPassword] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [showPassword, setShowPassword] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [showConfirmPassword, setShowConfirmPassword] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [success, setSuccess] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const passwordValidation = (0,_lib_utils__WEBPACK_IMPORTED_MODULE_6__.validatePassword)(newPassword);\n    const handleEmailSubmit = async (e)=>{\n        e.preventDefault();\n        setError('');\n        setLoading(true);\n        try {\n            const response = await fetch('/api/auth/send-otp', {\n                method: 'POST',\n                headers: {\n                    'Content-Type': 'application/json'\n                },\n                body: JSON.stringify({\n                    email,\n                    purpose: 'password_reset'\n                })\n            });\n            const data = await response.json();\n            if (data.success) {\n                setStep('otp');\n            } else {\n                setError(data.error || 'Failed to send reset code');\n            }\n        } catch (error) {\n            setError('Failed to send reset code. Please try again.');\n        } finally{\n            setLoading(false);\n        }\n    };\n    const handleOTPVerified = async (otp)=>{\n        setError('');\n        setLoading(true);\n        try {\n            const response = await fetch('/api/auth/verify-otp', {\n                method: 'POST',\n                headers: {\n                    'Content-Type': 'application/json'\n                },\n                body: JSON.stringify({\n                    email,\n                    otp,\n                    purpose: 'password_reset'\n                })\n            });\n            const data = await response.json();\n            if (data.success) {\n                setStep('password');\n            } else {\n                setError(data.error || 'Invalid OTP');\n            }\n        } catch (error) {\n            setError('Failed to verify OTP. Please try again.');\n        } finally{\n            setLoading(false);\n        }\n    };\n    const handleResendOTP = async ()=>{\n        setError('');\n        setLoading(true);\n        try {\n            const response = await fetch('/api/auth/send-otp', {\n                method: 'POST',\n                headers: {\n                    'Content-Type': 'application/json'\n                },\n                body: JSON.stringify({\n                    email,\n                    purpose: 'password_reset'\n                })\n            });\n            const data = await response.json();\n            if (!data.success) {\n                setError(data.error || 'Failed to resend code');\n            }\n        } catch (error) {\n            setError('Failed to resend code. Please try again.');\n        } finally{\n            setLoading(false);\n        }\n    };\n    const handlePasswordReset = async (e)=>{\n        e.preventDefault();\n        setError('');\n        if (newPassword !== confirmPassword) {\n            setError('Passwords do not match');\n            return;\n        }\n        if (!passwordValidation.isValid) {\n            setError('Please ensure your password meets all requirements');\n            return;\n        }\n        setLoading(true);\n        try {\n            const response = await fetch('/api/auth/reset-password', {\n                method: 'POST',\n                headers: {\n                    'Content-Type': 'application/json'\n                },\n                body: JSON.stringify({\n                    email,\n                    newPassword,\n                    confirmPassword\n                })\n            });\n            const data = await response.json();\n            if (data.success) {\n                setSuccess('Password reset successful! Redirecting to dashboard...');\n                setTimeout(()=>{\n                    router.push('/dashboard');\n                }, 2000);\n            } else {\n                setError(data.error || 'Failed to reset password');\n            }\n        } catch (error) {\n            setError('Failed to reset password. Please try again.');\n        } finally{\n            setLoading(false);\n        }\n    };\n    // Email Step\n    const renderEmailStep = ()=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"max-w-md mx-auto\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"text-center mb-8\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"w-16 h-16 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-4\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Check_Eye_EyeOff_Mail_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                className: \"w-8 h-8 text-blue-600\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\app\\\\(auth)\\\\forgot-password\\\\page.tsx\",\n                                lineNumber: 168,\n                                columnNumber: 11\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\app\\\\(auth)\\\\forgot-password\\\\page.tsx\",\n                            lineNumber: 167,\n                            columnNumber: 9\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                            className: \"text-2xl font-bold text-gray-900 mb-2\",\n                            children: \"Reset Your Password\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\app\\\\(auth)\\\\forgot-password\\\\page.tsx\",\n                            lineNumber: 170,\n                            columnNumber: 9\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-gray-600\",\n                            children: \"Enter your email address and we'll send you a verification code to reset your password.\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\app\\\\(auth)\\\\forgot-password\\\\page.tsx\",\n                            lineNumber: 171,\n                            columnNumber: 9\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\app\\\\(auth)\\\\forgot-password\\\\page.tsx\",\n                    lineNumber: 166,\n                    columnNumber: 7\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n                    onSubmit: handleEmailSubmit,\n                    className: \"space-y-6\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                    htmlFor: \"email\",\n                                    className: \"block text-sm font-medium text-gray-700 mb-2\",\n                                    children: \"Email Address\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\app\\\\(auth)\\\\forgot-password\\\\page.tsx\",\n                                    lineNumber: 178,\n                                    columnNumber: 11\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_4__.Input, {\n                                    id: \"email\",\n                                    type: \"email\",\n                                    value: email,\n                                    onChange: (e)=>setEmail(e.target.value),\n                                    placeholder: \"Enter your email address\",\n                                    required: true,\n                                    disabled: loading,\n                                    className: \"w-full\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\app\\\\(auth)\\\\forgot-password\\\\page.tsx\",\n                                    lineNumber: 181,\n                                    columnNumber: 11\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\app\\\\(auth)\\\\forgot-password\\\\page.tsx\",\n                            lineNumber: 177,\n                            columnNumber: 9\n                        }, this),\n                        error && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-red-50 border border-red-200 rounded-lg p-3\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-sm text-red-600 text-center\",\n                                children: error\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\app\\\\(auth)\\\\forgot-password\\\\page.tsx\",\n                                lineNumber: 195,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\app\\\\(auth)\\\\forgot-password\\\\page.tsx\",\n                            lineNumber: 194,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                            type: \"submit\",\n                            disabled: loading || !email,\n                            className: \"w-full bg-blue-600 hover:bg-blue-700 text-white\",\n                            children: loading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center justify-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\app\\\\(auth)\\\\forgot-password\\\\page.tsx\",\n                                        lineNumber: 206,\n                                        columnNumber: 15\n                                    }, this),\n                                    \"Sending Code...\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\app\\\\(auth)\\\\forgot-password\\\\page.tsx\",\n                                lineNumber: 205,\n                                columnNumber: 13\n                            }, this) : 'Send Reset Code'\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\app\\\\(auth)\\\\forgot-password\\\\page.tsx\",\n                            lineNumber: 199,\n                            columnNumber: 9\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-center\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                href: \"/login\",\n                                className: \"text-sm text-blue-600 hover:text-blue-700 flex items-center justify-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Check_Eye_EyeOff_Mail_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                        className: \"w-4 h-4 mr-1\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\app\\\\(auth)\\\\forgot-password\\\\page.tsx\",\n                                        lineNumber: 219,\n                                        columnNumber: 13\n                                    }, this),\n                                    \"Back to Login\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\app\\\\(auth)\\\\forgot-password\\\\page.tsx\",\n                                lineNumber: 215,\n                                columnNumber: 11\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\app\\\\(auth)\\\\forgot-password\\\\page.tsx\",\n                            lineNumber: 214,\n                            columnNumber: 9\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\app\\\\(auth)\\\\forgot-password\\\\page.tsx\",\n                    lineNumber: 176,\n                    columnNumber: 7\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\app\\\\(auth)\\\\forgot-password\\\\page.tsx\",\n            lineNumber: 165,\n            columnNumber: 5\n        }, this);\n    // Password Reset Step\n    const renderPasswordStep = ()=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"max-w-md mx-auto\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"text-center mb-8\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-4\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Check_Eye_EyeOff_Mail_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                className: \"w-8 h-8 text-green-600\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\app\\\\(auth)\\\\forgot-password\\\\page.tsx\",\n                                lineNumber: 232,\n                                columnNumber: 11\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\app\\\\(auth)\\\\forgot-password\\\\page.tsx\",\n                            lineNumber: 231,\n                            columnNumber: 9\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                            className: \"text-2xl font-bold text-gray-900 mb-2\",\n                            children: \"Create New Password\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\app\\\\(auth)\\\\forgot-password\\\\page.tsx\",\n                            lineNumber: 234,\n                            columnNumber: 9\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-gray-600\",\n                            children: \"Enter your new password below. Make sure it's strong and secure.\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\app\\\\(auth)\\\\forgot-password\\\\page.tsx\",\n                            lineNumber: 235,\n                            columnNumber: 9\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\app\\\\(auth)\\\\forgot-password\\\\page.tsx\",\n                    lineNumber: 230,\n                    columnNumber: 7\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n                    onSubmit: handlePasswordReset,\n                    className: \"space-y-6\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                    htmlFor: \"newPassword\",\n                                    className: \"block text-sm font-medium text-gray-700 mb-2\",\n                                    children: \"New Password\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\app\\\\(auth)\\\\forgot-password\\\\page.tsx\",\n                                    lineNumber: 242,\n                                    columnNumber: 11\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"relative\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_4__.Input, {\n                                            id: \"newPassword\",\n                                            type: showPassword ? 'text' : 'password',\n                                            value: newPassword,\n                                            onChange: (e)=>setNewPassword(e.target.value),\n                                            placeholder: \"Enter new password\",\n                                            required: true,\n                                            disabled: loading,\n                                            className: \"w-full pr-10\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\app\\\\(auth)\\\\forgot-password\\\\page.tsx\",\n                                            lineNumber: 246,\n                                            columnNumber: 13\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            type: \"button\",\n                                            onClick: ()=>setShowPassword(!showPassword),\n                                            className: \"absolute inset-y-0 right-0 pr-3 flex items-center\",\n                                            children: showPassword ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Check_Eye_EyeOff_Mail_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                className: \"h-4 w-4 text-gray-400\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\app\\\\(auth)\\\\forgot-password\\\\page.tsx\",\n                                                lineNumber: 262,\n                                                columnNumber: 17\n                                            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Check_Eye_EyeOff_Mail_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                className: \"h-4 w-4 text-gray-400\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\app\\\\(auth)\\\\forgot-password\\\\page.tsx\",\n                                                lineNumber: 264,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\app\\\\(auth)\\\\forgot-password\\\\page.tsx\",\n                                            lineNumber: 256,\n                                            columnNumber: 13\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\app\\\\(auth)\\\\forgot-password\\\\page.tsx\",\n                                    lineNumber: 245,\n                                    columnNumber: 11\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\app\\\\(auth)\\\\forgot-password\\\\page.tsx\",\n                            lineNumber: 241,\n                            columnNumber: 9\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                    htmlFor: \"confirmPassword\",\n                                    className: \"block text-sm font-medium text-gray-700 mb-2\",\n                                    children: \"Confirm New Password\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\app\\\\(auth)\\\\forgot-password\\\\page.tsx\",\n                                    lineNumber: 271,\n                                    columnNumber: 11\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"relative\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_4__.Input, {\n                                            id: \"confirmPassword\",\n                                            type: showConfirmPassword ? 'text' : 'password',\n                                            value: confirmPassword,\n                                            onChange: (e)=>setConfirmPassword(e.target.value),\n                                            placeholder: \"Confirm new password\",\n                                            required: true,\n                                            disabled: loading,\n                                            className: \"w-full pr-10\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\app\\\\(auth)\\\\forgot-password\\\\page.tsx\",\n                                            lineNumber: 275,\n                                            columnNumber: 13\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            type: \"button\",\n                                            onClick: ()=>setShowConfirmPassword(!showConfirmPassword),\n                                            className: \"absolute inset-y-0 right-0 pr-3 flex items-center\",\n                                            children: showConfirmPassword ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Check_Eye_EyeOff_Mail_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                className: \"h-4 w-4 text-gray-400\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\app\\\\(auth)\\\\forgot-password\\\\page.tsx\",\n                                                lineNumber: 291,\n                                                columnNumber: 17\n                                            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Check_Eye_EyeOff_Mail_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                className: \"h-4 w-4 text-gray-400\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\app\\\\(auth)\\\\forgot-password\\\\page.tsx\",\n                                                lineNumber: 293,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\app\\\\(auth)\\\\forgot-password\\\\page.tsx\",\n                                            lineNumber: 285,\n                                            columnNumber: 13\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\app\\\\(auth)\\\\forgot-password\\\\page.tsx\",\n                                    lineNumber: 274,\n                                    columnNumber: 11\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\app\\\\(auth)\\\\forgot-password\\\\page.tsx\",\n                            lineNumber: 270,\n                            columnNumber: 9\n                        }, this),\n                        newPassword && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-gray-50 border border-gray-200 rounded-lg p-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-sm font-medium text-gray-700 mb-2\",\n                                    children: \"Password Requirements:\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\app\\\\(auth)\\\\forgot-password\\\\page.tsx\",\n                                    lineNumber: 302,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"space-y-1\",\n                                    children: passwordValidation.checks.map((check, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center text-sm\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: `w-4 h-4 rounded-full mr-2 flex items-center justify-center ${check.valid ? 'bg-green-100' : 'bg-gray-100'}`,\n                                                    children: check.valid && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Check_Eye_EyeOff_Mail_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                        className: \"w-3 h-3 text-green-600\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\app\\\\(auth)\\\\forgot-password\\\\page.tsx\",\n                                                        lineNumber: 309,\n                                                        columnNumber: 37\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\app\\\\(auth)\\\\forgot-password\\\\page.tsx\",\n                                                    lineNumber: 306,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: check.valid ? 'text-green-600' : 'text-gray-500',\n                                                    children: check.message\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\app\\\\(auth)\\\\forgot-password\\\\page.tsx\",\n                                                    lineNumber: 311,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, index, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\app\\\\(auth)\\\\forgot-password\\\\page.tsx\",\n                                            lineNumber: 305,\n                                            columnNumber: 17\n                                        }, this))\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\app\\\\(auth)\\\\forgot-password\\\\page.tsx\",\n                                    lineNumber: 303,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\app\\\\(auth)\\\\forgot-password\\\\page.tsx\",\n                            lineNumber: 301,\n                            columnNumber: 11\n                        }, this),\n                        error && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-red-50 border border-red-200 rounded-lg p-3\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-sm text-red-600 text-center\",\n                                children: error\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\app\\\\(auth)\\\\forgot-password\\\\page.tsx\",\n                                lineNumber: 322,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\app\\\\(auth)\\\\forgot-password\\\\page.tsx\",\n                            lineNumber: 321,\n                            columnNumber: 11\n                        }, this),\n                        success && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-green-50 border border-green-200 rounded-lg p-3\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-sm text-green-600 text-center\",\n                                children: success\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\app\\\\(auth)\\\\forgot-password\\\\page.tsx\",\n                                lineNumber: 328,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\app\\\\(auth)\\\\forgot-password\\\\page.tsx\",\n                            lineNumber: 327,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                            type: \"submit\",\n                            disabled: loading || !passwordValidation.isValid || newPassword !== confirmPassword,\n                            className: \"w-full bg-green-600 hover:bg-green-700 text-white\",\n                            children: loading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center justify-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\app\\\\(auth)\\\\forgot-password\\\\page.tsx\",\n                                        lineNumber: 339,\n                                        columnNumber: 15\n                                    }, this),\n                                    \"Resetting Password...\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\app\\\\(auth)\\\\forgot-password\\\\page.tsx\",\n                                lineNumber: 338,\n                                columnNumber: 13\n                            }, this) : 'Reset Password'\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\app\\\\(auth)\\\\forgot-password\\\\page.tsx\",\n                            lineNumber: 332,\n                            columnNumber: 9\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\app\\\\(auth)\\\\forgot-password\\\\page.tsx\",\n                    lineNumber: 240,\n                    columnNumber: 7\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\app\\\\(auth)\\\\forgot-password\\\\page.tsx\",\n            lineNumber: 229,\n            columnNumber: 5\n        }, this);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 flex items-center justify-center p-4\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"w-full max-w-lg\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"text-center mb-8\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                        href: \"/\",\n                        className: \"inline-flex items-center justify-center mb-6\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_icons__WEBPACK_IMPORTED_MODULE_5__.SolarPanel, {\n                                className: \"h-8 w-8 text-yellow-500 mr-2\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\app\\\\(auth)\\\\forgot-password\\\\page.tsx\",\n                                lineNumber: 356,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"text-2xl font-bold text-gray-900\",\n                                children: \"HashCoreX\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\app\\\\(auth)\\\\forgot-password\\\\page.tsx\",\n                                lineNumber: 357,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\app\\\\(auth)\\\\forgot-password\\\\page.tsx\",\n                        lineNumber: 355,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\app\\\\(auth)\\\\forgot-password\\\\page.tsx\",\n                    lineNumber: 354,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"bg-white rounded-2xl shadow-xl p-8\",\n                    children: [\n                        step === 'email' && renderEmailStep(),\n                        step === 'otp' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_auth_OTPVerification__WEBPACK_IMPORTED_MODULE_8__.OTPVerification, {\n                            email: email,\n                            onVerified: handleOTPVerified,\n                            onResend: handleResendOTP,\n                            loading: loading,\n                            error: error\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\app\\\\(auth)\\\\forgot-password\\\\page.tsx\",\n                            lineNumber: 365,\n                            columnNumber: 13\n                        }, this),\n                        step === 'password' && renderPasswordStep()\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\app\\\\(auth)\\\\forgot-password\\\\page.tsx\",\n                    lineNumber: 362,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"text-center mt-6\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-sm text-gray-600\",\n                        children: [\n                            \"Remember your password?\",\n                            ' ',\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                href: \"/login\",\n                                className: \"text-blue-600 hover:text-blue-700 font-medium\",\n                                children: \"Sign in here\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\app\\\\(auth)\\\\forgot-password\\\\page.tsx\",\n                                lineNumber: 380,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\app\\\\(auth)\\\\forgot-password\\\\page.tsx\",\n                        lineNumber: 378,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\app\\\\(auth)\\\\forgot-password\\\\page.tsx\",\n                    lineNumber: 377,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\app\\\\(auth)\\\\forgot-password\\\\page.tsx\",\n            lineNumber: 352,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\app\\\\(auth)\\\\forgot-password\\\\page.tsx\",\n        lineNumber: 351,\n        columnNumber: 5\n    }, this);\n}\nfunction ForgotPasswordPage() {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_auth_AuthGuard__WEBPACK_IMPORTED_MODULE_7__.GuestRoute, {\n        redirectTo: \"/dashboard\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ForgotPasswordForm, {}, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\app\\\\(auth)\\\\forgot-password\\\\page.tsx\",\n            lineNumber: 393,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\app\\\\(auth)\\\\forgot-password\\\\page.tsx\",\n        lineNumber: 392,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/app/(auth)/forgot-password/page.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/auth/AuthGuard.tsx":
/*!*******************************************!*\
  !*** ./src/components/auth/AuthGuard.tsx ***!
  \*******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   AuthGuard: () => (/* binding */ AuthGuard),\n/* harmony export */   GuestRoute: () => (/* binding */ GuestRoute),\n/* harmony export */   ProtectedRoute: () => (/* binding */ ProtectedRoute),\n/* harmony export */   withAuthGuard: () => (/* binding */ withAuthGuard)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(ssr)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _hooks_useAuth__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/hooks/useAuth */ \"(ssr)/./src/hooks/useAuth.tsx\");\n/* harmony import */ var _components_ui__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui */ \"(ssr)/./src/components/ui/index.ts\");\n/* __next_internal_client_entry_do_not_use__ AuthGuard,withAuthGuard,ProtectedRoute,GuestRoute auto */ \n\n\n\n\nconst AuthGuard = ({ children, requireAuth = false, requireGuest = false, redirectTo })=>{\n    const { user, loading } = (0,_hooks_useAuth__WEBPACK_IMPORTED_MODULE_3__.useAuth)();\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    const pathname = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.usePathname)();\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"AuthGuard.useEffect\": ()=>{\n            if (loading) return; // Wait for auth check to complete\n            // If authentication is required but user is not logged in\n            if (requireAuth && !user) {\n                const loginUrl = `/login?redirect=${encodeURIComponent(pathname)}`;\n                router.replace(loginUrl);\n                return;\n            }\n            // If guest access is required but user is logged in\n            if (requireGuest && user) {\n                const destination = redirectTo || '/dashboard';\n                router.replace(destination);\n                return;\n            }\n        }\n    }[\"AuthGuard.useEffect\"], [\n        user,\n        loading,\n        requireAuth,\n        requireGuest,\n        router,\n        pathname,\n        redirectTo\n    ]);\n    // Show loading while checking authentication\n    if (loading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"min-h-screen flex items-center justify-center bg-gray-50\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_4__.Loading, {}, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\auth\\\\AuthGuard.tsx\",\n                        lineNumber: 48,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"mt-4 text-gray-600\",\n                        children: \"Checking authentication...\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\auth\\\\AuthGuard.tsx\",\n                        lineNumber: 49,\n                        columnNumber: 11\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\auth\\\\AuthGuard.tsx\",\n                lineNumber: 47,\n                columnNumber: 9\n            }, undefined)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\auth\\\\AuthGuard.tsx\",\n            lineNumber: 46,\n            columnNumber: 7\n        }, undefined);\n    }\n    // Don't render children if auth requirements are not met\n    if (requireAuth && !user) {\n        return null; // Will redirect in useEffect\n    }\n    if (requireGuest && user) {\n        return null; // Will redirect in useEffect\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: children\n    }, void 0, false);\n};\n// Higher-order component for protecting pages\nconst withAuthGuard = (Component, options = {})=>{\n    const WrappedComponent = (props)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(AuthGuard, {\n            ...options,\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Component, {\n                ...props\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\auth\\\\AuthGuard.tsx\",\n                lineNumber: 74,\n                columnNumber: 7\n            }, undefined)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\auth\\\\AuthGuard.tsx\",\n            lineNumber: 73,\n            columnNumber: 5\n        }, undefined);\n    WrappedComponent.displayName = `withAuthGuard(${Component.displayName || Component.name})`;\n    return WrappedComponent;\n};\n// Convenience components\nconst ProtectedRoute = ({ children })=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(AuthGuard, {\n        requireAuth: true,\n        children: children\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\auth\\\\AuthGuard.tsx\",\n        lineNumber: 85,\n        columnNumber: 3\n    }, undefined);\nconst GuestRoute = ({ children, redirectTo })=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(AuthGuard, {\n        requireGuest: true,\n        redirectTo: redirectTo,\n        children: children\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\auth\\\\AuthGuard.tsx\",\n        lineNumber: 92,\n        columnNumber: 3\n    }, undefined);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/auth/AuthGuard.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/auth/OTPVerification.tsx":
/*!*************************************************!*\
  !*** ./src/components/auth/OTPVerification.tsx ***!
  \*************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   OTPVerification: () => (/* binding */ OTPVerification)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_ui__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui */ \"(ssr)/./src/components/ui/index.ts\");\n/* harmony import */ var _barrel_optimize_names_CheckCircle_Mail_RefreshCw_lucide_react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=CheckCircle,Mail,RefreshCw!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/mail.js\");\n/* harmony import */ var _barrel_optimize_names_CheckCircle_Mail_RefreshCw_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=CheckCircle,Mail,RefreshCw!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/circle-check-big.js\");\n/* harmony import */ var _barrel_optimize_names_CheckCircle_Mail_RefreshCw_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=CheckCircle,Mail,RefreshCw!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/refresh-cw.js\");\n/* __next_internal_client_entry_do_not_use__ OTPVerification auto */ \n\n\n\nconst OTPVerification = ({ email, firstName, onVerified, onResend, loading = false, error })=>{\n    const [otp, setOtp] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([\n        '',\n        '',\n        '',\n        '',\n        '',\n        ''\n    ]);\n    const [timeLeft, setTimeLeft] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(600); // 10 minutes\n    const [canResend, setCanResend] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [resending, setResending] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const inputRefs = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)([]);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"OTPVerification.useEffect\": ()=>{\n            const timer = setInterval({\n                \"OTPVerification.useEffect.timer\": ()=>{\n                    setTimeLeft({\n                        \"OTPVerification.useEffect.timer\": (prev)=>{\n                            if (prev <= 1) {\n                                setCanResend(true);\n                                return 0;\n                            }\n                            return prev - 1;\n                        }\n                    }[\"OTPVerification.useEffect.timer\"]);\n                }\n            }[\"OTPVerification.useEffect.timer\"], 1000);\n            return ({\n                \"OTPVerification.useEffect\": ()=>clearInterval(timer)\n            })[\"OTPVerification.useEffect\"];\n        }\n    }[\"OTPVerification.useEffect\"], []);\n    const formatTime = (seconds)=>{\n        const mins = Math.floor(seconds / 60);\n        const secs = seconds % 60;\n        return `${mins}:${secs.toString().padStart(2, '0')}`;\n    };\n    const handleOtpChange = (index, value)=>{\n        if (value.length > 1) return; // Prevent multiple characters\n        const newOtp = [\n            ...otp\n        ];\n        newOtp[index] = value;\n        setOtp(newOtp);\n        // Auto-focus next input\n        if (value && index < 5) {\n            inputRefs.current[index + 1]?.focus();\n        }\n        // Auto-submit when all fields are filled\n        if (newOtp.every((digit)=>digit !== '') && newOtp.join('').length === 6) {\n            onVerified(newOtp.join(''));\n        }\n    };\n    const handleKeyDown = (index, e)=>{\n        if (e.key === 'Backspace' && !otp[index] && index > 0) {\n            inputRefs.current[index - 1]?.focus();\n        }\n    };\n    const handlePaste = (e)=>{\n        e.preventDefault();\n        const pastedData = e.clipboardData.getData('text').replace(/\\D/g, '').slice(0, 6);\n        if (pastedData.length === 6) {\n            const newOtp = pastedData.split('');\n            setOtp(newOtp);\n            onVerified(pastedData);\n        }\n    };\n    const handleResend = async ()=>{\n        setResending(true);\n        setCanResend(false);\n        setTimeLeft(600); // Reset timer\n        await onResend();\n        setResending(false);\n    };\n    const handleManualSubmit = ()=>{\n        const otpString = otp.join('');\n        if (otpString.length === 6) {\n            onVerified(otpString);\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"max-w-md mx-auto\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-center mb-8\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-4\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CheckCircle_Mail_RefreshCw_lucide_react__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                            className: \"w-8 h-8 text-green-600\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\auth\\\\OTPVerification.tsx\",\n                            lineNumber: 104,\n                            columnNumber: 11\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\auth\\\\OTPVerification.tsx\",\n                        lineNumber: 103,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                        className: \"text-2xl font-bold text-gray-900 mb-2\",\n                        children: \"Verify Your Email\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\auth\\\\OTPVerification.tsx\",\n                        lineNumber: 106,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-gray-600\",\n                        children: \"We've sent a 6-digit verification code to\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\auth\\\\OTPVerification.tsx\",\n                        lineNumber: 107,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"font-semibold text-gray-900\",\n                        children: email\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\auth\\\\OTPVerification.tsx\",\n                        lineNumber: 110,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\auth\\\\OTPVerification.tsx\",\n                lineNumber: 102,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"space-y-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                className: \"block text-sm font-medium text-gray-700 mb-3 text-center\",\n                                children: \"Enter verification code\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\auth\\\\OTPVerification.tsx\",\n                                lineNumber: 116,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex justify-center space-x-3\",\n                                children: otp.map((digit, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_2__.Input, {\n                                        ref: (el)=>inputRefs.current[index] = el,\n                                        type: \"text\",\n                                        inputMode: \"numeric\",\n                                        maxLength: 1,\n                                        value: digit,\n                                        onChange: (e)=>handleOtpChange(index, e.target.value),\n                                        onKeyDown: (e)=>handleKeyDown(index, e),\n                                        onPaste: handlePaste,\n                                        className: \"w-12 h-12 text-center text-lg font-semibold border-2 focus:border-green-500\",\n                                        disabled: loading\n                                    }, index, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\auth\\\\OTPVerification.tsx\",\n                                        lineNumber: 121,\n                                        columnNumber: 15\n                                    }, undefined))\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\auth\\\\OTPVerification.tsx\",\n                                lineNumber: 119,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\auth\\\\OTPVerification.tsx\",\n                        lineNumber: 115,\n                        columnNumber: 9\n                    }, undefined),\n                    error && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-red-50 border border-red-200 rounded-lg p-3\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-sm text-red-600 text-center\",\n                            children: error\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\auth\\\\OTPVerification.tsx\",\n                            lineNumber: 141,\n                            columnNumber: 13\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\auth\\\\OTPVerification.tsx\",\n                        lineNumber: 140,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-center\",\n                        children: timeLeft > 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-sm text-gray-600\",\n                            children: [\n                                \"Code expires in \",\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"font-semibold text-green-600\",\n                                    children: formatTime(timeLeft)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\auth\\\\OTPVerification.tsx\",\n                                    lineNumber: 149,\n                                    columnNumber: 31\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\auth\\\\OTPVerification.tsx\",\n                            lineNumber: 148,\n                            columnNumber: 13\n                        }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-sm text-red-600\",\n                            children: \"Code has expired\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\auth\\\\OTPVerification.tsx\",\n                            lineNumber: 152,\n                            columnNumber: 13\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\auth\\\\OTPVerification.tsx\",\n                        lineNumber: 146,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                        onClick: handleManualSubmit,\n                        disabled: loading || otp.some((digit)=>digit === ''),\n                        className: \"w-full bg-green-600 hover:bg-green-700 text-white\",\n                        children: loading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center justify-center\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\auth\\\\OTPVerification.tsx\",\n                                    lineNumber: 164,\n                                    columnNumber: 15\n                                }, undefined),\n                                \"Verifying...\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\auth\\\\OTPVerification.tsx\",\n                            lineNumber: 163,\n                            columnNumber: 13\n                        }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center justify-center\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CheckCircle_Mail_RefreshCw_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                    className: \"w-4 h-4 mr-2\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\auth\\\\OTPVerification.tsx\",\n                                    lineNumber: 169,\n                                    columnNumber: 15\n                                }, undefined),\n                                \"Verify Code\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\auth\\\\OTPVerification.tsx\",\n                            lineNumber: 168,\n                            columnNumber: 13\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\auth\\\\OTPVerification.tsx\",\n                        lineNumber: 157,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-center\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-sm text-gray-600 mb-2\",\n                                children: \"Didn't receive the code?\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\auth\\\\OTPVerification.tsx\",\n                                lineNumber: 177,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                onClick: handleResend,\n                                disabled: !canResend || resending,\n                                variant: \"ghost\",\n                                className: \"text-green-600 hover:text-green-700\",\n                                children: resending ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CheckCircle_Mail_RefreshCw_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                            className: \"w-4 h-4 mr-2 animate-spin\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\auth\\\\OTPVerification.tsx\",\n                                            lineNumber: 186,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        \"Sending...\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\auth\\\\OTPVerification.tsx\",\n                                    lineNumber: 185,\n                                    columnNumber: 15\n                                }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CheckCircle_Mail_RefreshCw_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                            className: \"w-4 h-4 mr-2\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\auth\\\\OTPVerification.tsx\",\n                                            lineNumber: 191,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        \"Resend Code\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\auth\\\\OTPVerification.tsx\",\n                                    lineNumber: 190,\n                                    columnNumber: 15\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\auth\\\\OTPVerification.tsx\",\n                                lineNumber: 178,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\auth\\\\OTPVerification.tsx\",\n                        lineNumber: 176,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-blue-50 border border-blue-200 rounded-lg p-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-sm text-blue-700\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                    children: \"Tips:\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\auth\\\\OTPVerification.tsx\",\n                                    lineNumber: 201,\n                                    columnNumber: 13\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\auth\\\\OTPVerification.tsx\",\n                                lineNumber: 200,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                className: \"text-sm text-blue-600 mt-1 space-y-1\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                        children: \"• Check your spam/junk folder\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\auth\\\\OTPVerification.tsx\",\n                                        lineNumber: 204,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                        children: [\n                                            \"• Make sure \",\n                                            email,\n                                            \" is correct\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\auth\\\\OTPVerification.tsx\",\n                                        lineNumber: 205,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                        children: \"• You can paste the code from your email\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\auth\\\\OTPVerification.tsx\",\n                                        lineNumber: 206,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\auth\\\\OTPVerification.tsx\",\n                                lineNumber: 203,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\auth\\\\OTPVerification.tsx\",\n                        lineNumber: 199,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\auth\\\\OTPVerification.tsx\",\n                lineNumber: 113,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\auth\\\\OTPVerification.tsx\",\n        lineNumber: 101,\n        columnNumber: 5\n    }, undefined);\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/auth/OTPVerification.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/icons/Cryptocurrency.tsx":
/*!*************************************************!*\
  !*** ./src/components/icons/Cryptocurrency.tsx ***!
  \*************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Bitcoin: () => (/* binding */ Bitcoin),\n/* harmony export */   Cryptocurrency: () => (/* binding */ Cryptocurrency)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* __next_internal_client_entry_do_not_use__ Cryptocurrency,Bitcoin auto */ \n\nconst Cryptocurrency = ({ className, size = 24 })=>{\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n        width: size,\n        height: size,\n        viewBox: \"0 0 24 24\",\n        fill: \"none\",\n        xmlns: \"http://www.w3.org/2000/svg\",\n        className: className,\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"circle\", {\n                cx: \"12\",\n                cy: \"12\",\n                r: \"10\",\n                stroke: \"currentColor\",\n                strokeWidth: \"2\",\n                fill: \"none\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\icons\\\\Cryptocurrency.tsx\",\n                lineNumber: 20,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                d: \"M8 12h8M12 8v8\",\n                stroke: \"currentColor\",\n                strokeWidth: \"2\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\icons\\\\Cryptocurrency.tsx\",\n                lineNumber: 21,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                d: \"M10 8h4c1.1 0 2 .9 2 2s-.9 2-2 2h-4\",\n                stroke: \"currentColor\",\n                strokeWidth: \"2\",\n                fill: \"none\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\icons\\\\Cryptocurrency.tsx\",\n                lineNumber: 22,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                d: \"M10 14h4c1.1 0 2 .9 2 2s-.9 2-2 2h-4\",\n                stroke: \"currentColor\",\n                strokeWidth: \"2\",\n                fill: \"none\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\icons\\\\Cryptocurrency.tsx\",\n                lineNumber: 23,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"line\", {\n                x1: \"12\",\n                y1: \"6\",\n                x2: \"12\",\n                y2: \"8\",\n                stroke: \"currentColor\",\n                strokeWidth: \"2\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\icons\\\\Cryptocurrency.tsx\",\n                lineNumber: 24,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"line\", {\n                x1: \"12\",\n                y1: \"16\",\n                x2: \"12\",\n                y2: \"18\",\n                stroke: \"currentColor\",\n                strokeWidth: \"2\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\icons\\\\Cryptocurrency.tsx\",\n                lineNumber: 25,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\icons\\\\Cryptocurrency.tsx\",\n        lineNumber: 12,\n        columnNumber: 5\n    }, undefined);\n};\nconst Bitcoin = ({ className, size = 24 })=>{\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n        width: size,\n        height: size,\n        viewBox: \"0 0 24 24\",\n        fill: \"none\",\n        xmlns: \"http://www.w3.org/2000/svg\",\n        className: className,\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"circle\", {\n                cx: \"12\",\n                cy: \"12\",\n                r: \"10\",\n                stroke: \"currentColor\",\n                strokeWidth: \"2\",\n                fill: \"none\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\icons\\\\Cryptocurrency.tsx\",\n                lineNumber: 40,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                d: \"M8 12h4c1.1 0 2-.9 2-2s-.9-2-2-2H8v8h4c1.1 0 2-.9 2-2s-.9-2-2-2\",\n                stroke: \"currentColor\",\n                strokeWidth: \"2\",\n                fill: \"none\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\icons\\\\Cryptocurrency.tsx\",\n                lineNumber: 41,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"line\", {\n                x1: \"10\",\n                y1: \"6\",\n                x2: \"10\",\n                y2: \"8\",\n                stroke: \"currentColor\",\n                strokeWidth: \"2\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\icons\\\\Cryptocurrency.tsx\",\n                lineNumber: 42,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"line\", {\n                x1: \"10\",\n                y1: \"16\",\n                x2: \"10\",\n                y2: \"18\",\n                stroke: \"currentColor\",\n                strokeWidth: \"2\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\icons\\\\Cryptocurrency.tsx\",\n                lineNumber: 43,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"line\", {\n                x1: \"14\",\n                y1: \"6\",\n                x2: \"14\",\n                y2: \"8\",\n                stroke: \"currentColor\",\n                strokeWidth: \"2\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\icons\\\\Cryptocurrency.tsx\",\n                lineNumber: 44,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"line\", {\n                x1: \"14\",\n                y1: \"16\",\n                x2: \"14\",\n                y2: \"18\",\n                stroke: \"currentColor\",\n                strokeWidth: \"2\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\icons\\\\Cryptocurrency.tsx\",\n                lineNumber: 45,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\icons\\\\Cryptocurrency.tsx\",\n        lineNumber: 32,\n        columnNumber: 5\n    }, undefined);\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/icons/Cryptocurrency.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/icons/EcoFriendly.tsx":
/*!**********************************************!*\
  !*** ./src/components/icons/EcoFriendly.tsx ***!
  \**********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Leaf: () => (/* binding */ Leaf),\n/* harmony export */   Recycle: () => (/* binding */ Recycle),\n/* harmony export */   WindTurbine: () => (/* binding */ WindTurbine)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* __next_internal_client_entry_do_not_use__ Leaf,Recycle,WindTurbine auto */ \n\nconst Leaf = ({ className, size = 24 })=>{\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n        width: size,\n        height: size,\n        viewBox: \"0 0 24 24\",\n        fill: \"none\",\n        xmlns: \"http://www.w3.org/2000/svg\",\n        className: className,\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                d: \"M12 2C8 2 5 5 5 9c0 5 7 13 7 13s7-8 7-13c0-4-3-7-7-7z\",\n                stroke: \"currentColor\",\n                strokeWidth: \"2\",\n                fill: \"none\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\icons\\\\EcoFriendly.tsx\",\n                lineNumber: 20,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                d: \"M12 9c0-2-1-3-3-3s-3 1-3 3 1 3 3 3 3-1 3-3z\",\n                stroke: \"currentColor\",\n                strokeWidth: \"2\",\n                fill: \"none\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\icons\\\\EcoFriendly.tsx\",\n                lineNumber: 21,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                d: \"M8 9c0 1 1 2 2 2s2-1 2-2-1-2-2-2-2 1-2 2z\",\n                fill: \"currentColor\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\icons\\\\EcoFriendly.tsx\",\n                lineNumber: 22,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\icons\\\\EcoFriendly.tsx\",\n        lineNumber: 12,\n        columnNumber: 5\n    }, undefined);\n};\nconst Recycle = ({ className, size = 24 })=>{\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n        width: size,\n        height: size,\n        viewBox: \"0 0 24 24\",\n        fill: \"none\",\n        xmlns: \"http://www.w3.org/2000/svg\",\n        className: className,\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                d: \"M7 19H4.815a1.83 1.83 0 01-1.57-.881 1.785 1.785 0 01-.004-1.784L7.196 9.5\",\n                stroke: \"currentColor\",\n                strokeWidth: \"2\",\n                fill: \"none\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\icons\\\\EcoFriendly.tsx\",\n                lineNumber: 37,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                d: \"M11 19h8.203a1.83 1.83 0 001.556-.89 1.784 1.784 0 000-1.775l-1.226-2.12\",\n                stroke: \"currentColor\",\n                strokeWidth: \"2\",\n                fill: \"none\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\icons\\\\EcoFriendly.tsx\",\n                lineNumber: 38,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                d: \"M14 16l-3 3 3 3\",\n                stroke: \"currentColor\",\n                strokeWidth: \"2\",\n                fill: \"none\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\icons\\\\EcoFriendly.tsx\",\n                lineNumber: 39,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                d: \"M8.293 13.596L7.196 9.5l3.1 1.598\",\n                stroke: \"currentColor\",\n                strokeWidth: \"2\",\n                fill: \"none\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\icons\\\\EcoFriendly.tsx\",\n                lineNumber: 40,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                d: \"M9.344 5.811L11.271 2a1.784 1.784 0 011.57-.881c.65 0 1.235.361 1.556.881l3.68 6.361\",\n                stroke: \"currentColor\",\n                strokeWidth: \"2\",\n                fill: \"none\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\icons\\\\EcoFriendly.tsx\",\n                lineNumber: 41,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                d: \"M16 8l3-3-3-3\",\n                stroke: \"currentColor\",\n                strokeWidth: \"2\",\n                fill: \"none\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\icons\\\\EcoFriendly.tsx\",\n                lineNumber: 42,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\icons\\\\EcoFriendly.tsx\",\n        lineNumber: 29,\n        columnNumber: 5\n    }, undefined);\n};\nconst WindTurbine = ({ className, size = 24 })=>{\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n        width: size,\n        height: size,\n        viewBox: \"0 0 24 24\",\n        fill: \"none\",\n        xmlns: \"http://www.w3.org/2000/svg\",\n        className: className,\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"line\", {\n                x1: \"12\",\n                y1: \"12\",\n                x2: \"12\",\n                y2: \"22\",\n                stroke: \"currentColor\",\n                strokeWidth: \"2\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\icons\\\\EcoFriendly.tsx\",\n                lineNumber: 57,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                d: \"M12 12L8 4c-1-2 0-4 2-4s3 2 2 4l-2 8z\",\n                stroke: \"currentColor\",\n                strokeWidth: \"2\",\n                fill: \"none\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\icons\\\\EcoFriendly.tsx\",\n                lineNumber: 58,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                d: \"M12 12l8-4c2-1 4 0 4 2s-2 3-4 2l-8-2z\",\n                stroke: \"currentColor\",\n                strokeWidth: \"2\",\n                fill: \"none\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\icons\\\\EcoFriendly.tsx\",\n                lineNumber: 59,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                d: \"M12 12l-4 8c-1 2-3 2-4 0s0-3 2-4l8-2z\",\n                stroke: \"currentColor\",\n                strokeWidth: \"2\",\n                fill: \"none\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\icons\\\\EcoFriendly.tsx\",\n                lineNumber: 60,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"circle\", {\n                cx: \"12\",\n                cy: \"12\",\n                r: \"1\",\n                fill: \"currentColor\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\icons\\\\EcoFriendly.tsx\",\n                lineNumber: 61,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"line\", {\n                x1: \"10\",\n                y1: \"22\",\n                x2: \"14\",\n                y2: \"22\",\n                stroke: \"currentColor\",\n                strokeWidth: \"2\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\icons\\\\EcoFriendly.tsx\",\n                lineNumber: 62,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\icons\\\\EcoFriendly.tsx\",\n        lineNumber: 49,\n        columnNumber: 5\n    }, undefined);\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/icons/EcoFriendly.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/icons/MiningRig.tsx":
/*!********************************************!*\
  !*** ./src/components/icons/MiningRig.tsx ***!
  \********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   MiningRig: () => (/* binding */ MiningRig)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* __next_internal_client_entry_do_not_use__ MiningRig auto */ \n\nconst MiningRig = ({ className, size = 24 })=>{\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n        width: size,\n        height: size,\n        viewBox: \"0 0 24 24\",\n        fill: \"none\",\n        xmlns: \"http://www.w3.org/2000/svg\",\n        className: className,\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"rect\", {\n                x: \"2\",\n                y: \"6\",\n                width: \"20\",\n                height: \"12\",\n                rx: \"2\",\n                stroke: \"currentColor\",\n                strokeWidth: \"2\",\n                fill: \"none\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\icons\\\\MiningRig.tsx\",\n                lineNumber: 20,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"rect\", {\n                x: \"4\",\n                y: \"8\",\n                width: \"3\",\n                height: \"3\",\n                rx: \"0.5\",\n                fill: \"currentColor\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\icons\\\\MiningRig.tsx\",\n                lineNumber: 21,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"rect\", {\n                x: \"8\",\n                y: \"8\",\n                width: \"3\",\n                height: \"3\",\n                rx: \"0.5\",\n                fill: \"currentColor\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\icons\\\\MiningRig.tsx\",\n                lineNumber: 22,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"rect\", {\n                x: \"12\",\n                y: \"8\",\n                width: \"3\",\n                height: \"3\",\n                rx: \"0.5\",\n                fill: \"currentColor\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\icons\\\\MiningRig.tsx\",\n                lineNumber: 23,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"rect\", {\n                x: \"16\",\n                y: \"8\",\n                width: \"3\",\n                height: \"3\",\n                rx: \"0.5\",\n                fill: \"currentColor\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\icons\\\\MiningRig.tsx\",\n                lineNumber: 24,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"rect\", {\n                x: \"4\",\n                y: \"12\",\n                width: \"3\",\n                height: \"3\",\n                rx: \"0.5\",\n                fill: \"currentColor\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\icons\\\\MiningRig.tsx\",\n                lineNumber: 25,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"rect\", {\n                x: \"8\",\n                y: \"12\",\n                width: \"3\",\n                height: \"3\",\n                rx: \"0.5\",\n                fill: \"currentColor\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\icons\\\\MiningRig.tsx\",\n                lineNumber: 26,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"rect\", {\n                x: \"12\",\n                y: \"12\",\n                width: \"3\",\n                height: \"3\",\n                rx: \"0.5\",\n                fill: \"currentColor\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\icons\\\\MiningRig.tsx\",\n                lineNumber: 27,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"rect\", {\n                x: \"16\",\n                y: \"12\",\n                width: \"3\",\n                height: \"3\",\n                rx: \"0.5\",\n                fill: \"currentColor\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\icons\\\\MiningRig.tsx\",\n                lineNumber: 28,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"circle\", {\n                cx: \"20\",\n                cy: \"4\",\n                r: \"1\",\n                fill: \"currentColor\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\icons\\\\MiningRig.tsx\",\n                lineNumber: 29,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"line\", {\n                x1: \"20\",\n                y1: \"4\",\n                x2: \"20\",\n                y2: \"6\",\n                stroke: \"currentColor\",\n                strokeWidth: \"2\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\icons\\\\MiningRig.tsx\",\n                lineNumber: 30,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"line\", {\n                x1: \"18\",\n                y1: \"2\",\n                x2: \"22\",\n                y2: \"2\",\n                stroke: \"currentColor\",\n                strokeWidth: \"2\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\icons\\\\MiningRig.tsx\",\n                lineNumber: 31,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"line\", {\n                x1: \"19\",\n                y1: \"1\",\n                x2: \"21\",\n                y2: \"3\",\n                stroke: \"currentColor\",\n                strokeWidth: \"1\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\icons\\\\MiningRig.tsx\",\n                lineNumber: 32,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"line\", {\n                x1: \"21\",\n                y1: \"1\",\n                x2: \"19\",\n                y2: \"3\",\n                stroke: \"currentColor\",\n                strokeWidth: \"1\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\icons\\\\MiningRig.tsx\",\n                lineNumber: 33,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\icons\\\\MiningRig.tsx\",\n        lineNumber: 12,\n        columnNumber: 5\n    }, undefined);\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/icons/MiningRig.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/icons/SolarPanel.tsx":
/*!*********************************************!*\
  !*** ./src/components/icons/SolarPanel.tsx ***!
  \*********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   SolarPanel: () => (/* binding */ SolarPanel)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* __next_internal_client_entry_do_not_use__ SolarPanel auto */ \n\nconst SolarPanel = ({ className, size = 24 })=>{\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n        width: size,\n        height: size,\n        viewBox: \"0 0 24 24\",\n        fill: \"none\",\n        xmlns: \"http://www.w3.org/2000/svg\",\n        className: className,\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"rect\", {\n                x: \"2\",\n                y: \"4\",\n                width: \"20\",\n                height: \"16\",\n                rx: \"2\",\n                stroke: \"currentColor\",\n                strokeWidth: \"2\",\n                fill: \"none\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\icons\\\\SolarPanel.tsx\",\n                lineNumber: 20,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"line\", {\n                x1: \"2\",\n                y1: \"8\",\n                x2: \"22\",\n                y2: \"8\",\n                stroke: \"currentColor\",\n                strokeWidth: \"2\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\icons\\\\SolarPanel.tsx\",\n                lineNumber: 21,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"line\", {\n                x1: \"2\",\n                y1: \"12\",\n                x2: \"22\",\n                y2: \"12\",\n                stroke: \"currentColor\",\n                strokeWidth: \"2\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\icons\\\\SolarPanel.tsx\",\n                lineNumber: 22,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"line\", {\n                x1: \"2\",\n                y1: \"16\",\n                x2: \"22\",\n                y2: \"16\",\n                stroke: \"currentColor\",\n                strokeWidth: \"2\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\icons\\\\SolarPanel.tsx\",\n                lineNumber: 23,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"line\", {\n                x1: \"6\",\n                y1: \"4\",\n                x2: \"6\",\n                y2: \"20\",\n                stroke: \"currentColor\",\n                strokeWidth: \"2\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\icons\\\\SolarPanel.tsx\",\n                lineNumber: 24,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"line\", {\n                x1: \"10\",\n                y1: \"4\",\n                x2: \"10\",\n                y2: \"20\",\n                stroke: \"currentColor\",\n                strokeWidth: \"2\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\icons\\\\SolarPanel.tsx\",\n                lineNumber: 25,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"line\", {\n                x1: \"14\",\n                y1: \"4\",\n                x2: \"14\",\n                y2: \"20\",\n                stroke: \"currentColor\",\n                strokeWidth: \"2\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\icons\\\\SolarPanel.tsx\",\n                lineNumber: 26,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"line\", {\n                x1: \"18\",\n                y1: \"4\",\n                x2: \"18\",\n                y2: \"20\",\n                stroke: \"currentColor\",\n                strokeWidth: \"2\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\icons\\\\SolarPanel.tsx\",\n                lineNumber: 27,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"circle\", {\n                cx: \"20\",\n                cy: \"2\",\n                r: \"1\",\n                fill: \"currentColor\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\icons\\\\SolarPanel.tsx\",\n                lineNumber: 28,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                d: \"M19 1l1 1-1 1-1-1z\",\n                fill: \"currentColor\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\icons\\\\SolarPanel.tsx\",\n                lineNumber: 29,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\icons\\\\SolarPanel.tsx\",\n        lineNumber: 12,\n        columnNumber: 5\n    }, undefined);\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/icons/SolarPanel.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/icons/index.ts":
/*!***************************************!*\
  !*** ./src/components/icons/index.ts ***!
  \***************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Bitcoin: () => (/* reexport safe */ _Cryptocurrency__WEBPACK_IMPORTED_MODULE_2__.Bitcoin),\n/* harmony export */   Cryptocurrency: () => (/* reexport safe */ _Cryptocurrency__WEBPACK_IMPORTED_MODULE_2__.Cryptocurrency),\n/* harmony export */   Leaf: () => (/* reexport safe */ _EcoFriendly__WEBPACK_IMPORTED_MODULE_3__.Leaf),\n/* harmony export */   MiningRig: () => (/* reexport safe */ _MiningRig__WEBPACK_IMPORTED_MODULE_1__.MiningRig),\n/* harmony export */   Recycle: () => (/* reexport safe */ _EcoFriendly__WEBPACK_IMPORTED_MODULE_3__.Recycle),\n/* harmony export */   SolarPanel: () => (/* reexport safe */ _SolarPanel__WEBPACK_IMPORTED_MODULE_0__.SolarPanel),\n/* harmony export */   WindTurbine: () => (/* reexport safe */ _EcoFriendly__WEBPACK_IMPORTED_MODULE_3__.WindTurbine)\n/* harmony export */ });\n/* harmony import */ var _SolarPanel__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./SolarPanel */ \"(ssr)/./src/components/icons/SolarPanel.tsx\");\n/* harmony import */ var _MiningRig__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./MiningRig */ \"(ssr)/./src/components/icons/MiningRig.tsx\");\n/* harmony import */ var _Cryptocurrency__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./Cryptocurrency */ \"(ssr)/./src/components/icons/Cryptocurrency.tsx\");\n/* harmony import */ var _EcoFriendly__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./EcoFriendly */ \"(ssr)/./src/components/icons/EcoFriendly.tsx\");\n\n\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9zcmMvY29tcG9uZW50cy9pY29ucy9pbmRleC50cyIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7Ozs7OztBQUEwQztBQUNGO0FBQ21CO0FBQ0EiLCJzb3VyY2VzIjpbIkM6XFxVc2Vyc1xcZHJlYW1cXERlc2t0b3BcXEhhc2hfTWluaW5nc1xcaGFzaGNvcmV4XFxzcmNcXGNvbXBvbmVudHNcXGljb25zXFxpbmRleC50cyJdLCJzb3VyY2VzQ29udGVudCI6WyJleHBvcnQgeyBTb2xhclBhbmVsIH0gZnJvbSAnLi9Tb2xhclBhbmVsJztcbmV4cG9ydCB7IE1pbmluZ1JpZyB9IGZyb20gJy4vTWluaW5nUmlnJztcbmV4cG9ydCB7IENyeXB0b2N1cnJlbmN5LCBCaXRjb2luIH0gZnJvbSAnLi9DcnlwdG9jdXJyZW5jeSc7XG5leHBvcnQgeyBMZWFmLCBSZWN5Y2xlLCBXaW5kVHVyYmluZSB9IGZyb20gJy4vRWNvRnJpZW5kbHknO1xuIl0sIm5hbWVzIjpbIlNvbGFyUGFuZWwiLCJNaW5pbmdSaWciLCJDcnlwdG9jdXJyZW5jeSIsIkJpdGNvaW4iLCJMZWFmIiwiUmVjeWNsZSIsIldpbmRUdXJiaW5lIl0sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./src/components/icons/index.ts\n");

/***/ }),

/***/ "(ssr)/./src/components/ui/Button.tsx":
/*!**************************************!*\
  !*** ./src/components/ui/Button.tsx ***!
  \**************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Button: () => (/* binding */ Button),\n/* harmony export */   buttonVariants: () => (/* binding */ buttonVariants)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var class_variance_authority__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! class-variance-authority */ \"(ssr)/./node_modules/class-variance-authority/dist/index.mjs\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/utils */ \"(ssr)/./src/lib/utils.ts\");\n/* __next_internal_client_entry_do_not_use__ Button,buttonVariants auto */ \n\n\n\nconst buttonVariants = (0,class_variance_authority__WEBPACK_IMPORTED_MODULE_2__.cva)('inline-flex items-center justify-center rounded-xl font-semibold focus:outline-none focus:ring-2 focus:ring-offset-2 disabled:opacity-50 disabled:pointer-events-none shadow-lg', {\n    variants: {\n        variant: {\n            primary: 'bg-yellow-500 text-white focus:ring-yellow-500',\n            secondary: 'bg-gray-200 text-gray-800 focus:ring-gray-500',\n            success: 'bg-emerald-500 text-white focus:ring-emerald-500',\n            danger: 'bg-red-500 text-white focus:ring-red-500',\n            warning: 'bg-yellow-500 text-white focus:ring-yellow-500',\n            destructive: 'bg-red-600 text-white focus:ring-red-500',\n            outline: 'border-2 border-yellow-500 bg-transparent text-yellow-600 focus:ring-yellow-500',\n            ghost: 'text-gray-600 focus:ring-yellow-500 rounded-lg',\n            link: 'text-yellow-600 underline-offset-4 focus:ring-yellow-500',\n            premium: 'bg-slate-800 text-white focus:ring-slate-500',\n            glass: 'glass-morphism text-slate-900 backdrop-blur-xl border border-white/20'\n        },\n        size: {\n            sm: 'h-10 px-4 text-sm rounded-lg',\n            md: 'h-12 px-6 text-base rounded-xl',\n            lg: 'h-14 px-8 text-lg rounded-xl',\n            xl: 'h-16 px-10 text-xl rounded-2xl font-bold',\n            icon: 'h-12 w-12 rounded-xl'\n        }\n    },\n    defaultVariants: {\n        variant: 'primary',\n        size: 'md'\n    }\n});\nconst Button = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1___default().forwardRef(({ className, variant, size, loading, leftIcon, rightIcon, children, disabled, ...props }, ref)=>{\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_3__.cn)(buttonVariants({\n            variant,\n            size,\n            className\n        })),\n        ref: ref,\n        disabled: disabled || loading,\n        ...props,\n        children: [\n            loading && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"mr-2\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"spinner\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\ui\\\\Button.tsx\",\n                    lineNumber: 58,\n                    columnNumber: 13\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\ui\\\\Button.tsx\",\n                lineNumber: 57,\n                columnNumber: 11\n            }, undefined),\n            leftIcon && !loading && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                className: \"mr-2\",\n                children: leftIcon\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\ui\\\\Button.tsx\",\n                lineNumber: 61,\n                columnNumber: 34\n            }, undefined),\n            children,\n            rightIcon && !loading && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                className: \"ml-2\",\n                children: rightIcon\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\ui\\\\Button.tsx\",\n                lineNumber: 63,\n                columnNumber: 35\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\ui\\\\Button.tsx\",\n        lineNumber: 50,\n        columnNumber: 7\n    }, undefined);\n});\nButton.displayName = 'Button';\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/ui/Button.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/ui/Card.tsx":
/*!************************************!*\
  !*** ./src/components/ui/Card.tsx ***!
  \************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Card: () => (/* binding */ Card),\n/* harmony export */   CardContent: () => (/* binding */ CardContent),\n/* harmony export */   CardDescription: () => (/* binding */ CardDescription),\n/* harmony export */   CardFooter: () => (/* binding */ CardFooter),\n/* harmony export */   CardHeader: () => (/* binding */ CardHeader),\n/* harmony export */   CardTitle: () => (/* binding */ CardTitle)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/utils */ \"(ssr)/./src/lib/utils.ts\");\n/* __next_internal_client_entry_do_not_use__ Card,CardHeader,CardTitle,CardDescription,CardContent,CardFooter auto */ \n\n\nconst Card = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1___default().forwardRef(({ className, children, ...props }, ref)=>{\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)('rounded-2xl border border-gray-200/50 bg-white/90 backdrop-blur-xl shadow-xl overflow-hidden', className),\n        ...props,\n        children: children\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\ui\\\\Card.tsx\",\n        lineNumber: 13,\n        columnNumber: 7\n    }, undefined);\n});\nCard.displayName = 'Card';\nconst CardHeader = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1___default().forwardRef(({ className, children, ...props }, ref)=>{\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)('flex flex-col space-y-1.5 p-6 pb-4', className),\n        ...props,\n        children: children\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\ui\\\\Card.tsx\",\n        lineNumber: 36,\n        columnNumber: 7\n    }, undefined);\n});\nCardHeader.displayName = 'CardHeader';\nconst CardTitle = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1___default().forwardRef(({ className, children, ...props }, ref)=>{\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)('text-xl font-semibold leading-none tracking-tight text-dark-900', className),\n        ...props,\n        children: children\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\ui\\\\Card.tsx\",\n        lineNumber: 56,\n        columnNumber: 7\n    }, undefined);\n});\nCardTitle.displayName = 'CardTitle';\nconst CardDescription = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1___default().forwardRef(({ className, children, ...props }, ref)=>{\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)('text-sm text-gray-500', className),\n        ...props,\n        children: children\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\ui\\\\Card.tsx\",\n        lineNumber: 76,\n        columnNumber: 7\n    }, undefined);\n});\nCardDescription.displayName = 'CardDescription';\nconst CardContent = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1___default().forwardRef(({ className, children, ...props }, ref)=>{\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)('p-6 pt-0', className),\n        ...props,\n        children: children\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\ui\\\\Card.tsx\",\n        lineNumber: 96,\n        columnNumber: 7\n    }, undefined);\n});\nCardContent.displayName = 'CardContent';\nconst CardFooter = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1___default().forwardRef(({ className, children, ...props }, ref)=>{\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)('flex items-center p-6 pt-0', className),\n        ...props,\n        children: children\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\ui\\\\Card.tsx\",\n        lineNumber: 116,\n        columnNumber: 7\n    }, undefined);\n});\nCardFooter.displayName = 'CardFooter';\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/ui/Card.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/ui/ConfirmDialog.tsx":
/*!*********************************************!*\
  !*** ./src/components/ui/ConfirmDialog.tsx ***!
  \*********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ConfirmDialog: () => (/* binding */ ConfirmDialog),\n/* harmony export */   \"default\": () => (/* binding */ ConfirmDialog),\n/* harmony export */   useConfirmDialog: () => (/* binding */ useConfirmDialog)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var react_dom__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react-dom */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-dom.js\");\n/* harmony import */ var react_dom__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(react_dom__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_CheckCircle_Info_X_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,CheckCircle,Info,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/triangle-alert.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_CheckCircle_Info_X_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,CheckCircle,Info,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/circle-check-big.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_CheckCircle_Info_X_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,CheckCircle,Info,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/info.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_CheckCircle_Info_X_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,CheckCircle,Info,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/x.js\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/utils */ \"(ssr)/./src/lib/utils.ts\");\n/* harmony import */ var _Button__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./Button */ \"(ssr)/./src/components/ui/Button.tsx\");\n/* __next_internal_client_entry_do_not_use__ ConfirmDialog,useConfirmDialog,default auto */ \n\n\n\n\n\nconst ConfirmDialog = ({ isOpen, onClose, onConfirm, title, message, confirmText = 'Confirm', cancelText = 'Cancel', variant = 'default', darkMode = false, loading = false })=>{\n    if (!isOpen) return null;\n    const getIcon = ()=>{\n        switch(variant){\n            case 'danger':\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_CheckCircle_Info_X_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                    className: \"h-6 w-6 text-red-500\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\ui\\\\ConfirmDialog.tsx\",\n                    lineNumber: 39,\n                    columnNumber: 16\n                }, undefined);\n            case 'warning':\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_CheckCircle_Info_X_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                    className: \"h-6 w-6 text-yellow-500\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\ui\\\\ConfirmDialog.tsx\",\n                    lineNumber: 41,\n                    columnNumber: 16\n                }, undefined);\n            case 'success':\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_CheckCircle_Info_X_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                    className: \"h-6 w-6 text-green-500\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\ui\\\\ConfirmDialog.tsx\",\n                    lineNumber: 43,\n                    columnNumber: 16\n                }, undefined);\n            default:\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_CheckCircle_Info_X_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                    className: \"h-6 w-6 text-blue-500\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\ui\\\\ConfirmDialog.tsx\",\n                    lineNumber: 45,\n                    columnNumber: 16\n                }, undefined);\n        }\n    };\n    const getConfirmButtonVariant = ()=>{\n        switch(variant){\n            case 'danger':\n                return 'destructive';\n            case 'warning':\n                return 'warning';\n            case 'success':\n                return 'default';\n            default:\n                return 'default';\n        }\n    };\n    const modalContent = /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"fixed inset-0 z-50 flex items-center justify-center p-4\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"absolute inset-0 bg-black bg-opacity-50 transition-opacity\",\n                onClick: onClose\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\ui\\\\ConfirmDialog.tsx\",\n                lineNumber: 65,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_3__.cn)('relative w-full max-w-md rounded-xl shadow-xl transform transition-all', darkMode ? 'bg-slate-800 border border-slate-700' : 'bg-white'),\n                onClick: (e)=>e.stopPropagation(),\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_3__.cn)('flex items-center justify-between p-6 border-b', darkMode ? 'border-slate-700' : 'border-gray-200'),\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center space-x-3\",\n                                children: [\n                                    getIcon(),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_3__.cn)('text-lg font-semibold', darkMode ? 'text-white' : 'text-gray-900'),\n                                        children: title\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\ui\\\\ConfirmDialog.tsx\",\n                                        lineNumber: 85,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\ui\\\\ConfirmDialog.tsx\",\n                                lineNumber: 83,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_Button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                variant: \"ghost\",\n                                size: \"icon\",\n                                onClick: onClose,\n                                disabled: loading,\n                                className: \"h-8 w-8 rounded-full\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_CheckCircle_Info_X_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                    className: \"h-4 w-4\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\ui\\\\ConfirmDialog.tsx\",\n                                    lineNumber: 99,\n                                    columnNumber: 13\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\ui\\\\ConfirmDialog.tsx\",\n                                lineNumber: 92,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\ui\\\\ConfirmDialog.tsx\",\n                        lineNumber: 79,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"p-6\",\n                        children: typeof message === 'string' ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_3__.cn)('text-sm leading-relaxed', darkMode ? 'text-slate-300' : 'text-gray-600'),\n                            children: message\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\ui\\\\ConfirmDialog.tsx\",\n                            lineNumber: 106,\n                            columnNumber: 13\n                        }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_3__.cn)('text-sm leading-relaxed', darkMode ? 'text-slate-300' : 'text-gray-600'),\n                            children: message\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\ui\\\\ConfirmDialog.tsx\",\n                            lineNumber: 113,\n                            columnNumber: 13\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\ui\\\\ConfirmDialog.tsx\",\n                        lineNumber: 104,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_3__.cn)('flex items-center justify-end space-x-3 p-6 border-t', darkMode ? 'border-slate-700' : 'border-gray-200'),\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_Button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                variant: \"outline\",\n                                onClick: onClose,\n                                disabled: loading,\n                                className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_3__.cn)(darkMode ? 'border-slate-600 text-slate-300 hover:bg-slate-700 hover:text-white' : ''),\n                                children: cancelText\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\ui\\\\ConfirmDialog.tsx\",\n                                lineNumber: 127,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_Button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                variant: getConfirmButtonVariant(),\n                                onClick: onConfirm,\n                                disabled: loading,\n                                className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_3__.cn)(loading && 'opacity-50 cursor-not-allowed'),\n                                children: loading ? 'Processing...' : confirmText\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\ui\\\\ConfirmDialog.tsx\",\n                                lineNumber: 137,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\ui\\\\ConfirmDialog.tsx\",\n                        lineNumber: 123,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\ui\\\\ConfirmDialog.tsx\",\n                lineNumber: 71,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\ui\\\\ConfirmDialog.tsx\",\n        lineNumber: 63,\n        columnNumber: 5\n    }, undefined);\n    return /*#__PURE__*/ (0,react_dom__WEBPACK_IMPORTED_MODULE_2__.createPortal)(modalContent, document.body);\n};\n// Hook for easier usage\nconst useConfirmDialog = ()=>{\n    const [dialogState, setDialogState] = react__WEBPACK_IMPORTED_MODULE_1___default().useState({\n        isOpen: false,\n        title: '',\n        message: '',\n        onConfirm: {\n            \"useConfirmDialog.useState\": ()=>{}\n        }[\"useConfirmDialog.useState\"]\n    });\n    const [loading, setLoading] = react__WEBPACK_IMPORTED_MODULE_1___default().useState(false);\n    const showConfirm = (options)=>{\n        setDialogState({\n            isOpen: true,\n            ...options,\n            onConfirm: async ()=>{\n                setLoading(true);\n                try {\n                    await options.onConfirm();\n                    setDialogState((prev)=>({\n                            ...prev,\n                            isOpen: false\n                        }));\n                } catch (error) {\n                    console.error('Confirm action failed:', error);\n                } finally{\n                    setLoading(false);\n                }\n            }\n        });\n    };\n    const hideConfirm = ()=>{\n        if (!loading) {\n            setDialogState((prev)=>({\n                    ...prev,\n                    isOpen: false\n                }));\n        }\n    };\n    const ConfirmDialogComponent = ()=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ConfirmDialog, {\n            isOpen: dialogState.isOpen,\n            onClose: hideConfirm,\n            onConfirm: dialogState.onConfirm,\n            title: dialogState.title,\n            message: dialogState.message,\n            variant: dialogState.variant,\n            confirmText: dialogState.confirmText,\n            cancelText: dialogState.cancelText,\n            darkMode: dialogState.darkMode,\n            loading: loading\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\ui\\\\ConfirmDialog.tsx\",\n            lineNumber: 208,\n            columnNumber: 5\n        }, undefined);\n    return {\n        showConfirm,\n        hideConfirm,\n        ConfirmDialog: ConfirmDialogComponent,\n        loading\n    };\n};\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/ui/ConfirmDialog.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/ui/Input.tsx":
/*!*************************************!*\
  !*** ./src/components/ui/Input.tsx ***!
  \*************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Input: () => (/* binding */ Input)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/utils */ \"(ssr)/./src/lib/utils.ts\");\n/* __next_internal_client_entry_do_not_use__ Input auto */ \n\n\nconst Input = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1___default().forwardRef(({ className, type, label, error, leftIcon, rightIcon, ...props }, ref)=>{\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"w-full\",\n        children: [\n            label && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                className: \"block text-sm font-medium text-dark-700 mb-2\",\n                children: label\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\ui\\\\Input.tsx\",\n                lineNumber: 18,\n                columnNumber: 11\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"relative\",\n                children: [\n                    leftIcon && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            className: \"text-gray-400\",\n                            children: leftIcon\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\ui\\\\Input.tsx\",\n                            lineNumber: 25,\n                            columnNumber: 15\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\ui\\\\Input.tsx\",\n                        lineNumber: 24,\n                        columnNumber: 13\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                        type: type,\n                        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)('flex h-14 w-full rounded-xl border-2 border-gray-200 bg-white/90 backdrop-blur-sm px-4 py-4 text-base shadow-inner focus:shadow-lg placeholder:text-gray-400 focus:outline-none focus:ring-2 focus:ring-solar-500 focus:border-solar-500 disabled:cursor-not-allowed disabled:opacity-50 transition-all duration-300 hover:border-gray-300', leftIcon && 'pl-12', rightIcon && 'pr-12', error && 'border-red-500 focus:ring-red-500 focus:border-red-500', className),\n                        ref: ref,\n                        ...props\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\ui\\\\Input.tsx\",\n                        lineNumber: 28,\n                        columnNumber: 11\n                    }, undefined),\n                    rightIcon && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute inset-y-0 right-0 pr-3 flex items-center\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            className: \"text-gray-400\",\n                            children: rightIcon\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\ui\\\\Input.tsx\",\n                            lineNumber: 42,\n                            columnNumber: 15\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\ui\\\\Input.tsx\",\n                        lineNumber: 41,\n                        columnNumber: 13\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\ui\\\\Input.tsx\",\n                lineNumber: 22,\n                columnNumber: 9\n            }, undefined),\n            error && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                className: \"mt-1 text-sm text-red-600\",\n                children: error\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\ui\\\\Input.tsx\",\n                lineNumber: 47,\n                columnNumber: 11\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\ui\\\\Input.tsx\",\n        lineNumber: 16,\n        columnNumber: 7\n    }, undefined);\n});\nInput.displayName = 'Input';\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/ui/Input.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/ui/Loading.tsx":
/*!***************************************!*\
  !*** ./src/components/ui/Loading.tsx ***!
  \***************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Loading: () => (/* binding */ Loading),\n/* harmony export */   LoadingOverlay: () => (/* binding */ LoadingOverlay)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/utils */ \"(ssr)/./src/lib/utils.ts\");\n/* __next_internal_client_entry_do_not_use__ Loading,LoadingOverlay auto */ \n\n\nconst Loading = ({ size = 'md', className, text })=>{\n    const sizeClasses = {\n        sm: 'h-4 w-4',\n        md: 'h-8 w-8',\n        lg: 'h-12 w-12'\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)('flex flex-col items-center justify-center', className),\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)('animate-spin rounded-full border-2 border-gray-300 border-t-solar-500', sizeClasses[size])\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\ui\\\\Loading.tsx\",\n                lineNumber: 21,\n                columnNumber: 7\n            }, undefined),\n            text && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                className: \"mt-2 text-sm text-gray-600\",\n                children: text\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\ui\\\\Loading.tsx\",\n                lineNumber: 28,\n                columnNumber: 9\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\ui\\\\Loading.tsx\",\n        lineNumber: 20,\n        columnNumber: 5\n    }, undefined);\n};\nconst LoadingOverlay = ({ isLoading, text = 'Loading...', children })=>{\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"relative\",\n        children: [\n            children,\n            isLoading && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"absolute inset-0 bg-white bg-opacity-75 flex items-center justify-center z-10\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Loading, {\n                    text: text\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\ui\\\\Loading.tsx\",\n                    lineNumber: 50,\n                    columnNumber: 11\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\ui\\\\Loading.tsx\",\n                lineNumber: 49,\n                columnNumber: 9\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\ui\\\\Loading.tsx\",\n        lineNumber: 46,\n        columnNumber: 5\n    }, undefined);\n};\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/ui/Loading.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/ui/MessageBox.tsx":
/*!******************************************!*\
  !*** ./src/components/ui/MessageBox.tsx ***!
  \******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   MessageBox: () => (/* binding */ MessageBox),\n/* harmony export */   useMessageBox: () => (/* binding */ useMessageBox)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var react_dom__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react-dom */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-dom.js\");\n/* harmony import */ var react_dom__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(react_dom__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _barrel_optimize_names_AlertCircle_AlertTriangle_CheckCircle_Info_X_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,AlertTriangle,CheckCircle,Info,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/circle-alert.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_AlertTriangle_CheckCircle_Info_X_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,AlertTriangle,CheckCircle,Info,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/triangle-alert.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_AlertTriangle_CheckCircle_Info_X_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,AlertTriangle,CheckCircle,Info,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/circle-check-big.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_AlertTriangle_CheckCircle_Info_X_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,AlertTriangle,CheckCircle,Info,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/info.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_AlertTriangle_CheckCircle_Info_X_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,AlertTriangle,CheckCircle,Info,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/x.js\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/utils */ \"(ssr)/./src/lib/utils.ts\");\n/* harmony import */ var _Button__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./Button */ \"(ssr)/./src/components/ui/Button.tsx\");\n/* __next_internal_client_entry_do_not_use__ MessageBox,useMessageBox auto */ \n\n\n\n\n\nconst MessageBox = ({ isOpen, onClose, title, message, variant = 'info', darkMode = false, showCloseButton = true, buttonText = 'OK', size = 'md' })=>{\n    if (!isOpen) return null;\n    const getIcon = ()=>{\n        switch(variant){\n            case 'error':\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_AlertTriangle_CheckCircle_Info_X_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                    className: \"h-6 w-6 text-red-500\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\ui\\\\MessageBox.tsx\",\n                    lineNumber: 37,\n                    columnNumber: 16\n                }, undefined);\n            case 'warning':\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_AlertTriangle_CheckCircle_Info_X_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                    className: \"h-6 w-6 text-yellow-500\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\ui\\\\MessageBox.tsx\",\n                    lineNumber: 39,\n                    columnNumber: 16\n                }, undefined);\n            case 'success':\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_AlertTriangle_CheckCircle_Info_X_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                    className: \"h-6 w-6 text-green-500\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\ui\\\\MessageBox.tsx\",\n                    lineNumber: 41,\n                    columnNumber: 16\n                }, undefined);\n            default:\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_AlertTriangle_CheckCircle_Info_X_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                    className: \"h-6 w-6 text-blue-500\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\ui\\\\MessageBox.tsx\",\n                    lineNumber: 43,\n                    columnNumber: 16\n                }, undefined);\n        }\n    };\n    const getButtonVariant = ()=>{\n        switch(variant){\n            case 'error':\n                return 'danger';\n            case 'warning':\n                return 'warning';\n            case 'success':\n                return 'success';\n            default:\n                return 'primary';\n        }\n    };\n    const sizeClasses = {\n        sm: 'max-w-sm',\n        md: 'max-w-md',\n        lg: 'max-w-lg'\n    };\n    const modalContent = /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"fixed inset-0 z-50 flex items-center justify-center p-4\",\n        onClick: onClose,\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"absolute inset-0 bg-black/50 backdrop-blur-sm\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\ui\\\\MessageBox.tsx\",\n                lineNumber: 72,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_3__.cn)('relative w-full rounded-xl shadow-xl transform transition-all', darkMode ? 'bg-slate-800 border border-slate-700' : 'bg-white', sizeClasses[size]),\n                onClick: (e)=>e.stopPropagation(),\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_3__.cn)('flex items-center justify-between p-6 border-b', darkMode ? 'border-slate-700' : 'border-gray-200'),\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center space-x-3\",\n                                children: [\n                                    getIcon(),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_3__.cn)('text-lg font-semibold', darkMode ? 'text-white' : 'text-gray-900'),\n                                        children: title\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\ui\\\\MessageBox.tsx\",\n                                        lineNumber: 90,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\ui\\\\MessageBox.tsx\",\n                                lineNumber: 88,\n                                columnNumber: 11\n                            }, undefined),\n                            showCloseButton && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_Button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                variant: \"ghost\",\n                                size: \"icon\",\n                                onClick: onClose,\n                                className: \"h-8 w-8 rounded-full\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_AlertTriangle_CheckCircle_Info_X_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                    className: \"h-4 w-4\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\ui\\\\MessageBox.tsx\",\n                                    lineNumber: 104,\n                                    columnNumber: 15\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\ui\\\\MessageBox.tsx\",\n                                lineNumber: 98,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\ui\\\\MessageBox.tsx\",\n                        lineNumber: 84,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"p-6\",\n                        children: typeof message === 'string' ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_3__.cn)('text-sm leading-relaxed', darkMode ? 'text-slate-300' : 'text-gray-600'),\n                            children: message\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\ui\\\\MessageBox.tsx\",\n                            lineNumber: 112,\n                            columnNumber: 13\n                        }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_3__.cn)('text-sm leading-relaxed', darkMode ? 'text-slate-300' : 'text-gray-600'),\n                            children: message\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\ui\\\\MessageBox.tsx\",\n                            lineNumber: 119,\n                            columnNumber: 13\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\ui\\\\MessageBox.tsx\",\n                        lineNumber: 110,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_3__.cn)('flex items-center justify-end p-6 border-t', darkMode ? 'border-slate-700' : 'border-gray-200'),\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_Button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                            variant: getButtonVariant(),\n                            onClick: onClose,\n                            className: \"min-w-[80px]\",\n                            children: buttonText\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\ui\\\\MessageBox.tsx\",\n                            lineNumber: 133,\n                            columnNumber: 11\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\ui\\\\MessageBox.tsx\",\n                        lineNumber: 129,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\ui\\\\MessageBox.tsx\",\n                lineNumber: 75,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\ui\\\\MessageBox.tsx\",\n        lineNumber: 67,\n        columnNumber: 5\n    }, undefined);\n    return /*#__PURE__*/ (0,react_dom__WEBPACK_IMPORTED_MODULE_2__.createPortal)(modalContent, document.body);\n};\n// Hook for easier usage\nconst useMessageBox = ()=>{\n    const [messageState, setMessageState] = react__WEBPACK_IMPORTED_MODULE_1___default().useState({\n        isOpen: false,\n        title: '',\n        message: ''\n    });\n    const showMessage = (options)=>{\n        setMessageState({\n            isOpen: true,\n            ...options\n        });\n    };\n    const hideMessage = ()=>{\n        setMessageState((prev)=>({\n                ...prev,\n                isOpen: false\n            }));\n    };\n    const MessageBoxComponent = ()=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(MessageBox, {\n            ...messageState,\n            onClose: hideMessage\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\ui\\\\MessageBox.tsx\",\n            lineNumber: 185,\n            columnNumber: 5\n        }, undefined);\n    return {\n        showMessage,\n        hideMessage,\n        MessageBoxComponent\n    };\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/ui/MessageBox.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/ui/Modal.tsx":
/*!*************************************!*\
  !*** ./src/components/ui/Modal.tsx ***!
  \*************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Modal: () => (/* binding */ Modal)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var react_dom__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react-dom */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-dom.js\");\n/* harmony import */ var react_dom__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(react_dom__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _barrel_optimize_names_X_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/x.js\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/utils */ \"(ssr)/./src/lib/utils.ts\");\n/* harmony import */ var _Button__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./Button */ \"(ssr)/./src/components/ui/Button.tsx\");\n/* __next_internal_client_entry_do_not_use__ Modal auto */ \n\n\n\n\n\nconst Modal = ({ isOpen, onClose, title, children, size = 'md', showCloseButton = true, darkMode = false })=>{\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"Modal.useEffect\": ()=>{\n            if (isOpen) {\n                document.body.style.overflow = 'hidden';\n            } else {\n                document.body.style.overflow = 'unset';\n            }\n            return ({\n                \"Modal.useEffect\": ()=>{\n                    document.body.style.overflow = 'unset';\n                }\n            })[\"Modal.useEffect\"];\n        }\n    }[\"Modal.useEffect\"], [\n        isOpen\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"Modal.useEffect\": ()=>{\n            const handleEscape = {\n                \"Modal.useEffect.handleEscape\": (event)=>{\n                    if (event.key === 'Escape') {\n                        onClose();\n                    }\n                }\n            }[\"Modal.useEffect.handleEscape\"];\n            if (isOpen) {\n                document.addEventListener('keydown', handleEscape);\n            }\n            return ({\n                \"Modal.useEffect\": ()=>{\n                    document.removeEventListener('keydown', handleEscape);\n                }\n            })[\"Modal.useEffect\"];\n        }\n    }[\"Modal.useEffect\"], [\n        isOpen,\n        onClose\n    ]);\n    if (!isOpen) return null;\n    const sizeClasses = {\n        sm: 'max-w-md',\n        md: 'max-w-lg',\n        lg: 'max-w-2xl',\n        xl: 'max-w-4xl'\n    };\n    const modalContent = /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"fixed inset-0 z-50 flex items-center justify-center p-4\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"fixed inset-0 bg-black bg-opacity-50 transition-opacity\",\n                onClick: onClose\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\ui\\\\Modal.tsx\",\n                lineNumber: 68,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_3__.cn)('relative w-full rounded-xl shadow-xl transform transition-all', darkMode ? 'bg-slate-800' : 'bg-white', sizeClasses[size]),\n                onClick: (e)=>e.stopPropagation(),\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_3__.cn)('flex items-center justify-between p-6 border-b', darkMode ? 'border-slate-700' : 'border-gray-200'),\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_3__.cn)('text-xl font-semibold', darkMode ? 'text-white' : 'text-dark-900'),\n                                children: title\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\ui\\\\Modal.tsx\",\n                                lineNumber: 87,\n                                columnNumber: 11\n                            }, undefined),\n                            showCloseButton && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_Button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                variant: \"ghost\",\n                                size: \"icon\",\n                                onClick: onClose,\n                                className: \"h-8 w-8 rounded-full\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_X_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                    className: \"h-4 w-4\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\ui\\\\Modal.tsx\",\n                                    lineNumber: 98,\n                                    columnNumber: 15\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\ui\\\\Modal.tsx\",\n                                lineNumber: 92,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\ui\\\\Modal.tsx\",\n                        lineNumber: 83,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"p-6\",\n                        children: children\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\ui\\\\Modal.tsx\",\n                        lineNumber: 104,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\ui\\\\Modal.tsx\",\n                lineNumber: 74,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\ui\\\\Modal.tsx\",\n        lineNumber: 66,\n        columnNumber: 5\n    }, undefined);\n    return /*#__PURE__*/ (0,react_dom__WEBPACK_IMPORTED_MODULE_2__.createPortal)(modalContent, document.body);\n};\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9zcmMvY29tcG9uZW50cy91aS9Nb2RhbC50c3giLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7Ozs7QUFFeUM7QUFDQTtBQUNSO0FBQ0E7QUFDQztBQVlsQyxNQUFNTSxRQUE4QixDQUFDLEVBQ25DQyxNQUFNLEVBQ05DLE9BQU8sRUFDUEMsS0FBSyxFQUNMQyxRQUFRLEVBQ1JDLE9BQU8sSUFBSSxFQUNYQyxrQkFBa0IsSUFBSSxFQUN0QkMsV0FBVyxLQUFLLEVBQ2pCO0lBQ0NaLGdEQUFTQTsyQkFBQztZQUNSLElBQUlNLFFBQVE7Z0JBQ1ZPLFNBQVNDLElBQUksQ0FBQ0MsS0FBSyxDQUFDQyxRQUFRLEdBQUc7WUFDakMsT0FBTztnQkFDTEgsU0FBU0MsSUFBSSxDQUFDQyxLQUFLLENBQUNDLFFBQVEsR0FBRztZQUNqQztZQUVBO21DQUFPO29CQUNMSCxTQUFTQyxJQUFJLENBQUNDLEtBQUssQ0FBQ0MsUUFBUSxHQUFHO2dCQUNqQzs7UUFDRjswQkFBRztRQUFDVjtLQUFPO0lBRVhOLGdEQUFTQTsyQkFBQztZQUNSLE1BQU1pQjtnREFBZSxDQUFDQztvQkFDcEIsSUFBSUEsTUFBTUMsR0FBRyxLQUFLLFVBQVU7d0JBQzFCWjtvQkFDRjtnQkFDRjs7WUFFQSxJQUFJRCxRQUFRO2dCQUNWTyxTQUFTTyxnQkFBZ0IsQ0FBQyxXQUFXSDtZQUN2QztZQUVBO21DQUFPO29CQUNMSixTQUFTUSxtQkFBbUIsQ0FBQyxXQUFXSjtnQkFDMUM7O1FBQ0Y7MEJBQUc7UUFBQ1g7UUFBUUM7S0FBUTtJQUVwQixJQUFJLENBQUNELFFBQVEsT0FBTztJQUVwQixNQUFNZ0IsY0FBYztRQUNsQkMsSUFBSTtRQUNKQyxJQUFJO1FBQ0pDLElBQUk7UUFDSkMsSUFBSTtJQUNOO0lBRUEsTUFBTUMsNkJBQ0osOERBQUNDO1FBQUlDLFdBQVU7OzBCQUViLDhEQUFDRDtnQkFDQ0MsV0FBVTtnQkFDVkMsU0FBU3ZCOzs7Ozs7MEJBSVgsOERBQUNxQjtnQkFDQ0MsV0FBVzFCLDhDQUFFQSxDQUNYLGlFQUNBUyxXQUFXLGlCQUFpQixZQUM1QlUsV0FBVyxDQUFDWixLQUFLO2dCQUVuQm9CLFNBQVMsQ0FBQ0MsSUFBTUEsRUFBRUMsZUFBZTs7a0NBR2pDLDhEQUFDSjt3QkFBSUMsV0FBVzFCLDhDQUFFQSxDQUNoQixrREFDQVMsV0FBVyxxQkFBcUI7OzBDQUVoQyw4REFBQ3FCO2dDQUFHSixXQUFXMUIsOENBQUVBLENBQ2YseUJBQ0FTLFdBQVcsZUFBZTswQ0FDeEJKOzs7Ozs7NEJBQ0hHLGlDQUNDLDhEQUFDUCwyQ0FBTUE7Z0NBQ0w4QixTQUFRO2dDQUNSeEIsTUFBSztnQ0FDTG9CLFNBQVN2QjtnQ0FDVHNCLFdBQVU7MENBRVYsNEVBQUMzQiw2RUFBQ0E7b0NBQUMyQixXQUFVOzs7Ozs7Ozs7Ozs7Ozs7OztrQ0FNbkIsOERBQUNEO3dCQUFJQyxXQUFVO2tDQUNacEI7Ozs7Ozs7Ozs7Ozs7Ozs7OztJQU1ULHFCQUFPUix1REFBWUEsQ0FBQzBCLGNBQWNkLFNBQVNDLElBQUk7QUFDakQ7QUFFaUIiLCJzb3VyY2VzIjpbIkM6XFxVc2Vyc1xcZHJlYW1cXERlc2t0b3BcXEhhc2hfTWluaW5nc1xcaGFzaGNvcmV4XFxzcmNcXGNvbXBvbmVudHNcXHVpXFxNb2RhbC50c3giXSwic291cmNlc0NvbnRlbnQiOlsiJ3VzZSBjbGllbnQnO1xuXG5pbXBvcnQgUmVhY3QsIHsgdXNlRWZmZWN0IH0gZnJvbSAncmVhY3QnO1xuaW1wb3J0IHsgY3JlYXRlUG9ydGFsIH0gZnJvbSAncmVhY3QtZG9tJztcbmltcG9ydCB7IFggfSBmcm9tICdsdWNpZGUtcmVhY3QnO1xuaW1wb3J0IHsgY24gfSBmcm9tICdAL2xpYi91dGlscyc7XG5pbXBvcnQgeyBCdXR0b24gfSBmcm9tICcuL0J1dHRvbic7XG5cbmV4cG9ydCBpbnRlcmZhY2UgTW9kYWxQcm9wcyB7XG4gIGlzT3BlbjogYm9vbGVhbjtcbiAgb25DbG9zZTogKCkgPT4gdm9pZDtcbiAgdGl0bGU6IHN0cmluZztcbiAgY2hpbGRyZW46IFJlYWN0LlJlYWN0Tm9kZTtcbiAgc2l6ZT86ICdzbScgfCAnbWQnIHwgJ2xnJyB8ICd4bCc7XG4gIHNob3dDbG9zZUJ1dHRvbj86IGJvb2xlYW47XG4gIGRhcmtNb2RlPzogYm9vbGVhbjtcbn1cblxuY29uc3QgTW9kYWw6IFJlYWN0LkZDPE1vZGFsUHJvcHM+ID0gKHtcbiAgaXNPcGVuLFxuICBvbkNsb3NlLFxuICB0aXRsZSxcbiAgY2hpbGRyZW4sXG4gIHNpemUgPSAnbWQnLFxuICBzaG93Q2xvc2VCdXR0b24gPSB0cnVlLFxuICBkYXJrTW9kZSA9IGZhbHNlLFxufSkgPT4ge1xuICB1c2VFZmZlY3QoKCkgPT4ge1xuICAgIGlmIChpc09wZW4pIHtcbiAgICAgIGRvY3VtZW50LmJvZHkuc3R5bGUub3ZlcmZsb3cgPSAnaGlkZGVuJztcbiAgICB9IGVsc2Uge1xuICAgICAgZG9jdW1lbnQuYm9keS5zdHlsZS5vdmVyZmxvdyA9ICd1bnNldCc7XG4gICAgfVxuXG4gICAgcmV0dXJuICgpID0+IHtcbiAgICAgIGRvY3VtZW50LmJvZHkuc3R5bGUub3ZlcmZsb3cgPSAndW5zZXQnO1xuICAgIH07XG4gIH0sIFtpc09wZW5dKTtcblxuICB1c2VFZmZlY3QoKCkgPT4ge1xuICAgIGNvbnN0IGhhbmRsZUVzY2FwZSA9IChldmVudDogS2V5Ym9hcmRFdmVudCkgPT4ge1xuICAgICAgaWYgKGV2ZW50LmtleSA9PT0gJ0VzY2FwZScpIHtcbiAgICAgICAgb25DbG9zZSgpO1xuICAgICAgfVxuICAgIH07XG5cbiAgICBpZiAoaXNPcGVuKSB7XG4gICAgICBkb2N1bWVudC5hZGRFdmVudExpc3RlbmVyKCdrZXlkb3duJywgaGFuZGxlRXNjYXBlKTtcbiAgICB9XG5cbiAgICByZXR1cm4gKCkgPT4ge1xuICAgICAgZG9jdW1lbnQucmVtb3ZlRXZlbnRMaXN0ZW5lcigna2V5ZG93bicsIGhhbmRsZUVzY2FwZSk7XG4gICAgfTtcbiAgfSwgW2lzT3Blbiwgb25DbG9zZV0pO1xuXG4gIGlmICghaXNPcGVuKSByZXR1cm4gbnVsbDtcblxuICBjb25zdCBzaXplQ2xhc3NlcyA9IHtcbiAgICBzbTogJ21heC13LW1kJyxcbiAgICBtZDogJ21heC13LWxnJyxcbiAgICBsZzogJ21heC13LTJ4bCcsXG4gICAgeGw6ICdtYXgtdy00eGwnLFxuICB9O1xuXG4gIGNvbnN0IG1vZGFsQ29udGVudCA9IChcbiAgICA8ZGl2IGNsYXNzTmFtZT1cImZpeGVkIGluc2V0LTAgei01MCBmbGV4IGl0ZW1zLWNlbnRlciBqdXN0aWZ5LWNlbnRlciBwLTRcIj5cbiAgICAgIHsvKiBCYWNrZHJvcCAqL31cbiAgICAgIDxkaXZcbiAgICAgICAgY2xhc3NOYW1lPVwiZml4ZWQgaW5zZXQtMCBiZy1ibGFjayBiZy1vcGFjaXR5LTUwIHRyYW5zaXRpb24tb3BhY2l0eVwiXG4gICAgICAgIG9uQ2xpY2s9e29uQ2xvc2V9XG4gICAgICAvPlxuICAgICAgXG4gICAgICB7LyogTW9kYWwgKi99XG4gICAgICA8ZGl2XG4gICAgICAgIGNsYXNzTmFtZT17Y24oXG4gICAgICAgICAgJ3JlbGF0aXZlIHctZnVsbCByb3VuZGVkLXhsIHNoYWRvdy14bCB0cmFuc2Zvcm0gdHJhbnNpdGlvbi1hbGwnLFxuICAgICAgICAgIGRhcmtNb2RlID8gJ2JnLXNsYXRlLTgwMCcgOiAnYmctd2hpdGUnLFxuICAgICAgICAgIHNpemVDbGFzc2VzW3NpemVdXG4gICAgICAgICl9XG4gICAgICAgIG9uQ2xpY2s9eyhlKSA9PiBlLnN0b3BQcm9wYWdhdGlvbigpfVxuICAgICAgPlxuICAgICAgICB7LyogSGVhZGVyICovfVxuICAgICAgICA8ZGl2IGNsYXNzTmFtZT17Y24oXG4gICAgICAgICAgJ2ZsZXggaXRlbXMtY2VudGVyIGp1c3RpZnktYmV0d2VlbiBwLTYgYm9yZGVyLWInLFxuICAgICAgICAgIGRhcmtNb2RlID8gJ2JvcmRlci1zbGF0ZS03MDAnIDogJ2JvcmRlci1ncmF5LTIwMCdcbiAgICAgICAgKX0+XG4gICAgICAgICAgPGgyIGNsYXNzTmFtZT17Y24oXG4gICAgICAgICAgICAndGV4dC14bCBmb250LXNlbWlib2xkJyxcbiAgICAgICAgICAgIGRhcmtNb2RlID8gJ3RleHQtd2hpdGUnIDogJ3RleHQtZGFyay05MDAnXG4gICAgICAgICAgKX0+e3RpdGxlfTwvaDI+XG4gICAgICAgICAge3Nob3dDbG9zZUJ1dHRvbiAmJiAoXG4gICAgICAgICAgICA8QnV0dG9uXG4gICAgICAgICAgICAgIHZhcmlhbnQ9XCJnaG9zdFwiXG4gICAgICAgICAgICAgIHNpemU9XCJpY29uXCJcbiAgICAgICAgICAgICAgb25DbGljaz17b25DbG9zZX1cbiAgICAgICAgICAgICAgY2xhc3NOYW1lPVwiaC04IHctOCByb3VuZGVkLWZ1bGxcIlxuICAgICAgICAgICAgPlxuICAgICAgICAgICAgICA8WCBjbGFzc05hbWU9XCJoLTQgdy00XCIgLz5cbiAgICAgICAgICAgIDwvQnV0dG9uPlxuICAgICAgICAgICl9XG4gICAgICAgIDwvZGl2PlxuICAgICAgICBcbiAgICAgICAgey8qIENvbnRlbnQgKi99XG4gICAgICAgIDxkaXYgY2xhc3NOYW1lPVwicC02XCI+XG4gICAgICAgICAge2NoaWxkcmVufVxuICAgICAgICA8L2Rpdj5cbiAgICAgIDwvZGl2PlxuICAgIDwvZGl2PlxuICApO1xuXG4gIHJldHVybiBjcmVhdGVQb3J0YWwobW9kYWxDb250ZW50LCBkb2N1bWVudC5ib2R5KTtcbn07XG5cbmV4cG9ydCB7IE1vZGFsIH07XG4iXSwibmFtZXMiOlsiUmVhY3QiLCJ1c2VFZmZlY3QiLCJjcmVhdGVQb3J0YWwiLCJYIiwiY24iLCJCdXR0b24iLCJNb2RhbCIsImlzT3BlbiIsIm9uQ2xvc2UiLCJ0aXRsZSIsImNoaWxkcmVuIiwic2l6ZSIsInNob3dDbG9zZUJ1dHRvbiIsImRhcmtNb2RlIiwiZG9jdW1lbnQiLCJib2R5Iiwic3R5bGUiLCJvdmVyZmxvdyIsImhhbmRsZUVzY2FwZSIsImV2ZW50Iiwia2V5IiwiYWRkRXZlbnRMaXN0ZW5lciIsInJlbW92ZUV2ZW50TGlzdGVuZXIiLCJzaXplQ2xhc3NlcyIsInNtIiwibWQiLCJsZyIsInhsIiwibW9kYWxDb250ZW50IiwiZGl2IiwiY2xhc3NOYW1lIiwib25DbGljayIsImUiLCJzdG9wUHJvcGFnYXRpb24iLCJoMiIsInZhcmlhbnQiXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./src/components/ui/Modal.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/ui/ProfileImage.tsx":
/*!********************************************!*\
  !*** ./src/components/ui/ProfileImage.tsx ***!
  \********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ProfileImage: () => (/* binding */ ProfileImage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_image__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/image */ \"(ssr)/./node_modules/next/dist/api/image.js\");\n/* harmony import */ var _barrel_optimize_names_User_lucide_react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=User!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/user.js\");\n/* __next_internal_client_entry_do_not_use__ ProfileImage auto */ \n\n\n\nconst ProfileImage = ({ src, alt = 'Profile', size = 40, className = '', fallbackText, fallbackBgColor = 'bg-gray-500', priority = false, loading = 'lazy' })=>{\n    const [imageError, setImageError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [imageLoading, setImageLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const handleImageError = ()=>{\n        setImageError(true);\n        setImageLoading(false);\n    };\n    const handleImageLoad = ()=>{\n        setImageLoading(false);\n    };\n    const showImage = src && !imageError;\n    const showFallback = !src || imageError;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: `relative flex items-center justify-center overflow-hidden ${fallbackBgColor} ${className}`,\n        style: {\n            width: size,\n            height: size\n        },\n        children: [\n            imageLoading && src && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"absolute inset-0 bg-gray-200 animate-pulse flex items-center justify-center\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"w-4 h-4 bg-gray-300 rounded-full\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\ui\\\\ProfileImage.tsx\",\n                    lineNumber: 51,\n                    columnNumber: 11\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\ui\\\\ProfileImage.tsx\",\n                lineNumber: 50,\n                columnNumber: 9\n            }, undefined),\n            showImage && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_image__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                src: src,\n                alt: alt,\n                width: size,\n                height: size,\n                className: \"w-full h-full object-cover\",\n                priority: priority,\n                loading: loading,\n                onError: handleImageError,\n                onLoad: handleImageLoad,\n                style: {\n                    opacity: imageLoading ? 0 : 1,\n                    transition: 'opacity 0.2s ease-in-out'\n                }\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\ui\\\\ProfileImage.tsx\",\n                lineNumber: 57,\n                columnNumber: 9\n            }, undefined),\n            showFallback && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center justify-center w-full h-full\",\n                children: fallbackText ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                    className: \"text-white font-semibold\",\n                    style: {\n                        fontSize: size * 0.4\n                    },\n                    children: fallbackText\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\ui\\\\ProfileImage.tsx\",\n                    lineNumber: 78,\n                    columnNumber: 13\n                }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_User_lucide_react__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                    className: \"text-white\",\n                    style: {\n                        width: size * 0.5,\n                        height: size * 0.5\n                    }\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\ui\\\\ProfileImage.tsx\",\n                    lineNumber: 82,\n                    columnNumber: 13\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\ui\\\\ProfileImage.tsx\",\n                lineNumber: 76,\n                columnNumber: 9\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\ui\\\\ProfileImage.tsx\",\n        lineNumber: 44,\n        columnNumber: 5\n    }, undefined);\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9zcmMvY29tcG9uZW50cy91aS9Qcm9maWxlSW1hZ2UudHN4IiwibWFwcGluZ3MiOiI7Ozs7Ozs7Ozs7O0FBRXdDO0FBQ1Q7QUFDSztBQWE3QixNQUFNSSxlQUE0QyxDQUFDLEVBQ3hEQyxHQUFHLEVBQ0hDLE1BQU0sU0FBUyxFQUNmQyxPQUFPLEVBQUUsRUFDVEMsWUFBWSxFQUFFLEVBQ2RDLFlBQVksRUFDWkMsa0JBQWtCLGFBQWEsRUFDL0JDLFdBQVcsS0FBSyxFQUNoQkMsVUFBVSxNQUFNLEVBQ2pCO0lBQ0MsTUFBTSxDQUFDQyxZQUFZQyxjQUFjLEdBQUdiLCtDQUFRQSxDQUFDO0lBQzdDLE1BQU0sQ0FBQ2MsY0FBY0MsZ0JBQWdCLEdBQUdmLCtDQUFRQSxDQUFDO0lBRWpELE1BQU1nQixtQkFBbUI7UUFDdkJILGNBQWM7UUFDZEUsZ0JBQWdCO0lBQ2xCO0lBRUEsTUFBTUUsa0JBQWtCO1FBQ3RCRixnQkFBZ0I7SUFDbEI7SUFFQSxNQUFNRyxZQUFZZCxPQUFPLENBQUNRO0lBQzFCLE1BQU1PLGVBQWUsQ0FBQ2YsT0FBT1E7SUFFN0IscUJBQ0UsOERBQUNRO1FBQ0NiLFdBQVcsQ0FBQywwREFBMEQsRUFBRUUsZ0JBQWdCLENBQUMsRUFBRUYsV0FBVztRQUN0R2MsT0FBTztZQUFFQyxPQUFPaEI7WUFBTWlCLFFBQVFqQjtRQUFLOztZQUdsQ1EsZ0JBQWdCVixxQkFDZiw4REFBQ2dCO2dCQUFJYixXQUFVOzBCQUNiLDRFQUFDYTtvQkFBSWIsV0FBVTs7Ozs7Ozs7Ozs7WUFLbEJXLDJCQUNDLDhEQUFDakIsa0RBQUtBO2dCQUNKRyxLQUFLQTtnQkFDTEMsS0FBS0E7Z0JBQ0xpQixPQUFPaEI7Z0JBQ1BpQixRQUFRakI7Z0JBQ1JDLFdBQVU7Z0JBQ1ZHLFVBQVVBO2dCQUNWQyxTQUFTQTtnQkFDVGEsU0FBU1I7Z0JBQ1RTLFFBQVFSO2dCQUNSSSxPQUFPO29CQUNMSyxTQUFTWixlQUFlLElBQUk7b0JBQzVCYSxZQUFZO2dCQUNkOzs7Ozs7WUFLSFIsOEJBQ0MsOERBQUNDO2dCQUFJYixXQUFVOzBCQUNaQyw2QkFDQyw4REFBQ29CO29CQUFLckIsV0FBVTtvQkFBMkJjLE9BQU87d0JBQUVRLFVBQVV2QixPQUFPO29CQUFJOzhCQUN0RUU7Ozs7OzhDQUdILDhEQUFDTixnRkFBSUE7b0JBQUNLLFdBQVU7b0JBQWFjLE9BQU87d0JBQUVDLE9BQU9oQixPQUFPO3dCQUFLaUIsUUFBUWpCLE9BQU87b0JBQUk7Ozs7Ozs7Ozs7Ozs7Ozs7O0FBTXhGLEVBQUUiLCJzb3VyY2VzIjpbIkM6XFxVc2Vyc1xcZHJlYW1cXERlc2t0b3BcXEhhc2hfTWluaW5nc1xcaGFzaGNvcmV4XFxzcmNcXGNvbXBvbmVudHNcXHVpXFxQcm9maWxlSW1hZ2UudHN4Il0sInNvdXJjZXNDb250ZW50IjpbIid1c2UgY2xpZW50JztcblxuaW1wb3J0IFJlYWN0LCB7IHVzZVN0YXRlIH0gZnJvbSAncmVhY3QnO1xuaW1wb3J0IEltYWdlIGZyb20gJ25leHQvaW1hZ2UnO1xuaW1wb3J0IHsgVXNlciB9IGZyb20gJ2x1Y2lkZS1yZWFjdCc7XG5cbmludGVyZmFjZSBQcm9maWxlSW1hZ2VQcm9wcyB7XG4gIHNyYz86IHN0cmluZyB8IG51bGw7XG4gIGFsdD86IHN0cmluZztcbiAgc2l6ZT86IG51bWJlcjtcbiAgY2xhc3NOYW1lPzogc3RyaW5nO1xuICBmYWxsYmFja1RleHQ/OiBzdHJpbmc7XG4gIGZhbGxiYWNrQmdDb2xvcj86IHN0cmluZztcbiAgcHJpb3JpdHk/OiBib29sZWFuO1xuICBsb2FkaW5nPzogJ2xhenknIHwgJ2VhZ2VyJztcbn1cblxuZXhwb3J0IGNvbnN0IFByb2ZpbGVJbWFnZTogUmVhY3QuRkM8UHJvZmlsZUltYWdlUHJvcHM+ID0gKHtcbiAgc3JjLFxuICBhbHQgPSAnUHJvZmlsZScsXG4gIHNpemUgPSA0MCxcbiAgY2xhc3NOYW1lID0gJycsXG4gIGZhbGxiYWNrVGV4dCxcbiAgZmFsbGJhY2tCZ0NvbG9yID0gJ2JnLWdyYXktNTAwJyxcbiAgcHJpb3JpdHkgPSBmYWxzZSxcbiAgbG9hZGluZyA9ICdsYXp5Jyxcbn0pID0+IHtcbiAgY29uc3QgW2ltYWdlRXJyb3IsIHNldEltYWdlRXJyb3JdID0gdXNlU3RhdGUoZmFsc2UpO1xuICBjb25zdCBbaW1hZ2VMb2FkaW5nLCBzZXRJbWFnZUxvYWRpbmddID0gdXNlU3RhdGUodHJ1ZSk7XG5cbiAgY29uc3QgaGFuZGxlSW1hZ2VFcnJvciA9ICgpID0+IHtcbiAgICBzZXRJbWFnZUVycm9yKHRydWUpO1xuICAgIHNldEltYWdlTG9hZGluZyhmYWxzZSk7XG4gIH07XG5cbiAgY29uc3QgaGFuZGxlSW1hZ2VMb2FkID0gKCkgPT4ge1xuICAgIHNldEltYWdlTG9hZGluZyhmYWxzZSk7XG4gIH07XG5cbiAgY29uc3Qgc2hvd0ltYWdlID0gc3JjICYmICFpbWFnZUVycm9yO1xuICBjb25zdCBzaG93RmFsbGJhY2sgPSAhc3JjIHx8IGltYWdlRXJyb3I7XG5cbiAgcmV0dXJuIChcbiAgICA8ZGl2IFxuICAgICAgY2xhc3NOYW1lPXtgcmVsYXRpdmUgZmxleCBpdGVtcy1jZW50ZXIganVzdGlmeS1jZW50ZXIgb3ZlcmZsb3ctaGlkZGVuICR7ZmFsbGJhY2tCZ0NvbG9yfSAke2NsYXNzTmFtZX1gfVxuICAgICAgc3R5bGU9e3sgd2lkdGg6IHNpemUsIGhlaWdodDogc2l6ZSB9fVxuICAgID5cbiAgICAgIHsvKiBMb2FkaW5nIHN0YXRlICovfVxuICAgICAge2ltYWdlTG9hZGluZyAmJiBzcmMgJiYgKFxuICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImFic29sdXRlIGluc2V0LTAgYmctZ3JheS0yMDAgYW5pbWF0ZS1wdWxzZSBmbGV4IGl0ZW1zLWNlbnRlciBqdXN0aWZ5LWNlbnRlclwiPlxuICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwidy00IGgtNCBiZy1ncmF5LTMwMCByb3VuZGVkLWZ1bGxcIj48L2Rpdj5cbiAgICAgICAgPC9kaXY+XG4gICAgICApfVxuXG4gICAgICB7LyogUHJvZmlsZSBJbWFnZSAqL31cbiAgICAgIHtzaG93SW1hZ2UgJiYgKFxuICAgICAgICA8SW1hZ2VcbiAgICAgICAgICBzcmM9e3NyY31cbiAgICAgICAgICBhbHQ9e2FsdH1cbiAgICAgICAgICB3aWR0aD17c2l6ZX1cbiAgICAgICAgICBoZWlnaHQ9e3NpemV9XG4gICAgICAgICAgY2xhc3NOYW1lPVwidy1mdWxsIGgtZnVsbCBvYmplY3QtY292ZXJcIlxuICAgICAgICAgIHByaW9yaXR5PXtwcmlvcml0eX1cbiAgICAgICAgICBsb2FkaW5nPXtsb2FkaW5nfVxuICAgICAgICAgIG9uRXJyb3I9e2hhbmRsZUltYWdlRXJyb3J9XG4gICAgICAgICAgb25Mb2FkPXtoYW5kbGVJbWFnZUxvYWR9XG4gICAgICAgICAgc3R5bGU9e3sgXG4gICAgICAgICAgICBvcGFjaXR5OiBpbWFnZUxvYWRpbmcgPyAwIDogMSxcbiAgICAgICAgICAgIHRyYW5zaXRpb246ICdvcGFjaXR5IDAuMnMgZWFzZS1pbi1vdXQnXG4gICAgICAgICAgfX1cbiAgICAgICAgLz5cbiAgICAgICl9XG5cbiAgICAgIHsvKiBGYWxsYmFjayAqL31cbiAgICAgIHtzaG93RmFsbGJhY2sgJiYgKFxuICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggaXRlbXMtY2VudGVyIGp1c3RpZnktY2VudGVyIHctZnVsbCBoLWZ1bGxcIj5cbiAgICAgICAgICB7ZmFsbGJhY2tUZXh0ID8gKFxuICAgICAgICAgICAgPHNwYW4gY2xhc3NOYW1lPVwidGV4dC13aGl0ZSBmb250LXNlbWlib2xkXCIgc3R5bGU9e3sgZm9udFNpemU6IHNpemUgKiAwLjQgfX0+XG4gICAgICAgICAgICAgIHtmYWxsYmFja1RleHR9XG4gICAgICAgICAgICA8L3NwYW4+XG4gICAgICAgICAgKSA6IChcbiAgICAgICAgICAgIDxVc2VyIGNsYXNzTmFtZT1cInRleHQtd2hpdGVcIiBzdHlsZT17eyB3aWR0aDogc2l6ZSAqIDAuNSwgaGVpZ2h0OiBzaXplICogMC41IH19IC8+XG4gICAgICAgICAgKX1cbiAgICAgICAgPC9kaXY+XG4gICAgICApfVxuICAgIDwvZGl2PlxuICApO1xufTtcbiJdLCJuYW1lcyI6WyJSZWFjdCIsInVzZVN0YXRlIiwiSW1hZ2UiLCJVc2VyIiwiUHJvZmlsZUltYWdlIiwic3JjIiwiYWx0Iiwic2l6ZSIsImNsYXNzTmFtZSIsImZhbGxiYWNrVGV4dCIsImZhbGxiYWNrQmdDb2xvciIsInByaW9yaXR5IiwibG9hZGluZyIsImltYWdlRXJyb3IiLCJzZXRJbWFnZUVycm9yIiwiaW1hZ2VMb2FkaW5nIiwic2V0SW1hZ2VMb2FkaW5nIiwiaGFuZGxlSW1hZ2VFcnJvciIsImhhbmRsZUltYWdlTG9hZCIsInNob3dJbWFnZSIsInNob3dGYWxsYmFjayIsImRpdiIsInN0eWxlIiwid2lkdGgiLCJoZWlnaHQiLCJvbkVycm9yIiwib25Mb2FkIiwib3BhY2l0eSIsInRyYW5zaXRpb24iLCJzcGFuIiwiZm9udFNpemUiXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./src/components/ui/ProfileImage.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/ui/ProfilePictureUpload.tsx":
/*!****************************************************!*\
  !*** ./src/components/ui/ProfilePictureUpload.tsx ***!
  \****************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ProfilePictureUpload: () => (/* binding */ ProfilePictureUpload)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_ui__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui */ \"(ssr)/./src/components/ui/index.ts\");\n/* harmony import */ var _barrel_optimize_names_Camera_Upload_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=Camera,Upload,User,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/user.js\");\n/* harmony import */ var _barrel_optimize_names_Camera_Upload_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=Camera,Upload,User,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/x.js\");\n/* harmony import */ var _barrel_optimize_names_Camera_Upload_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=Camera,Upload,User,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/camera.js\");\n/* harmony import */ var _barrel_optimize_names_Camera_Upload_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=Camera,Upload,User,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/upload.js\");\n/* harmony import */ var next_image__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/image */ \"(ssr)/./node_modules/next/dist/api/image.js\");\n/* __next_internal_client_entry_do_not_use__ ProfilePictureUpload auto */ \n\n\n\n\nconst ProfilePictureUpload = ({ currentPicture, onUpload, onRemove, loading = false, disabled = false })=>{\n    const [preview, setPreview] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [dragOver, setDragOver] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const fileInputRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const handleFileSelect = (file)=>{\n        if (!file) return;\n        // Validate file type\n        if (!file.type.startsWith('image/')) {\n            alert('Please select an image file');\n            return;\n        }\n        // Validate file size (max 5MB)\n        if (file.size > 5 * 1024 * 1024) {\n            alert('File size must be less than 5MB');\n            return;\n        }\n        // Create preview\n        const reader = new FileReader();\n        reader.onload = (e)=>{\n            setPreview(e.target?.result);\n        };\n        reader.readAsDataURL(file);\n        // Upload file\n        onUpload(file);\n    };\n    const handleFileInputChange = (e)=>{\n        const file = e.target.files?.[0];\n        if (file) {\n            handleFileSelect(file);\n        }\n    };\n    const handleDrop = (e)=>{\n        e.preventDefault();\n        setDragOver(false);\n        const file = e.dataTransfer.files[0];\n        if (file) {\n            handleFileSelect(file);\n        }\n    };\n    const handleDragOver = (e)=>{\n        e.preventDefault();\n        setDragOver(true);\n    };\n    const handleDragLeave = (e)=>{\n        e.preventDefault();\n        setDragOver(false);\n    };\n    const handleRemove = ()=>{\n        setPreview(null);\n        if (onRemove) {\n            onRemove();\n        }\n    };\n    const displayImage = preview || currentPicture;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"flex flex-col items-center space-y-4\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"relative\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"w-32 h-32 rounded-full overflow-hidden border-4 border-gray-200 bg-gray-100 flex items-center justify-center\",\n                        children: displayImage ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_image__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                            src: displayImage,\n                            alt: \"Profile Picture\",\n                            width: 128,\n                            height: 128,\n                            className: \"w-full h-full object-cover\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\ui\\\\ProfilePictureUpload.tsx\",\n                            lineNumber: 95,\n                            columnNumber: 13\n                        }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Camera_Upload_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                            className: \"w-16 h-16 text-gray-400\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\ui\\\\ProfilePictureUpload.tsx\",\n                            lineNumber: 103,\n                            columnNumber: 13\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\ui\\\\ProfilePictureUpload.tsx\",\n                        lineNumber: 93,\n                        columnNumber: 9\n                    }, undefined),\n                    loading && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute inset-0 bg-black bg-opacity-50 rounded-full flex items-center justify-center\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"animate-spin rounded-full h-8 w-8 border-b-2 border-white\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\ui\\\\ProfilePictureUpload.tsx\",\n                            lineNumber: 110,\n                            columnNumber: 13\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\ui\\\\ProfilePictureUpload.tsx\",\n                        lineNumber: 109,\n                        columnNumber: 11\n                    }, undefined),\n                    displayImage && !loading && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        onClick: handleRemove,\n                        disabled: disabled,\n                        className: \"absolute -top-2 -right-2 bg-red-500 text-white rounded-full p-1 hover:bg-red-600 transition-colors\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Camera_Upload_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                            className: \"w-4 h-4\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\ui\\\\ProfilePictureUpload.tsx\",\n                            lineNumber: 121,\n                            columnNumber: 13\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\ui\\\\ProfilePictureUpload.tsx\",\n                        lineNumber: 116,\n                        columnNumber: 11\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\ui\\\\ProfilePictureUpload.tsx\",\n                lineNumber: 92,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: `border-2 border-dashed rounded-lg p-6 text-center transition-colors ${dragOver ? 'border-blue-400 bg-blue-50' : 'border-gray-300 hover:border-gray-400'} ${disabled ? 'opacity-50 cursor-not-allowed' : 'cursor-pointer'}`,\n                onDrop: handleDrop,\n                onDragOver: handleDragOver,\n                onDragLeave: handleDragLeave,\n                onClick: ()=>!disabled && fileInputRef.current?.click(),\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex flex-col items-center space-y-2\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Camera_Upload_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                            className: \"w-8 h-8 text-gray-400\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\ui\\\\ProfilePictureUpload.tsx\",\n                            lineNumber: 139,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-sm font-medium text-gray-700\",\n                                    children: \"Click to upload or drag and drop\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\ui\\\\ProfilePictureUpload.tsx\",\n                                    lineNumber: 141,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-xs text-gray-500\",\n                                    children: \"PNG, JPG, GIF up to 5MB\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\ui\\\\ProfilePictureUpload.tsx\",\n                                    lineNumber: 144,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\ui\\\\ProfilePictureUpload.tsx\",\n                            lineNumber: 140,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\ui\\\\ProfilePictureUpload.tsx\",\n                    lineNumber: 138,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\ui\\\\ProfilePictureUpload.tsx\",\n                lineNumber: 127,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                ref: fileInputRef,\n                type: \"file\",\n                accept: \"image/*\",\n                onChange: handleFileInputChange,\n                className: \"hidden\",\n                disabled: disabled\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\ui\\\\ProfilePictureUpload.tsx\",\n                lineNumber: 152,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                onClick: ()=>fileInputRef.current?.click(),\n                disabled: disabled || loading,\n                variant: \"outline\",\n                size: \"sm\",\n                className: \"flex items-center space-x-2\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Camera_Upload_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                        className: \"w-4 h-4\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\ui\\\\ProfilePictureUpload.tsx\",\n                        lineNumber: 169,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                        children: \"Choose File\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\ui\\\\ProfilePictureUpload.tsx\",\n                        lineNumber: 170,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\ui\\\\ProfilePictureUpload.tsx\",\n                lineNumber: 162,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\ui\\\\ProfilePictureUpload.tsx\",\n        lineNumber: 90,\n        columnNumber: 5\n    }, undefined);\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/ui/ProfilePictureUpload.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/ui/index.ts":
/*!************************************!*\
  !*** ./src/components/ui/index.ts ***!
  \************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Button: () => (/* reexport safe */ _Button__WEBPACK_IMPORTED_MODULE_0__.Button),\n/* harmony export */   Card: () => (/* reexport safe */ _Card__WEBPACK_IMPORTED_MODULE_1__.Card),\n/* harmony export */   CardContent: () => (/* reexport safe */ _Card__WEBPACK_IMPORTED_MODULE_1__.CardContent),\n/* harmony export */   CardDescription: () => (/* reexport safe */ _Card__WEBPACK_IMPORTED_MODULE_1__.CardDescription),\n/* harmony export */   CardFooter: () => (/* reexport safe */ _Card__WEBPACK_IMPORTED_MODULE_1__.CardFooter),\n/* harmony export */   CardHeader: () => (/* reexport safe */ _Card__WEBPACK_IMPORTED_MODULE_1__.CardHeader),\n/* harmony export */   CardTitle: () => (/* reexport safe */ _Card__WEBPACK_IMPORTED_MODULE_1__.CardTitle),\n/* harmony export */   ConfirmDialog: () => (/* reexport safe */ _ConfirmDialog__WEBPACK_IMPORTED_MODULE_5__.ConfirmDialog),\n/* harmony export */   Input: () => (/* reexport safe */ _Input__WEBPACK_IMPORTED_MODULE_2__.Input),\n/* harmony export */   Loading: () => (/* reexport safe */ _Loading__WEBPACK_IMPORTED_MODULE_4__.Loading),\n/* harmony export */   LoadingOverlay: () => (/* reexport safe */ _Loading__WEBPACK_IMPORTED_MODULE_4__.LoadingOverlay),\n/* harmony export */   MessageBox: () => (/* reexport safe */ _MessageBox__WEBPACK_IMPORTED_MODULE_6__.MessageBox),\n/* harmony export */   Modal: () => (/* reexport safe */ _Modal__WEBPACK_IMPORTED_MODULE_3__.Modal),\n/* harmony export */   ProfileImage: () => (/* reexport safe */ _ProfileImage__WEBPACK_IMPORTED_MODULE_8__.ProfileImage),\n/* harmony export */   ProfilePictureUpload: () => (/* reexport safe */ _ProfilePictureUpload__WEBPACK_IMPORTED_MODULE_7__.ProfilePictureUpload),\n/* harmony export */   buttonVariants: () => (/* reexport safe */ _Button__WEBPACK_IMPORTED_MODULE_0__.buttonVariants),\n/* harmony export */   useConfirmDialog: () => (/* reexport safe */ _ConfirmDialog__WEBPACK_IMPORTED_MODULE_5__.useConfirmDialog),\n/* harmony export */   useMessageBox: () => (/* reexport safe */ _MessageBox__WEBPACK_IMPORTED_MODULE_6__.useMessageBox)\n/* harmony export */ });\n/* harmony import */ var _Button__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./Button */ \"(ssr)/./src/components/ui/Button.tsx\");\n/* harmony import */ var _Card__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./Card */ \"(ssr)/./src/components/ui/Card.tsx\");\n/* harmony import */ var _Input__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./Input */ \"(ssr)/./src/components/ui/Input.tsx\");\n/* harmony import */ var _Modal__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./Modal */ \"(ssr)/./src/components/ui/Modal.tsx\");\n/* harmony import */ var _Loading__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./Loading */ \"(ssr)/./src/components/ui/Loading.tsx\");\n/* harmony import */ var _ConfirmDialog__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./ConfirmDialog */ \"(ssr)/./src/components/ui/ConfirmDialog.tsx\");\n/* harmony import */ var _MessageBox__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ./MessageBox */ \"(ssr)/./src/components/ui/MessageBox.tsx\");\n/* harmony import */ var _ProfilePictureUpload__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ./ProfilePictureUpload */ \"(ssr)/./src/components/ui/ProfilePictureUpload.tsx\");\n/* harmony import */ var _ProfileImage__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ./ProfileImage */ \"(ssr)/./src/components/ui/ProfileImage.tsx\");\n\n\n\n\n\n\n\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9zcmMvY29tcG9uZW50cy91aS9pbmRleC50cyIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7QUFBb0U7QUFDMkI7QUFDOUM7QUFDQTtBQUNzQjtBQUNvQjtBQUNaO0FBQ2pCO0FBQ2hCIiwic291cmNlcyI6WyJDOlxcVXNlcnNcXGRyZWFtXFxEZXNrdG9wXFxIYXNoX01pbmluZ3NcXGhhc2hjb3JleFxcc3JjXFxjb21wb25lbnRzXFx1aVxcaW5kZXgudHMiXSwic291cmNlc0NvbnRlbnQiOlsiZXhwb3J0IHsgQnV0dG9uLCBidXR0b25WYXJpYW50cywgdHlwZSBCdXR0b25Qcm9wcyB9IGZyb20gJy4vQnV0dG9uJztcbmV4cG9ydCB7IENhcmQsIENhcmRIZWFkZXIsIENhcmRUaXRsZSwgQ2FyZERlc2NyaXB0aW9uLCBDYXJkQ29udGVudCwgQ2FyZEZvb3RlciB9IGZyb20gJy4vQ2FyZCc7XG5leHBvcnQgeyBJbnB1dCwgdHlwZSBJbnB1dFByb3BzIH0gZnJvbSAnLi9JbnB1dCc7XG5leHBvcnQgeyBNb2RhbCwgdHlwZSBNb2RhbFByb3BzIH0gZnJvbSAnLi9Nb2RhbCc7XG5leHBvcnQgeyBMb2FkaW5nLCBMb2FkaW5nT3ZlcmxheSwgdHlwZSBMb2FkaW5nUHJvcHMgfSBmcm9tICcuL0xvYWRpbmcnO1xuZXhwb3J0IHsgQ29uZmlybURpYWxvZywgdXNlQ29uZmlybURpYWxvZywgdHlwZSBDb25maXJtRGlhbG9nUHJvcHMgfSBmcm9tICcuL0NvbmZpcm1EaWFsb2cnO1xuZXhwb3J0IHsgTWVzc2FnZUJveCwgdXNlTWVzc2FnZUJveCwgdHlwZSBNZXNzYWdlQm94UHJvcHMgfSBmcm9tICcuL01lc3NhZ2VCb3gnO1xuZXhwb3J0IHsgUHJvZmlsZVBpY3R1cmVVcGxvYWQgfSBmcm9tICcuL1Byb2ZpbGVQaWN0dXJlVXBsb2FkJztcbmV4cG9ydCB7IFByb2ZpbGVJbWFnZSB9IGZyb20gJy4vUHJvZmlsZUltYWdlJztcbiJdLCJuYW1lcyI6WyJCdXR0b24iLCJidXR0b25WYXJpYW50cyIsIkNhcmQiLCJDYXJkSGVhZGVyIiwiQ2FyZFRpdGxlIiwiQ2FyZERlc2NyaXB0aW9uIiwiQ2FyZENvbnRlbnQiLCJDYXJkRm9vdGVyIiwiSW5wdXQiLCJNb2RhbCIsIkxvYWRpbmciLCJMb2FkaW5nT3ZlcmxheSIsIkNvbmZpcm1EaWFsb2ciLCJ1c2VDb25maXJtRGlhbG9nIiwiTWVzc2FnZUJveCIsInVzZU1lc3NhZ2VCb3giLCJQcm9maWxlUGljdHVyZVVwbG9hZCIsIlByb2ZpbGVJbWFnZSJdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./src/components/ui/index.ts\n");

/***/ }),

/***/ "(ssr)/./src/hooks/useAuth.tsx":
/*!*******************************!*\
  !*** ./src/hooks/useAuth.tsx ***!
  \*******************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   AuthProvider: () => (/* binding */ AuthProvider),\n/* harmony export */   useAuth: () => (/* binding */ useAuth)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* __next_internal_client_entry_do_not_use__ AuthProvider,useAuth auto */ \n\nconst AuthContext = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.createContext)(undefined);\nconst AuthProvider = ({ children })=>{\n    const [user, setUser] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    // Check authentication status on mount\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"AuthProvider.useEffect\": ()=>{\n            checkAuth();\n        }\n    }[\"AuthProvider.useEffect\"], []);\n    const checkAuth = async ()=>{\n        try {\n            const response = await fetch('/api/auth/me', {\n                credentials: 'include'\n            });\n            if (response.ok) {\n                const data = await response.json();\n                if (data.success) {\n                    setUser(data.data);\n                }\n            }\n        } catch (error) {\n            console.error('Auth check failed:', error);\n        } finally{\n            setLoading(false);\n        }\n    };\n    const login = async (email, password)=>{\n        setLoading(true);\n        try {\n            const response = await fetch('/api/auth/login', {\n                method: 'POST',\n                headers: {\n                    'Content-Type': 'application/json'\n                },\n                credentials: 'include',\n                body: JSON.stringify({\n                    email,\n                    password\n                })\n            });\n            const data = await response.json();\n            if (!data.success) {\n                throw new Error(data.error || 'Login failed');\n            }\n            setUser(data.data.user);\n            // Force a re-check of authentication after login\n            setTimeout(()=>{\n                checkAuth();\n            }, 100);\n        } catch (error) {\n            throw error;\n        } finally{\n            setLoading(false);\n        }\n    };\n    const register = async (email, firstName, lastName, password, confirmPassword, referralCode, placementSide, otp)=>{\n        setLoading(true);\n        try {\n            // Build URL with side parameter if provided\n            const url = placementSide ? `/api/auth/register?side=${placementSide}` : '/api/auth/register';\n            const response = await fetch(url, {\n                method: 'POST',\n                headers: {\n                    'Content-Type': 'application/json'\n                },\n                body: JSON.stringify({\n                    email,\n                    firstName,\n                    lastName,\n                    password,\n                    confirmPassword,\n                    referralCode,\n                    otp\n                })\n            });\n            const data = await response.json();\n            if (!data.success) {\n                throw new Error(data.error || 'Registration failed');\n            }\n            // Set user state after successful registration (registration API already sets auth cookie)\n            setUser({\n                id: data.data.user.id,\n                email: data.data.user.email,\n                firstName: data.data.user.firstName || '',\n                lastName: data.data.user.lastName || '',\n                referralId: data.data.user.referralId,\n                role: data.data.user.role || 'USER',\n                kycStatus: data.data.user.kycStatus,\n                isActive: data.data.user.isActive || true,\n                profilePicture: data.data.user.profilePicture || null,\n                createdAt: data.data.user.createdAt,\n                updatedAt: data.data.user.updatedAt\n            });\n        } catch (error) {\n            throw error;\n        } finally{\n            setLoading(false);\n        }\n    };\n    const logout = async ()=>{\n        try {\n            await fetch('/api/auth/logout', {\n                method: 'POST',\n                credentials: 'include'\n            });\n        } catch (error) {\n            console.error('Logout error:', error);\n        } finally{\n            setUser(null);\n        }\n    };\n    const refreshUser = async ()=>{\n        await checkAuth();\n    };\n    const value = {\n        user,\n        loading,\n        login,\n        register,\n        logout,\n        refreshUser\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(AuthContext.Provider, {\n        value: value,\n        children: children\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\hooks\\\\useAuth.tsx\",\n        lineNumber: 162,\n        columnNumber: 10\n    }, undefined);\n};\nconst useAuth = ()=>{\n    const context = (0,react__WEBPACK_IMPORTED_MODULE_1__.useContext)(AuthContext);\n    if (context === undefined) {\n        throw new Error('useAuth must be used within an AuthProvider');\n    }\n    return context;\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/hooks/useAuth.tsx\n");

/***/ }),

/***/ "(ssr)/./src/lib/utils.ts":
/*!**************************!*\
  !*** ./src/lib/utils.ts ***!
  \**************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   calculateROI: () => (/* binding */ calculateROI),\n/* harmony export */   calculateTHSPrice: () => (/* binding */ calculateTHSPrice),\n/* harmony export */   cn: () => (/* binding */ cn),\n/* harmony export */   copyToClipboard: () => (/* binding */ copyToClipboard),\n/* harmony export */   debounce: () => (/* binding */ debounce),\n/* harmony export */   formatCurrency: () => (/* binding */ formatCurrency),\n/* harmony export */   formatDate: () => (/* binding */ formatDate),\n/* harmony export */   formatDateTime: () => (/* binding */ formatDateTime),\n/* harmony export */   formatNumber: () => (/* binding */ formatNumber),\n/* harmony export */   formatTHS: () => (/* binding */ formatTHS),\n/* harmony export */   generateId: () => (/* binding */ generateId),\n/* harmony export */   getTimeUntilBinaryPayout: () => (/* binding */ getTimeUntilBinaryPayout),\n/* harmony export */   getTimeUntilNextPayout: () => (/* binding */ getTimeUntilNextPayout),\n/* harmony export */   sleep: () => (/* binding */ sleep),\n/* harmony export */   throttle: () => (/* binding */ throttle),\n/* harmony export */   truncateText: () => (/* binding */ truncateText),\n/* harmony export */   validateEmail: () => (/* binding */ validateEmail),\n/* harmony export */   validatePassword: () => (/* binding */ validatePassword)\n/* harmony export */ });\n/* harmony import */ var clsx__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! clsx */ \"(ssr)/./node_modules/clsx/dist/clsx.mjs\");\n/* harmony import */ var tailwind_merge__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! tailwind-merge */ \"(ssr)/./node_modules/tailwind-merge/dist/bundle-mjs.mjs\");\n\n\nfunction cn(...inputs) {\n    return (0,tailwind_merge__WEBPACK_IMPORTED_MODULE_1__.twMerge)((0,clsx__WEBPACK_IMPORTED_MODULE_0__.clsx)(inputs));\n}\nfunction formatCurrency(amount, currency = 'USD') {\n    return new Intl.NumberFormat('en-US', {\n        style: 'currency',\n        currency,\n        minimumFractionDigits: 2,\n        maximumFractionDigits: 2\n    }).format(amount);\n}\nfunction formatNumber(num, decimals = 2) {\n    return new Intl.NumberFormat('en-US', {\n        minimumFractionDigits: decimals,\n        maximumFractionDigits: decimals\n    }).format(num);\n}\nfunction formatDate(date) {\n    const d = typeof date === 'string' ? new Date(date) : date;\n    return new Intl.DateTimeFormat('en-US', {\n        year: 'numeric',\n        month: 'short',\n        day: 'numeric'\n    }).format(d);\n}\nfunction formatDateTime(date) {\n    const d = typeof date === 'string' ? new Date(date) : date;\n    return new Intl.DateTimeFormat('en-US', {\n        year: 'numeric',\n        month: 'short',\n        day: 'numeric',\n        hour: '2-digit',\n        minute: '2-digit'\n    }).format(d);\n}\nfunction truncateText(text, maxLength) {\n    if (text.length <= maxLength) return text;\n    return text.slice(0, maxLength) + '...';\n}\nfunction generateId() {\n    // Use crypto.randomUUID if available (modern browsers), fallback to timestamp + random\n    if (typeof crypto !== 'undefined' && crypto.randomUUID) {\n        return crypto.randomUUID().replace(/-/g, '').substring(0, 9);\n    }\n    // Fallback for older browsers or server-side\n    const timestamp = Date.now().toString(36);\n    const randomPart = Math.random().toString(36).substr(2, 5);\n    return (timestamp + randomPart).substr(0, 9);\n}\nfunction sleep(ms) {\n    return new Promise((resolve)=>setTimeout(resolve, ms));\n}\nfunction debounce(func, wait) {\n    let timeout;\n    return (...args)=>{\n        clearTimeout(timeout);\n        timeout = setTimeout(()=>func(...args), wait);\n    };\n}\nfunction throttle(func, limit) {\n    let inThrottle;\n    return (...args)=>{\n        if (!inThrottle) {\n            func(...args);\n            inThrottle = true;\n            setTimeout(()=>inThrottle = false, limit);\n        }\n    };\n}\nfunction copyToClipboard(text) {\n    if (navigator.clipboard) {\n        return navigator.clipboard.writeText(text);\n    }\n    // Fallback for older browsers\n    const textArea = document.createElement('textarea');\n    textArea.value = text;\n    document.body.appendChild(textArea);\n    textArea.focus();\n    textArea.select();\n    try {\n        document.execCommand('copy');\n        return Promise.resolve();\n    } catch (err) {\n        return Promise.reject(err);\n    } finally{\n        document.body.removeChild(textArea);\n    }\n}\nfunction validateEmail(email) {\n    const emailRegex = /^[^\\s@]+@[^\\s@]+\\.[^\\s@]+$/;\n    return emailRegex.test(email);\n}\nfunction validatePassword(password) {\n    const errors = [];\n    const checks = [];\n    // Check length\n    const lengthValid = password.length >= 8;\n    checks.push({\n        valid: lengthValid,\n        message: 'At least 8 characters long'\n    });\n    if (!lengthValid) {\n        errors.push('Password must be at least 8 characters long');\n    }\n    // Check uppercase\n    const uppercaseValid = /[A-Z]/.test(password);\n    checks.push({\n        valid: uppercaseValid,\n        message: 'At least one uppercase letter'\n    });\n    if (!uppercaseValid) {\n        errors.push('Password must contain at least one uppercase letter');\n    }\n    // Check lowercase\n    const lowercaseValid = /[a-z]/.test(password);\n    checks.push({\n        valid: lowercaseValid,\n        message: 'At least one lowercase letter'\n    });\n    if (!lowercaseValid) {\n        errors.push('Password must contain at least one lowercase letter');\n    }\n    // Check number\n    const numberValid = /\\d/.test(password);\n    checks.push({\n        valid: numberValid,\n        message: 'At least one number'\n    });\n    if (!numberValid) {\n        errors.push('Password must contain at least one number');\n    }\n    // Check special character\n    const specialValid = /[!@#$%^&*(),.?\":{}|<>]/.test(password);\n    checks.push({\n        valid: specialValid,\n        message: 'At least one special character'\n    });\n    if (!specialValid) {\n        errors.push('Password must contain at least one special character');\n    }\n    return {\n        isValid: errors.length === 0,\n        errors,\n        checks\n    };\n}\nfunction calculateROI(investment, dailyRate, days) {\n    return investment * (dailyRate / 100) * days;\n}\nfunction calculateTHSPrice(ths, pricePerTHS) {\n    return ths * pricePerTHS;\n}\nfunction formatTHS(ths) {\n    if (ths >= 1000) {\n        return `${(ths / 1000).toFixed(1)}K TH/s`;\n    }\n    return `${ths.toFixed(2)} TH/s`;\n}\nfunction getTimeUntilNextPayout() {\n    const now = new Date();\n    const nextSaturday = new Date();\n    // Set to next Saturday at 15:00 UTC\n    nextSaturday.setUTCDate(now.getUTCDate() + (6 - now.getUTCDay()));\n    nextSaturday.setUTCHours(15, 0, 0, 0);\n    // If it's already past Saturday 15:00, move to next week\n    if (now > nextSaturday) {\n        nextSaturday.setUTCDate(nextSaturday.getUTCDate() + 7);\n    }\n    const diff = nextSaturday.getTime() - now.getTime();\n    const days = Math.floor(diff / (1000 * 60 * 60 * 24));\n    const hours = Math.floor(diff % (1000 * 60 * 60 * 24) / (1000 * 60 * 60));\n    const minutes = Math.floor(diff % (1000 * 60 * 60) / (1000 * 60));\n    const seconds = Math.floor(diff % (1000 * 60) / 1000);\n    return {\n        days,\n        hours,\n        minutes,\n        seconds\n    };\n}\nfunction getTimeUntilBinaryPayout() {\n    const now = new Date();\n    const nextSaturday = new Date();\n    // Set to next Saturday at 15:00 UTC\n    nextSaturday.setUTCDate(now.getUTCDate() + (6 - now.getUTCDay()));\n    nextSaturday.setUTCHours(15, 0, 0, 0);\n    // If it's already past Saturday 15:00, move to next week\n    if (now > nextSaturday) {\n        nextSaturday.setUTCDate(nextSaturday.getUTCDate() + 7);\n    }\n    const diff = nextSaturday.getTime() - now.getTime();\n    const days = Math.floor(diff / (1000 * 60 * 60 * 24));\n    const hours = Math.floor(diff % (1000 * 60 * 60 * 24) / (1000 * 60 * 60));\n    const minutes = Math.floor(diff % (1000 * 60 * 60) / (1000 * 60));\n    const seconds = Math.floor(diff % (1000 * 60) / 1000);\n    return {\n        days,\n        hours,\n        minutes,\n        seconds\n    };\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/lib/utils.ts\n");

/***/ }),

/***/ "../app-render/action-async-storage.external":
/*!*******************************************************************************!*\
  !*** external "next/dist/server/app-render/action-async-storage.external.js" ***!
  \*******************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/action-async-storage.external.js");

/***/ }),

/***/ "../app-render/after-task-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist/server/app-render/after-task-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/after-task-async-storage.external.js");

/***/ }),

/***/ "../app-render/work-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/server/app-render/work-async-storage.external.js" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-async-storage.external.js");

/***/ }),

/***/ "./work-unit-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist/server/app-render/work-unit-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-unit-async-storage.external.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "path":
/*!***********************!*\
  !*** external "path" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("path");

/***/ }),

/***/ "url":
/*!**********************!*\
  !*** external "url" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("url");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/@opentelemetry","vendor-chunks/@swc","vendor-chunks/tailwind-merge","vendor-chunks/lucide-react","vendor-chunks/class-variance-authority","vendor-chunks/clsx"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2F(auth)%2Fforgot-password%2Fpage&page=%2F(auth)%2Fforgot-password%2Fpage&appPaths=%2F(auth)%2Fforgot-password%2Fpage&pagePath=private-next-app-dir%2F(auth)%2Fforgot-password%2Fpage.tsx&appDir=C%3A%5CUsers%5Cdream%5CDesktop%5CHash_Minings%5Chashcorex%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cdream%5CDesktop%5CHash_Minings%5Chashcorex&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();