"use strict";(()=>{var e={};e.id=6409,e.ids=[6409],e.modules={3295:e=>{e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},12909:(e,t,i)=>{i.d(t,{DT:()=>w,DY:()=>f,HU:()=>c,Lx:()=>y,b9:()=>g,qc:()=>E});var a=i(85663),n=i(43205),r=i.n(n),s=i(6710);let o=process.env.JWT_SECRET||"fallback-secret-key",d=process.env.JWT_EXPIRES_IN||"30d",u=async e=>await a.Ay.hash(e,12),l=async(e,t)=>await a.Ay.compare(e,t),c=e=>r().sign(e,o,{expiresIn:d}),m=e=>{try{return r().verify(e,o)}catch(e){return null}},p=()=>{let e="ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789",t="HC";for(let i=0;i<8;i++)t+=e.charAt(Math.floor(Math.random()*e.length));return t},g=async e=>{let t=e.headers.get("authorization")?.replace("Bearer ","")||e.cookies.get("auth-token")?.value;if(!t)return{authenticated:!1,user:null};let i=m(t);if(!i)return{authenticated:!1,user:null};let a=await s.Gy.findByEmail(i.email);return a?{authenticated:!0,user:a}:{authenticated:!1,user:null}},f=async e=>{let t,a;if(await s.Gy.findByEmail(e.email))throw Error("User already exists with this email");if(e.referralCode){let i=await s.Gy.findByReferralId(e.referralCode);if(!i)throw Error("Invalid referral code");t=i.id}let n=await u(e.password),r=!1;do a=p(),r=!await s.Gy.findByReferralId(a);while(!r);let o=await s.Gy.create({email:e.email,firstName:e.firstName,lastName:e.lastName,password:n,referralId:a});if(t){let{placeUserByReferralType:a}=await i.e(2746).then(i.bind(i,2746)),n="general";"left"===e.placementSide?n="left":"right"===e.placementSide&&(n="right"),await a(t,o.id,n)}return{id:o.id,email:o.email,referralId:o.referralId,kycStatus:o.kycStatus}},y=async e=>{let t=await s.Gy.findByEmail(e.email);if(!t||!await l(e.password,t.password))throw Error("Invalid email or password");return{token:c({userId:t.id,email:t.email}),user:{id:t.id,email:t.email,referralId:t.referralId,kycStatus:t.kycStatus}}},w=e=>/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(e),E=async e=>{let t=await s.Gy.findById(e);return t?.role==="ADMIN"}},27910:e=>{e.exports=require("stream")},28354:e=>{e.exports=require("util")},29294:e=>{e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},39794:(e,t,i)=>{i.d(t,{Py:()=>s,k8:()=>u,kp:()=>d});var a=i(31183),n=i(6710);async function r(e){return await a.prisma.miningUnit.findMany({where:{userId:e,status:"ACTIVE",expiryDate:{gt:new Date}},orderBy:{createdAt:"asc"}})}async function s(e,t,i,s,d){let u=await r(e);if(0===u.length)throw Error("No active mining units found for earnings allocation");let l=[],c=t;for(let e of u){var m;if(c<=0)break;let t=Math.max(0,5*(m=e).investmentAmount-(m.miningEarnings+m.referralEarnings+m.binaryEarnings));if(t<=0)continue;let n=Math.min(c,t);if(n>0){let r={};switch(i){case"MINING_EARNINGS":r.miningEarnings={increment:n};break;case"DIRECT_REFERRAL":r.referralEarnings={increment:n};break;case"BINARY_BONUS":r.binaryEarnings={increment:n}}r.totalEarned={increment:n},await a.prisma.miningUnit.update({where:{id:e.id},data:r}),await a.prisma.miningUnitEarningsAllocation.create({data:{miningUnitId:e.id,transactionId:s,earningType:i,amount:n,description:d}}),l.push({miningUnitId:e.id,amount:n,remainingCapacity:t-n}),c-=n;let u=await a.prisma.miningUnit.findUnique({where:{id:e.id}});u&&function(e){let t=5*e.investmentAmount;return e.miningEarnings+e.referralEarnings+e.binaryEarnings>=t}(u)&&await o(e.id,"5x_investment_reached")}}return c>0&&(console.warn(`Unable to allocate ${c} to mining units - all units at capacity`),await n.AJ.create({action:"EARNINGS_ALLOCATION_OVERFLOW",userId:e,details:{totalAmount:t,allocatedAmount:t-c,overflowAmount:c,earningType:i,reason:"all_units_at_capacity"}})),l}async function o(e,t){let i=await a.prisma.miningUnit.findUnique({where:{id:e}});if(!i)throw Error(`Mining unit ${e} not found`);await a.prisma.miningUnit.update({where:{id:e},data:{status:"EXPIRED"}});let r=i.miningEarnings+i.referralEarnings+i.binaryEarnings;await n.AJ.create({action:"MINING_UNIT_EXPIRED",userId:i.userId,details:{miningUnitId:e,reason:t,totalEarned:r,miningEarnings:i.miningEarnings,referralEarnings:i.referralEarnings,binaryEarnings:i.binaryEarnings,investmentAmount:i.investmentAmount,multiplier:r/i.investmentAmount}}),console.log(`Mining unit ${e} expired due to ${t}. Total earnings: ${r}`)}async function d(e){return await a.prisma.miningUnit.findMany({where:{userId:e},orderBy:{createdAt:"asc"}})}async function u(e){return await a.prisma.miningUnitEarningsAllocation.findMany({where:{miningUnitId:e},include:{transaction:!0},orderBy:{allocatedAt:"desc"}})}},44870:e=>{e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},55511:e=>{e.exports=require("crypto")},63033:e=>{e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},66839:(e,t,i)=>{i.r(t),i.d(t,{patchFetch:()=>y,routeModule:()=>m,serverHooks:()=>f,workAsyncStorage:()=>p,workUnitAsyncStorage:()=>g});var a={};i.r(a),i.d(a,{POST:()=>c});var n=i(96559),r=i(48088),s=i(37719),o=i(32190),d=i(12909),u=i(92731),l=i(6710);async function c(e){try{let{authenticated:t,user:i}=await (0,d.b9)(e);if(!t||!i)return o.NextResponse.json({success:!1,error:"Authentication required"},{status:401});if("ADMIN"!==i.role)return o.NextResponse.json({success:!1,error:"Admin access required"},{status:403});console.log(`Mining units ROI update initiated by admin: ${i.email}`);let a=await (0,u.s2)();return await l.AJ.create({action:"MANUAL_MINING_UNITS_ROI_UPDATE_TRIGGERED",details:{triggeredBy:i.email,adminId:i.id,result:a,timestamp:new Date().toISOString()}}),console.log(`Mining units ROI update completed. Updated ${a.unitsUpdated} out of ${a.totalUnits} units`),o.NextResponse.json({success:!0,message:"Mining units ROI update completed successfully",data:{unitsUpdated:a.unitsUpdated,totalUnits:a.totalUnits,updateResults:a.updateResults}})}catch(t){console.error("Mining units ROI update error:",t);try{let{authenticated:i,user:a}=await (0,d.b9)(e);await l.AJ.create({action:"MANUAL_MINING_UNITS_ROI_UPDATE_ERROR",details:{triggeredBy:a?.email||"unknown",error:t.message,timestamp:new Date().toISOString()}})}catch(e){console.error("Failed to log mining units ROI update error:",e)}return o.NextResponse.json({success:!1,error:"Failed to update mining units ROI"},{status:500})}}let m=new n.AppRouteRouteModule({definition:{kind:r.RouteKind.APP_ROUTE,page:"/api/admin/update-mining-units-roi/route",pathname:"/api/admin/update-mining-units-roi",filename:"route",bundlePath:"app/api/admin/update-mining-units-roi/route"},resolvedPagePath:"C:\\Users\\<USER>\\Desktop\\Hash_Minings\\hashcorex\\src\\app\\api\\admin\\update-mining-units-roi\\route.ts",nextConfigOutput:"",userland:a}),{workAsyncStorage:p,workUnitAsyncStorage:g,serverHooks:f}=m;function y(){return(0,s.patchFetch)({workAsyncStorage:p,workUnitAsyncStorage:g})}},79428:e=>{e.exports=require("buffer")},96330:e=>{e.exports=require("@prisma/client")}};var t=require("../../../../webpack-runtime.js");t.C(e);var i=e=>t(t.s=e),a=t.X(0,[4447,580,5315,3306,5112],()=>i(66839));module.exports=a})();