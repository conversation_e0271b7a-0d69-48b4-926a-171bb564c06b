"use strict";(()=>{var e={};e.id=3355,e.ids=[3355],e.modules={3295:e=>{e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},12909:(e,r,t)=>{t.d(r,{DT:()=>w,DY:()=>m,HU:()=>c,Lx:()=>y,b9:()=>h,qc:()=>g});var a=t(85663),i=t(43205),s=t.n(i),n=t(6710);let l=process.env.JWT_SECRET||"fallback-secret-key",d=process.env.JWT_EXPIRES_IN||"30d",o=async e=>await a.Ay.hash(e,12),u=async(e,r)=>await a.Ay.compare(e,r),c=e=>s().sign(e,l,{expiresIn:d}),f=e=>{try{return s().verify(e,l)}catch(e){return null}},p=()=>{let e="ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789",r="HC";for(let t=0;t<8;t++)r+=e.charAt(Math.floor(Math.random()*e.length));return r},h=async e=>{let r=e.headers.get("authorization")?.replace("Bearer ","")||e.cookies.get("auth-token")?.value;if(!r)return{authenticated:!1,user:null};let t=f(r);if(!t)return{authenticated:!1,user:null};let a=await n.Gy.findByEmail(t.email);return a?{authenticated:!0,user:a}:{authenticated:!1,user:null}},m=async e=>{let r,a;if(await n.Gy.findByEmail(e.email))throw Error("User already exists with this email");if(e.referralCode){let t=await n.Gy.findByReferralId(e.referralCode);if(!t)throw Error("Invalid referral code");r=t.id}let i=await o(e.password),s=!1;do a=p(),s=!await n.Gy.findByReferralId(a);while(!s);let l=await n.Gy.create({email:e.email,firstName:e.firstName,lastName:e.lastName,password:i,referralId:a});if(r){let{placeUserByReferralType:a}=await t.e(2746).then(t.bind(t,2746)),i="general";"left"===e.placementSide?i="left":"right"===e.placementSide&&(i="right"),await a(r,l.id,i)}return{id:l.id,email:l.email,referralId:l.referralId,kycStatus:l.kycStatus}},y=async e=>{let r=await n.Gy.findByEmail(e.email);if(!r||!await u(e.password,r.password))throw Error("Invalid email or password");return{token:c({userId:r.id,email:r.email}),user:{id:r.id,email:r.email,referralId:r.referralId,kycStatus:r.kycStatus}}},w=e=>/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(e),g=async e=>{let r=await n.Gy.findById(e);return r?.role==="ADMIN"}},27910:e=>{e.exports=require("stream")},28354:e=>{e.exports=require("util")},28367:(e,r,t)=>{t.r(r),t.d(r,{patchFetch:()=>w,routeModule:()=>p,serverHooks:()=>y,workAsyncStorage:()=>h,workUnitAsyncStorage:()=>m});var a={};t.r(a),t.d(a,{GET:()=>f});var i=t(96559),s=t(48088),n=t(37719),l=t(32190),d=t(12909),o=t(2746),u=t(6710),c=t(31183);async function f(e){try{let{authenticated:r,user:t}=await (0,d.b9)(e);if(!r||!t)return l.NextResponse.json({success:!1,error:"Not authenticated"},{status:401});let{searchParams:a}=new URL(e.url),i=parseInt(a.get("depth")||"3"),s="true"===a.get("enhanced"),n=a.get("expanded")?new Set(a.get("expanded").split(",").filter(e=>e.length>0)):new Set,f=a.get("nodeId");if(f){let e=await (0,o.OZ)(f);return l.NextResponse.json({success:!0,data:e})}let p=await (0,o.PM)(t.id,Math.min(i,50),n),h=await c.prisma.user.findMany({where:{referrerId:t.id},select:{id:!0,email:!0,firstName:!0,lastName:!0,isActive:!0,createdAt:!0}}),m=await u.cc.findByReferrerId(t.id),y=await u.FW.findByUserId(t.id),w=m.filter(e=>"LEFT"===e.placementSide).length,g=m.filter(e=>"RIGHT"===e.placementSide).length,x=await (0,o.l6)(t.id),I=(await u.DR.findByUserId(t.id,{types:["DIRECT_REFERRAL","BINARY_BONUS"],status:"COMPLETED"})).reduce((e,r)=>e+r.amount,0),R={treeStructure:p,statistics:{totalDirectReferrals:h.length,leftPlacements:w,rightPlacements:g,leftReferrals:x.left,rightReferrals:x.right,leftTeam:x.left,rightTeam:x.right,totalTeam:x.total,totalCommissions:I,binaryPoints:y||{leftPoints:0,rightPoints:0,matchedPoints:0}},referralLinks:{left:`http://localhost:3000/register?ref=${t.referralId}&side=left`,right:`http://localhost:3000/register?ref=${t.referralId}&side=right`,general:`http://localhost:3000/register?ref=${t.referralId}`}};if(s){let e=await (0,o.fB)(t.id),r=await (0,o.g5)(t.id);R.statistics.detailedStats=e,R.statistics.treeHealth=r}return l.NextResponse.json({success:!0,data:R})}catch(e){return console.error("Binary tree fetch error:",e),l.NextResponse.json({success:!1,error:"Failed to fetch binary tree"},{status:500})}}let p=new i.AppRouteRouteModule({definition:{kind:s.RouteKind.APP_ROUTE,page:"/api/referrals/tree/route",pathname:"/api/referrals/tree",filename:"route",bundlePath:"app/api/referrals/tree/route"},resolvedPagePath:"C:\\Users\\<USER>\\Desktop\\Hash_Minings\\hashcorex\\src\\app\\api\\referrals\\tree\\route.ts",nextConfigOutput:"",userland:a}),{workAsyncStorage:h,workUnitAsyncStorage:m,serverHooks:y}=p;function w(){return(0,n.patchFetch)({workAsyncStorage:h,workUnitAsyncStorage:m})}},29294:e=>{e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},44870:e=>{e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},55511:e=>{e.exports=require("crypto")},63033:e=>{e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},79428:e=>{e.exports=require("buffer")},96330:e=>{e.exports=require("@prisma/client")}};var r=require("../../../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),a=r.X(0,[4447,580,5315,3306,2746],()=>t(28367));module.exports=a})();