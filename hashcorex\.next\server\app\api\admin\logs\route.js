"use strict";(()=>{var e={};e.id=3295,e.ids=[3295],e.modules={3295:e=>{e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},12909:(e,t,r)=>{r.d(t,{DT:()=>g,DY:()=>y,HU:()=>u,Lx:()=>h,b9:()=>f,qc:()=>w});var a=r(85663),i=r(43205),s=r.n(i),n=r(6710);let o=process.env.JWT_SECRET||"fallback-secret-key",l=process.env.JWT_EXPIRES_IN||"30d",d=async e=>await a.Ay.hash(e,12),c=async(e,t)=>await a.Ay.compare(e,t),u=e=>s().sign(e,o,{expiresIn:l}),m=e=>{try{return s().verify(e,o)}catch(e){return null}},p=()=>{let e="ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789",t="HC";for(let r=0;r<8;r++)t+=e.charAt(Math.floor(Math.random()*e.length));return t},f=async e=>{let t=e.headers.get("authorization")?.replace("Bearer ","")||e.cookies.get("auth-token")?.value;if(!t)return{authenticated:!1,user:null};let r=m(t);if(!r)return{authenticated:!1,user:null};let a=await n.Gy.findByEmail(r.email);return a?{authenticated:!0,user:a}:{authenticated:!1,user:null}},y=async e=>{let t,a;if(await n.Gy.findByEmail(e.email))throw Error("User already exists with this email");if(e.referralCode){let r=await n.Gy.findByReferralId(e.referralCode);if(!r)throw Error("Invalid referral code");t=r.id}let i=await d(e.password),s=!1;do a=p(),s=!await n.Gy.findByReferralId(a);while(!s);let o=await n.Gy.create({email:e.email,firstName:e.firstName,lastName:e.lastName,password:i,referralId:a});if(t){let{placeUserByReferralType:a}=await r.e(2746).then(r.bind(r,2746)),i="general";"left"===e.placementSide?i="left":"right"===e.placementSide&&(i="right"),await a(t,o.id,i)}return{id:o.id,email:o.email,referralId:o.referralId,kycStatus:o.kycStatus}},h=async e=>{let t=await n.Gy.findByEmail(e.email);if(!t||!await c(e.password,t.password))throw Error("Invalid email or password");return{token:u({userId:t.id,email:t.email}),user:{id:t.id,email:t.email,referralId:t.referralId,kycStatus:t.kycStatus}}},g=e=>/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(e),w=async e=>{let t=await n.Gy.findById(e);return t?.role==="ADMIN"}},27910:e=>{e.exports=require("stream")},28354:e=>{e.exports=require("util")},29294:e=>{e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},44870:e=>{e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},55511:e=>{e.exports=require("crypto")},63033:e=>{e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},79428:e=>{e.exports=require("buffer")},95317:(e,t,r)=>{r.r(t),r.d(t,{patchFetch:()=>y,routeModule:()=>u,serverHooks:()=>f,workAsyncStorage:()=>m,workUnitAsyncStorage:()=>p});var a={};r.r(a),r.d(a,{GET:()=>c});var i=r(96559),s=r(48088),n=r(37719),o=r(32190),l=r(12909),d=r(31183);async function c(e){try{let{authenticated:t,user:r}=await (0,l.b9)(e);if(!t||!r)return o.NextResponse.json({error:"Not authenticated"},{status:401});if(!await (0,l.qc)(r.id))return o.NextResponse.json({error:"Admin access required"},{status:403});let{searchParams:a}=new URL(e.url),i=parseInt(a.get("page")||"1"),s=parseInt(a.get("limit")||"20"),n=a.get("search")||"",c=a.get("action")||"",u=a.get("userId")||"",m=a.get("dateFrom")||"",p=a.get("dateTo")||"",f={};if(n&&(f.OR=[{action:{contains:n,mode:"insensitive"}},{details:{contains:n,mode:"insensitive"}},{ipAddress:{contains:n,mode:"insensitive"}},{userAgent:{contains:n,mode:"insensitive"}}]),c&&"all"!==c&&("ERROR"===c?f.action={contains:"ERROR",mode:"insensitive"}:"LOGIN"===c?f.action={contains:"LOGIN",mode:"insensitive"}:"ADMIN"===c?f.action={contains:"ADMIN",mode:"insensitive"}:"PAYMENT"===c?f.OR=[{action:{contains:"PAYMENT",mode:"insensitive"}},{action:{contains:"WALLET",mode:"insensitive"}},{action:{contains:"DEPOSIT",mode:"insensitive"}},{action:{contains:"WITHDRAWAL",mode:"insensitive"}}]:f.action={contains:c,mode:"insensitive"}),u&&(f.userId=u),(m||p)&&(f.createdAt={},m&&(f.createdAt.gte=new Date(m)),p)){let e=new Date(p);e.setHours(23,59,59,999),f.createdAt.lte=e}let[y,h]=await Promise.all([d.prisma.systemLog.findMany({where:f,include:{user:{select:{firstName:!0,lastName:!0,email:!0}}},orderBy:{createdAt:"desc"},skip:(i-1)*s,take:s}),d.prisma.systemLog.count({where:f})]),g=Math.ceil(h/s),w=y.map(e=>({...e,user:e.user?{name:`${e.user.firstName} ${e.user.lastName}`.trim(),email:e.user.email}:null}));return o.NextResponse.json({success:!0,logs:w,total:h,totalPages:g,currentPage:i})}catch(e){return console.error("Admin logs fetch error:",e),o.NextResponse.json({success:!1,error:"Failed to fetch logs"},{status:500})}}let u=new i.AppRouteRouteModule({definition:{kind:s.RouteKind.APP_ROUTE,page:"/api/admin/logs/route",pathname:"/api/admin/logs",filename:"route",bundlePath:"app/api/admin/logs/route"},resolvedPagePath:"C:\\Users\\<USER>\\Desktop\\Hash_Minings\\hashcorex\\src\\app\\api\\admin\\logs\\route.ts",nextConfigOutput:"",userland:a}),{workAsyncStorage:m,workUnitAsyncStorage:p,serverHooks:f}=u;function y(){return(0,n.patchFetch)({workAsyncStorage:m,workUnitAsyncStorage:p})}},96330:e=>{e.exports=require("@prisma/client")}};var t=require("../../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),a=t.X(0,[4447,580,5315,3306],()=>r(95317));module.exports=a})();