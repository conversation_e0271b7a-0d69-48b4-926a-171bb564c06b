"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[6469],{6469:(e,t,s)=>{s.d(t,{$n:()=>o,Zp:()=>c,Wu:()=>u,aR:()=>d,ZB:()=>m,pd:()=>x,Rh:()=>p,aF:()=>g,vp:()=>U,G_:()=>j,eC:()=>k});var r=s(5155),a=s(2115),l=s(2085),n=s(9434);let i=(0,l.F)("inline-flex items-center justify-center rounded-xl font-semibold focus:outline-none focus:ring-2 focus:ring-offset-2 disabled:opacity-50 disabled:pointer-events-none shadow-lg",{variants:{variant:{primary:"bg-yellow-500 text-white focus:ring-yellow-500",secondary:"bg-gray-200 text-gray-800 focus:ring-gray-500",success:"bg-emerald-500 text-white focus:ring-emerald-500",danger:"bg-red-500 text-white focus:ring-red-500",warning:"bg-yellow-500 text-white focus:ring-yellow-500",destructive:"bg-red-600 text-white focus:ring-red-500",outline:"border-2 border-yellow-500 bg-transparent text-yellow-600 focus:ring-yellow-500",ghost:"text-gray-600 focus:ring-yellow-500 rounded-lg",link:"text-yellow-600 underline-offset-4 focus:ring-yellow-500",premium:"bg-slate-800 text-white focus:ring-slate-500",glass:"glass-morphism text-slate-900 backdrop-blur-xl border border-white/20"},size:{sm:"h-10 px-4 text-sm rounded-lg",md:"h-12 px-6 text-base rounded-xl",lg:"h-14 px-8 text-lg rounded-xl",xl:"h-16 px-10 text-xl rounded-2xl font-bold",icon:"h-12 w-12 rounded-xl"}},defaultVariants:{variant:"primary",size:"md"}}),o=a.forwardRef((e,t)=>{let{className:s,variant:a,size:l,loading:o,leftIcon:c,rightIcon:d,children:m,disabled:u,...x}=e;return(0,r.jsxs)("button",{className:(0,n.cn)(i({variant:a,size:l,className:s})),ref:t,disabled:u||o,...x,children:[o&&(0,r.jsx)("div",{className:"mr-2",children:(0,r.jsx)("div",{className:"spinner"})}),c&&!o&&(0,r.jsx)("span",{className:"mr-2",children:c}),m,d&&!o&&(0,r.jsx)("span",{className:"ml-2",children:d})]})});o.displayName="Button";let c=a.forwardRef((e,t)=>{let{className:s,children:a,...l}=e;return(0,r.jsx)("div",{ref:t,className:(0,n.cn)("rounded-2xl border border-gray-200/50 bg-white/90 backdrop-blur-xl shadow-xl overflow-hidden",s),...l,children:a})});c.displayName="Card";let d=a.forwardRef((e,t)=>{let{className:s,children:a,...l}=e;return(0,r.jsx)("div",{ref:t,className:(0,n.cn)("flex flex-col space-y-1.5 p-6 pb-4",s),...l,children:a})});d.displayName="CardHeader";let m=a.forwardRef((e,t)=>{let{className:s,children:a,...l}=e;return(0,r.jsx)("h3",{ref:t,className:(0,n.cn)("text-xl font-semibold leading-none tracking-tight text-dark-900",s),...l,children:a})});m.displayName="CardTitle",a.forwardRef((e,t)=>{let{className:s,children:a,...l}=e;return(0,r.jsx)("p",{ref:t,className:(0,n.cn)("text-sm text-gray-500",s),...l,children:a})}).displayName="CardDescription";let u=a.forwardRef((e,t)=>{let{className:s,children:a,...l}=e;return(0,r.jsx)("div",{ref:t,className:(0,n.cn)("p-6 pt-0",s),...l,children:a})});u.displayName="CardContent",a.forwardRef((e,t)=>{let{className:s,children:a,...l}=e;return(0,r.jsx)("div",{ref:t,className:(0,n.cn)("flex items-center p-6 pt-0",s),...l,children:a})}).displayName="CardFooter";let x=a.forwardRef((e,t)=>{let{className:s,type:a,label:l,error:i,leftIcon:o,rightIcon:c,...d}=e;return(0,r.jsxs)("div",{className:"w-full",children:[l&&(0,r.jsx)("label",{className:"block text-sm font-medium text-dark-700 mb-2",children:l}),(0,r.jsxs)("div",{className:"relative",children:[o&&(0,r.jsx)("div",{className:"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none",children:(0,r.jsx)("span",{className:"text-gray-400",children:o})}),(0,r.jsx)("input",{type:a,className:(0,n.cn)("flex h-14 w-full rounded-xl border-2 border-gray-200 bg-white/90 backdrop-blur-sm px-4 py-4 text-base shadow-inner focus:shadow-lg placeholder:text-gray-400 focus:outline-none focus:ring-2 focus:ring-solar-500 focus:border-solar-500 disabled:cursor-not-allowed disabled:opacity-50 transition-all duration-300 hover:border-gray-300",o&&"pl-12",c&&"pr-12",i&&"border-red-500 focus:ring-red-500 focus:border-red-500",s),ref:t,...d}),c&&(0,r.jsx)("div",{className:"absolute inset-y-0 right-0 pr-3 flex items-center",children:(0,r.jsx)("span",{className:"text-gray-400",children:c})})]}),i&&(0,r.jsx)("p",{className:"mt-1 text-sm text-red-600",children:i})]})});x.displayName="Input";var f=s(7650),h=s(4416);let g=e=>{let{isOpen:t,onClose:s,title:l,children:i,size:c="md",showCloseButton:d=!0,darkMode:m=!1}=e;if((0,a.useEffect)(()=>(t?document.body.style.overflow="hidden":document.body.style.overflow="unset",()=>{document.body.style.overflow="unset"}),[t]),(0,a.useEffect)(()=>{let e=e=>{"Escape"===e.key&&s()};return t&&document.addEventListener("keydown",e),()=>{document.removeEventListener("keydown",e)}},[t,s]),!t)return null;let u=(0,r.jsxs)("div",{className:"fixed inset-0 z-50 flex items-center justify-center p-4",children:[(0,r.jsx)("div",{className:"fixed inset-0 bg-black bg-opacity-50 transition-opacity",onClick:s}),(0,r.jsxs)("div",{className:(0,n.cn)("relative w-full rounded-xl shadow-xl transform transition-all",m?"bg-slate-800":"bg-white",{sm:"max-w-md",md:"max-w-lg",lg:"max-w-2xl",xl:"max-w-4xl"}[c]),onClick:e=>e.stopPropagation(),children:[(0,r.jsxs)("div",{className:(0,n.cn)("flex items-center justify-between p-6 border-b",m?"border-slate-700":"border-gray-200"),children:[(0,r.jsx)("h2",{className:(0,n.cn)("text-xl font-semibold",m?"text-white":"text-dark-900"),children:l}),d&&(0,r.jsx)(o,{variant:"ghost",size:"icon",onClick:s,className:"h-8 w-8 rounded-full",children:(0,r.jsx)(h.A,{className:"h-4 w-4"})})]}),(0,r.jsx)("div",{className:"p-6",children:i})]})]});return(0,f.createPortal)(u,document.body)},p=e=>{let{size:t="md",className:s,text:a}=e;return(0,r.jsxs)("div",{className:(0,n.cn)("flex flex-col items-center justify-center",s),children:[(0,r.jsx)("div",{className:(0,n.cn)("animate-spin rounded-full border-2 border-gray-300 border-t-solar-500",{sm:"h-4 w-4",md:"h-8 w-8",lg:"h-12 w-12"}[t])}),a&&(0,r.jsx)("p",{className:"mt-2 text-sm text-gray-600",children:a})]})};var b=s(1243),w=s(646),y=s(1284);let v=e=>{let{isOpen:t,onClose:s,onConfirm:a,title:l,message:i,confirmText:c="Confirm",cancelText:d="Cancel",variant:m="default",darkMode:u=!1,loading:x=!1}=e;if(!t)return null;let g=(0,r.jsxs)("div",{className:"fixed inset-0 z-50 flex items-center justify-center p-4",children:[(0,r.jsx)("div",{className:"absolute inset-0 bg-black bg-opacity-50 transition-opacity",onClick:s}),(0,r.jsxs)("div",{className:(0,n.cn)("relative w-full max-w-md rounded-xl shadow-xl transform transition-all",u?"bg-slate-800 border border-slate-700":"bg-white"),onClick:e=>e.stopPropagation(),children:[(0,r.jsxs)("div",{className:(0,n.cn)("flex items-center justify-between p-6 border-b",u?"border-slate-700":"border-gray-200"),children:[(0,r.jsxs)("div",{className:"flex items-center space-x-3",children:[(()=>{switch(m){case"danger":return(0,r.jsx)(b.A,{className:"h-6 w-6 text-red-500"});case"warning":return(0,r.jsx)(b.A,{className:"h-6 w-6 text-yellow-500"});case"success":return(0,r.jsx)(w.A,{className:"h-6 w-6 text-green-500"});default:return(0,r.jsx)(y.A,{className:"h-6 w-6 text-blue-500"})}})(),(0,r.jsx)("h2",{className:(0,n.cn)("text-lg font-semibold",u?"text-white":"text-gray-900"),children:l})]}),(0,r.jsx)(o,{variant:"ghost",size:"icon",onClick:s,disabled:x,className:"h-8 w-8 rounded-full",children:(0,r.jsx)(h.A,{className:"h-4 w-4"})})]}),(0,r.jsx)("div",{className:"p-6",children:"string"==typeof i?(0,r.jsx)("p",{className:(0,n.cn)("text-sm leading-relaxed",u?"text-slate-300":"text-gray-600"),children:i}):(0,r.jsx)("div",{className:(0,n.cn)("text-sm leading-relaxed",u?"text-slate-300":"text-gray-600"),children:i})}),(0,r.jsxs)("div",{className:(0,n.cn)("flex items-center justify-end space-x-3 p-6 border-t",u?"border-slate-700":"border-gray-200"),children:[(0,r.jsx)(o,{variant:"outline",onClick:s,disabled:x,className:(0,n.cn)(u?"border-slate-600 text-slate-300 hover:bg-slate-700 hover:text-white":""),children:d}),(0,r.jsx)(o,{variant:(()=>{switch(m){case"danger":return"destructive";case"warning":return"warning";default:return"default"}})(),onClick:a,disabled:x,className:(0,n.cn)(x&&"opacity-50 cursor-not-allowed"),children:x?"Processing...":c})]})]})]});return(0,f.createPortal)(g,document.body)},j=()=>{let[e,t]=a.useState({isOpen:!1,title:"",message:"",onConfirm:()=>{}}),[s,l]=a.useState(!1),n=()=>{s||t(e=>({...e,isOpen:!1}))};return{showConfirm:e=>{t({isOpen:!0,...e,onConfirm:async()=>{l(!0);try{await e.onConfirm(),t(e=>({...e,isOpen:!1}))}catch(e){console.error("Confirm action failed:",e)}finally{l(!1)}}})},hideConfirm:n,ConfirmDialog:()=>(0,r.jsx)(v,{isOpen:e.isOpen,onClose:n,onConfirm:e.onConfirm,title:e.title,message:e.message,variant:e.variant,confirmText:e.confirmText,cancelText:e.cancelText,darkMode:e.darkMode,loading:s}),loading:s}};var N=s(5339);let C=e=>{let{isOpen:t,onClose:s,title:a,message:l,variant:i="info",darkMode:c=!1,showCloseButton:d=!0,buttonText:m="OK",size:u="md"}=e;if(!t)return null;let x=(0,r.jsxs)("div",{className:"fixed inset-0 z-50 flex items-center justify-center p-4",onClick:s,children:[(0,r.jsx)("div",{className:"absolute inset-0 bg-black/50 backdrop-blur-sm"}),(0,r.jsxs)("div",{className:(0,n.cn)("relative w-full rounded-xl shadow-xl transform transition-all",c?"bg-slate-800 border border-slate-700":"bg-white",{sm:"max-w-sm",md:"max-w-md",lg:"max-w-lg"}[u]),onClick:e=>e.stopPropagation(),children:[(0,r.jsxs)("div",{className:(0,n.cn)("flex items-center justify-between p-6 border-b",c?"border-slate-700":"border-gray-200"),children:[(0,r.jsxs)("div",{className:"flex items-center space-x-3",children:[(()=>{switch(i){case"error":return(0,r.jsx)(N.A,{className:"h-6 w-6 text-red-500"});case"warning":return(0,r.jsx)(b.A,{className:"h-6 w-6 text-yellow-500"});case"success":return(0,r.jsx)(w.A,{className:"h-6 w-6 text-green-500"});default:return(0,r.jsx)(y.A,{className:"h-6 w-6 text-blue-500"})}})(),(0,r.jsx)("h2",{className:(0,n.cn)("text-lg font-semibold",c?"text-white":"text-gray-900"),children:a})]}),d&&(0,r.jsx)(o,{variant:"ghost",size:"icon",onClick:s,className:"h-8 w-8 rounded-full",children:(0,r.jsx)(h.A,{className:"h-4 w-4"})})]}),(0,r.jsx)("div",{className:"p-6",children:"string"==typeof l?(0,r.jsx)("p",{className:(0,n.cn)("text-sm leading-relaxed",c?"text-slate-300":"text-gray-600"),children:l}):(0,r.jsx)("div",{className:(0,n.cn)("text-sm leading-relaxed",c?"text-slate-300":"text-gray-600"),children:l})}),(0,r.jsx)("div",{className:(0,n.cn)("flex items-center justify-end p-6 border-t",c?"border-slate-700":"border-gray-200"),children:(0,r.jsx)(o,{variant:(()=>{switch(i){case"error":return"danger";case"warning":return"warning";case"success":return"success";default:return"primary"}})(),onClick:s,className:"min-w-[80px]",children:m})})]})]});return(0,f.createPortal)(x,document.body)},k=()=>{let[e,t]=a.useState({isOpen:!1,title:"",message:""}),s=()=>{t(e=>({...e,isOpen:!1}))};return{showMessage:e=>{t({isOpen:!0,...e})},hideMessage:s,MessageBoxComponent:()=>(0,r.jsx)(C,{...e,onClose:s})}};var D=s(1007),T=s(4355),A=s(9869),P=s(6766);let U=e=>{let{currentPicture:t,onUpload:s,onRemove:l,loading:n=!1,disabled:i=!1}=e,[c,d]=(0,a.useState)(null),[m,u]=(0,a.useState)(!1),x=(0,a.useRef)(null),f=e=>{if(!e)return;if(!e.type.startsWith("image/"))return void alert("Please select an image file");if(e.size>5242880)return void alert("File size must be less than 5MB");let t=new FileReader;t.onload=e=>{var t;d(null==(t=e.target)?void 0:t.result)},t.readAsDataURL(e),s(e)},g=c||t;return(0,r.jsxs)("div",{className:"flex flex-col items-center space-y-4",children:[(0,r.jsxs)("div",{className:"relative",children:[(0,r.jsx)("div",{className:"w-32 h-32 rounded-full overflow-hidden border-4 border-gray-200 bg-gray-100 flex items-center justify-center",children:g?(0,r.jsx)(P.default,{src:g,alt:"Profile Picture",width:128,height:128,className:"w-full h-full object-cover"}):(0,r.jsx)(D.A,{className:"w-16 h-16 text-gray-400"})}),n&&(0,r.jsx)("div",{className:"absolute inset-0 bg-black bg-opacity-50 rounded-full flex items-center justify-center",children:(0,r.jsx)("div",{className:"animate-spin rounded-full h-8 w-8 border-b-2 border-white"})}),g&&!n&&(0,r.jsx)("button",{onClick:()=>{d(null),l&&l()},disabled:i,className:"absolute -top-2 -right-2 bg-red-500 text-white rounded-full p-1 hover:bg-red-600 transition-colors",children:(0,r.jsx)(h.A,{className:"w-4 h-4"})})]}),(0,r.jsx)("div",{className:"border-2 border-dashed rounded-lg p-6 text-center transition-colors ".concat(m?"border-blue-400 bg-blue-50":"border-gray-300 hover:border-gray-400"," ").concat(i?"opacity-50 cursor-not-allowed":"cursor-pointer"),onDrop:e=>{e.preventDefault(),u(!1);let t=e.dataTransfer.files[0];t&&f(t)},onDragOver:e=>{e.preventDefault(),u(!0)},onDragLeave:e=>{e.preventDefault(),u(!1)},onClick:()=>{var e;return!i&&(null==(e=x.current)?void 0:e.click())},children:(0,r.jsxs)("div",{className:"flex flex-col items-center space-y-2",children:[(0,r.jsx)(T.A,{className:"w-8 h-8 text-gray-400"}),(0,r.jsxs)("div",{children:[(0,r.jsx)("p",{className:"text-sm font-medium text-gray-700",children:"Click to upload or drag and drop"}),(0,r.jsx)("p",{className:"text-xs text-gray-500",children:"PNG, JPG, GIF up to 5MB"})]})]})}),(0,r.jsx)("input",{ref:x,type:"file",accept:"image/*",onChange:e=>{var t;let s=null==(t=e.target.files)?void 0:t[0];s&&f(s)},className:"hidden",disabled:i}),(0,r.jsxs)(o,{onClick:()=>{var e;return null==(e=x.current)?void 0:e.click()},disabled:i||n,variant:"outline",size:"sm",className:"flex items-center space-x-2",children:[(0,r.jsx)(A.A,{className:"w-4 h-4"}),(0,r.jsx)("span",{children:"Choose File"})]})]})}},9434:(e,t,s)=>{s.d(t,{D1:()=>f,Oj:()=>m,Yq:()=>o,ZU:()=>x,ZV:()=>i,cn:()=>l,jI:()=>u,lW:()=>d,r6:()=>c,vv:()=>n});var r=s(2596),a=s(9688);function l(){for(var e=arguments.length,t=Array(e),s=0;s<e;s++)t[s]=arguments[s];return(0,a.QP)((0,r.$)(t))}function n(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"USD";return new Intl.NumberFormat("en-US",{style:"currency",currency:t,minimumFractionDigits:2,maximumFractionDigits:2}).format(e)}function i(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:2;return new Intl.NumberFormat("en-US",{minimumFractionDigits:t,maximumFractionDigits:t}).format(e)}function o(e){let t="string"==typeof e?new Date(e):e;return new Intl.DateTimeFormat("en-US",{year:"numeric",month:"short",day:"numeric"}).format(t)}function c(e){let t="string"==typeof e?new Date(e):e;return new Intl.DateTimeFormat("en-US",{year:"numeric",month:"short",day:"numeric",hour:"2-digit",minute:"2-digit"}).format(t)}function d(e){if(navigator.clipboard)return navigator.clipboard.writeText(e);let t=document.createElement("textarea");t.value=e,document.body.appendChild(t),t.focus(),t.select();try{return document.execCommand("copy"),Promise.resolve()}catch(e){return Promise.reject(e)}finally{document.body.removeChild(t)}}function m(e){let t=[],s=[],r=e.length>=8;s.push({valid:r,message:"At least 8 characters long"}),r||t.push("Password must be at least 8 characters long");let a=/[A-Z]/.test(e);s.push({valid:a,message:"At least one uppercase letter"}),a||t.push("Password must contain at least one uppercase letter");let l=/[a-z]/.test(e);s.push({valid:l,message:"At least one lowercase letter"}),l||t.push("Password must contain at least one lowercase letter");let n=/\d/.test(e);s.push({valid:n,message:"At least one number"}),n||t.push("Password must contain at least one number");let i=/[!@#$%^&*(),.?":{}|<>]/.test(e);return s.push({valid:i,message:"At least one special character"}),i||t.push("Password must contain at least one special character"),{isValid:0===t.length,errors:t,checks:s}}function u(e){return e>=1e3?"".concat((e/1e3).toFixed(1),"K TH/s"):"".concat(e.toFixed(2)," TH/s")}function x(){let e=new Date,t=new Date;t.setUTCDate(e.getUTCDate()+(6-e.getUTCDay())),t.setUTCHours(15,0,0,0),e>t&&t.setUTCDate(t.getUTCDate()+7);let s=t.getTime()-e.getTime(),r=Math.floor(s/864e5),a=Math.floor(s%864e5/36e5);return{days:r,hours:a,minutes:Math.floor(s%36e5/6e4),seconds:Math.floor(s%6e4/1e3)}}function f(){let e=new Date,t=new Date;t.setUTCDate(e.getUTCDate()+(6-e.getUTCDay())),t.setUTCHours(15,0,0,0),e>t&&t.setUTCDate(t.getUTCDate()+7);let s=t.getTime()-e.getTime(),r=Math.floor(s/864e5),a=Math.floor(s%864e5/36e5);return{days:r,hours:a,minutes:Math.floor(s%36e5/6e4),seconds:Math.floor(s%6e4/1e3)}}}}]);