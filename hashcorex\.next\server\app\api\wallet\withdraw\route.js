"use strict";(()=>{var e={};e.id=1938,e.ids=[1938],e.modules={3295:e=>{e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10157:(e,t,r)=>{r.r(t),r.d(t,{patchFetch:()=>x,routeModule:()=>p,serverHooks:()=>m,workAsyncStorage:()=>h,workUnitAsyncStorage:()=>f});var a={};r.r(a),r.d(a,{GET:()=>w,POST:()=>c});var s=r(96559),i=r(48088),n=r(37719),o=r(32190),d=r(12909),u=r(6710),l=r(31183);async function c(e){try{let{authenticated:t,user:r}=await (0,d.b9)(e);if(!t||!r)return o.NextResponse.json({success:!1,error:"Not authenticated"},{status:401});let{amount:a,usdtAddress:s}=await e.json();if(!a||!s||a<=0)return o.NextResponse.json({success:!1,error:"Invalid amount or USDT address"},{status:400});if(!s.match(/^T[A-Za-z1-9]{33}$/))return o.NextResponse.json({success:!1,error:"Invalid USDT TRC20 address format"},{status:400});let i=await u.Gy.findByEmail(r.email);if(!i||"APPROVED"!==i.kycStatus)return o.NextResponse.json({success:!1,error:"KYC verification required for withdrawals"},{status:400});let n=parseFloat(await u.rs.get("minWithdrawalAmount")||"10"),l=parseFloat(await u.rs.get("withdrawalFeeFixed")||"3"),c=parseFloat(await u.rs.get("withdrawalFeePercentage")||"1");if(a<n)return o.NextResponse.json({success:!1,error:`Minimum withdrawal amount is $${n}`},{status:400});let w=a*c/100,p=l+w,h=a+p,f=(await u.k_.getOrCreate(r.id)).availableBalance;if(f<h)return o.NextResponse.json({success:!1,error:`Insufficient balance. Required: $${h.toFixed(2)} (Amount: $${a} + Fees: $${p.toFixed(2)}), Available: $${f.toFixed(2)}`},{status:400});if((await u.wJ.findPending()).filter(e=>e.userId===r.id).length>0)return o.NextResponse.json({success:!1,error:"You have a pending withdrawal request. Please wait for it to be processed."},{status:400});let m=await u.wJ.create({userId:r.id,amount:a,usdtAddress:s});return await u.k_.deductWithdrawal(r.id,h),await u.DR.create({userId:r.id,type:"WITHDRAWAL",amount:h,description:`USDT withdrawal: $${a} (Fees: $${p.toFixed(2)}) to ${s.slice(0,8)}...${s.slice(-8)}`,status:"PENDING",reference:m.id}),await u.AJ.create({action:"WITHDRAWAL_REQUESTED",userId:r.id,details:{withdrawalId:m.id,requestedAmount:a,netAmount:a,totalFees:p,fixedFee:l,percentageFee:w,totalDeduction:h,usdtAddress:s,balanceAfter:f-h},ipAddress:e.headers.get("x-forwarded-for")||"unknown",userAgent:e.headers.get("user-agent")||"unknown"}),o.NextResponse.json({success:!0,message:"Withdrawal request submitted successfully",data:{withdrawalId:m.id,requestedAmount:a,netAmount:a,totalFees:p,fixedFee:l,percentageFee:w,totalDeduction:h,status:"PENDING"}})}catch(e){return console.error("Withdrawal request error:",e),o.NextResponse.json({success:!1,error:"Failed to create withdrawal request"},{status:500})}}async function w(e){try{let{authenticated:t,user:r}=await (0,d.b9)(e);if(!t||!r)return o.NextResponse.json({success:!1,error:"Not authenticated"},{status:401});let a=(await l.prisma.withdrawalRequest.findMany({where:{userId:r.id},orderBy:{createdAt:"desc"},take:50})).map(e=>({id:e.id,amount:e.amount,usdtAddress:e.usdtAddress,status:e.status,txid:e.txid,createdAt:e.createdAt,processedAt:e.processedAt,rejectionReason:e.rejectionReason}));return o.NextResponse.json({success:!0,data:a})}catch(e){return console.error("Withdrawal history fetch error:",e),o.NextResponse.json({success:!1,error:"Failed to fetch withdrawal history"},{status:500})}}let p=new s.AppRouteRouteModule({definition:{kind:i.RouteKind.APP_ROUTE,page:"/api/wallet/withdraw/route",pathname:"/api/wallet/withdraw",filename:"route",bundlePath:"app/api/wallet/withdraw/route"},resolvedPagePath:"C:\\Users\\<USER>\\Desktop\\Hash_Minings\\hashcorex\\src\\app\\api\\wallet\\withdraw\\route.ts",nextConfigOutput:"",userland:a}),{workAsyncStorage:h,workUnitAsyncStorage:f,serverHooks:m}=p;function x(){return(0,n.patchFetch)({workAsyncStorage:h,workUnitAsyncStorage:f})}},10846:e=>{e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},12909:(e,t,r)=>{r.d(t,{DT:()=>x,DY:()=>f,HU:()=>c,Lx:()=>m,b9:()=>h,qc:()=>y});var a=r(85663),s=r(43205),i=r.n(s),n=r(6710);let o=process.env.JWT_SECRET||"fallback-secret-key",d=process.env.JWT_EXPIRES_IN||"30d",u=async e=>await a.Ay.hash(e,12),l=async(e,t)=>await a.Ay.compare(e,t),c=e=>i().sign(e,o,{expiresIn:d}),w=e=>{try{return i().verify(e,o)}catch(e){return null}},p=()=>{let e="ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789",t="HC";for(let r=0;r<8;r++)t+=e.charAt(Math.floor(Math.random()*e.length));return t},h=async e=>{let t=e.headers.get("authorization")?.replace("Bearer ","")||e.cookies.get("auth-token")?.value;if(!t)return{authenticated:!1,user:null};let r=w(t);if(!r)return{authenticated:!1,user:null};let a=await n.Gy.findByEmail(r.email);return a?{authenticated:!0,user:a}:{authenticated:!1,user:null}},f=async e=>{let t,a;if(await n.Gy.findByEmail(e.email))throw Error("User already exists with this email");if(e.referralCode){let r=await n.Gy.findByReferralId(e.referralCode);if(!r)throw Error("Invalid referral code");t=r.id}let s=await u(e.password),i=!1;do a=p(),i=!await n.Gy.findByReferralId(a);while(!i);let o=await n.Gy.create({email:e.email,firstName:e.firstName,lastName:e.lastName,password:s,referralId:a});if(t){let{placeUserByReferralType:a}=await r.e(2746).then(r.bind(r,2746)),s="general";"left"===e.placementSide?s="left":"right"===e.placementSide&&(s="right"),await a(t,o.id,s)}return{id:o.id,email:o.email,referralId:o.referralId,kycStatus:o.kycStatus}},m=async e=>{let t=await n.Gy.findByEmail(e.email);if(!t||!await l(e.password,t.password))throw Error("Invalid email or password");return{token:c({userId:t.id,email:t.email}),user:{id:t.id,email:t.email,referralId:t.referralId,kycStatus:t.kycStatus}}},x=e=>/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(e),y=async e=>{let t=await n.Gy.findById(e);return t?.role==="ADMIN"}},27910:e=>{e.exports=require("stream")},28354:e=>{e.exports=require("util")},29294:e=>{e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},44870:e=>{e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},55511:e=>{e.exports=require("crypto")},63033:e=>{e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},79428:e=>{e.exports=require("buffer")},96330:e=>{e.exports=require("@prisma/client")}};var t=require("../../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),a=t.X(0,[4447,580,5315,3306],()=>r(10157));module.exports=a})();