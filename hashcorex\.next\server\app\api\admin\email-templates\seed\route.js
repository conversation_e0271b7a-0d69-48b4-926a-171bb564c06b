"use strict";(()=>{var e={};e.id=3392,e.ids=[3392],e.modules={3295:e=>{e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},12909:(e,t,r)=>{r.d(t,{DT:()=>x,DY:()=>g,HU:()=>c,Lx:()=>f,b9:()=>m,qc:()=>y});var a=r(85663),o=r(43205),i=r.n(o),s=r(6710);let n=process.env.JWT_SECRET||"fallback-secret-key",d=process.env.JWT_EXPIRES_IN||"30d",l=async e=>await a.Ay.hash(e,12),p=async(e,t)=>await a.Ay.compare(e,t),c=e=>i().sign(e,n,{expiresIn:d}),h=e=>{try{return i().verify(e,n)}catch(e){return null}},u=()=>{let e="ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789",t="HC";for(let r=0;r<8;r++)t+=e.charAt(Math.floor(Math.random()*e.length));return t},m=async e=>{let t=e.headers.get("authorization")?.replace("Bearer ","")||e.cookies.get("auth-token")?.value;if(!t)return{authenticated:!1,user:null};let r=h(t);if(!r)return{authenticated:!1,user:null};let a=await s.Gy.findByEmail(r.email);return a?{authenticated:!0,user:a}:{authenticated:!1,user:null}},g=async e=>{let t,a;if(await s.Gy.findByEmail(e.email))throw Error("User already exists with this email");if(e.referralCode){let r=await s.Gy.findByReferralId(e.referralCode);if(!r)throw Error("Invalid referral code");t=r.id}let o=await l(e.password),i=!1;do a=u(),i=!await s.Gy.findByReferralId(a);while(!i);let n=await s.Gy.create({email:e.email,firstName:e.firstName,lastName:e.lastName,password:o,referralId:a});if(t){let{placeUserByReferralType:a}=await r.e(2746).then(r.bind(r,2746)),o="general";"left"===e.placementSide?o="left":"right"===e.placementSide&&(o="right"),await a(t,n.id,o)}return{id:n.id,email:n.email,referralId:n.referralId,kycStatus:n.kycStatus}},f=async e=>{let t=await s.Gy.findByEmail(e.email);if(!t||!await p(e.password,t.password))throw Error("Invalid email or password");return{token:c({userId:t.id,email:t.email}),user:{id:t.id,email:t.email,referralId:t.referralId,kycStatus:t.kycStatus}}},x=e=>/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(e),y=async e=>{let t=await s.Gy.findById(e);return t?.role==="ADMIN"}},27910:e=>{e.exports=require("stream")},28354:e=>{e.exports=require("util")},29294:e=>{e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},44870:e=>{e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},55511:e=>{e.exports=require("crypto")},63033:e=>{e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},79428:e=>{e.exports=require("buffer")},82629:(e,t,r)=>{r.d(t,{v5:()=>o});var a=r(6710);class o{static async logError(e){try{let t=e.error instanceof Error?e.error:Error(String(e.error)),r={action:e.action,userId:e.userId,adminId:e.adminId,details:{message:t.message,stack:t.stack,name:t.name,requestUrl:e.requestUrl,requestMethod:e.requestMethod,requestBody:e.requestBody,additionalData:e.additionalData,timestamp:new Date().toISOString()},ipAddress:e.ipAddress,userAgent:e.userAgent};await a.AJ.create(r),console.error(`[${e.action}] Error logged:`,{message:t.message,stack:t.stack,userId:e.userId,adminId:e.adminId,url:e.requestUrl})}catch(t){console.error("Failed to log error to database:",t),console.error("Original error:",e.error)}}static async logApiError(e,t,r,a,o,i){try{let s=null;try{if("GET"!==e.method&&e.headers.get("content-type")?.includes("application/json")){let t=e.clone();(s=await t.json()).password&&(s.password="[REDACTED]"),s.token&&(s.token="[REDACTED]"),s.apiKey&&(s.apiKey="[REDACTED]")}}catch(e){}await this.logError({action:r,error:t,userId:a,adminId:o,ipAddress:e.headers.get("x-forwarded-for")||e.headers.get("x-real-ip")||"unknown",userAgent:e.headers.get("user-agent")||"unknown",requestUrl:e.url,requestMethod:e.method,requestBody:s,additionalData:i})}catch(e){console.error("Failed to log API error:",e),console.error("Original error:",t)}}static async logClientError(e){try{await a.AJ.create({action:"CLIENT_ERROR",userId:e.userId,details:{message:e.message,stack:e.stack,url:e.url,timestamp:e.timestamp,additionalData:e.additionalData},userAgent:e.userAgent})}catch(e){console.error("Failed to log client error:",e)}}static async logAuthError(e,t,r,a){await this.logApiError(e,t,"AUTH_ERROR",void 0,void 0,{email:r,...a})}static async logDatabaseError(e,t,r,o,i){try{await a.AJ.create({action:"DATABASE_ERROR",userId:o,details:{message:e instanceof Error?e.message:String(e),stack:e instanceof Error?e.stack:void 0,operation:t,table:r,timestamp:new Date().toISOString(),additionalData:i}})}catch(t){console.error("Failed to log database error:",t),console.error("Original error:",e)}}static async logBusinessError(e,t,r,a,o){await this.logError({action:"BUSINESS_LOGIC_ERROR",error:e,userId:r,adminId:a,additionalData:{operation:t,...o}})}static async logExternalApiError(e,t,r,a,o){await this.logError({action:"EXTERNAL_API_ERROR",error:e,userId:a,additionalData:{service:t,endpoint:r,...o}})}static async logValidationError(e,t,r,a,o){await this.logError({action:"VALIDATION_ERROR",error:e,userId:a,additionalData:{field:t,value:r,...o}})}}},88231:(e,t,r)=>{r.r(t),r.d(t,{patchFetch:()=>x,routeModule:()=>u,serverHooks:()=>f,workAsyncStorage:()=>m,workUnitAsyncStorage:()=>g});var a={};r.r(a),r.d(a,{POST:()=>h});var o=r(96559),i=r(48088),s=r(37719),n=r(32190),d=r(12909),l=r(6710),p=r(82629);let c=[{name:"otp_verification",subject:"Email Verification - HashCoreX",htmlContent:`
<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Email Verification</title>
</head>
<body style="font-family: Arial, sans-serif; line-height: 1.6; color: #333; max-width: 600px; margin: 0 auto; padding: 20px;">
    <div style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); padding: 30px; text-align: center; border-radius: 10px 10px 0 0;">
        <h1 style="color: white; margin: 0; font-size: 28px;">HashCoreX</h1>
        <p style="color: #f0f0f0; margin: 10px 0 0 0;">Sustainable Mining Platform</p>
    </div>

    <div style="background: #f9f9f9; padding: 30px; border-radius: 0 0 10px 10px;">
        <h2 style="color: #333; margin-top: 0;">Hello {{firstName}}!</h2>

        <p>Thank you for registering with HashCoreX. To complete your registration, please verify your email address using the OTP code below:</p>

        <div style="background: #fff; border: 2px dashed #667eea; padding: 20px; text-align: center; margin: 20px 0; border-radius: 8px;">
            <h3 style="margin: 0; color: #667eea; font-size: 32px; letter-spacing: 5px;">{{otp}}</h3>
            <p style="margin: 10px 0 0 0; color: #666; font-size: 14px;">This code expires in 10 minutes</p>
        </div>

        <p>If you didn't create an account with HashCoreX, please ignore this email.</p>

        <div style="margin-top: 30px; padding-top: 20px; border-top: 1px solid #ddd; text-align: center; color: #666; font-size: 14px;">
            <p>Best regards,<br>The HashCoreX Team</p>
            <p style="margin-top: 20px;">
                <a href="#" style="color: #667eea; text-decoration: none;">Visit HashCoreX</a> |
                <a href="#" style="color: #667eea; text-decoration: none;">Support</a>
            </p>
        </div>
    </div>
</body>
</html>`,textContent:`Hello {{firstName}}!

Thank you for registering with HashCoreX. To complete your registration, please verify your email address using the OTP code below:

Your OTP Code: {{otp}}

This code expires in 10 minutes.

If you didn't create an account with HashCoreX, please ignore this email.

Best regards,
The HashCoreX Team`},{name:"password_reset",subject:"Password Reset - HashCoreX",htmlContent:`
<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Password Reset</title>
</head>
<body style="font-family: Arial, sans-serif; line-height: 1.6; color: #333; max-width: 600px; margin: 0 auto; padding: 20px;">
    <div style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); padding: 30px; text-align: center; border-radius: 10px 10px 0 0;">
        <h1 style="color: white; margin: 0; font-size: 28px;">HashCoreX</h1>
        <p style="color: #f0f0f0; margin: 10px 0 0 0;">Password Reset Request</p>
    </div>

    <div style="background: #f9f9f9; padding: 30px; border-radius: 0 0 10px 10px;">
        <h2 style="color: #333; margin-top: 0;">Hello {{firstName}}!</h2>

        <p>We received a request to reset your password for your HashCoreX account. Use the OTP code below to proceed with your password reset:</p>

        <div style="background: #fff; border: 2px dashed #667eea; padding: 20px; text-align: center; margin: 20px 0; border-radius: 8px;">
            <h3 style="margin: 0; color: #667eea; font-size: 32px; letter-spacing: 5px;">{{otp}}</h3>
            <p style="margin: 10px 0 0 0; color: #666; font-size: 14px;">This code expires in 10 minutes</p>
        </div>

        <p>If you didn't request a password reset, please ignore this email and your password will remain unchanged.</p>

        <div style="margin-top: 30px; padding-top: 20px; border-top: 1px solid #ddd; text-align: center; color: #666; font-size: 14px;">
            <p>Best regards,<br>The HashCoreX Team</p>
            <p style="margin-top: 20px;">
                <a href="#" style="color: #667eea; text-decoration: none;">Visit HashCoreX</a> |
                <a href="#" style="color: #667eea; text-decoration: none;">Support</a>
            </p>
        </div>
    </div>
</body>
</html>`,textContent:`Hello {{firstName}}!

We received a request to reset your password for your HashCoreX account. Use the OTP code below to proceed with your password reset:

Your OTP Code: {{otp}}

This code expires in 10 minutes.

If you didn't request a password reset, please ignore this email and your password will remain unchanged.

Best regards,
The HashCoreX Team`},{name:"welcome_email",subject:"Welcome to HashCoreX - Your Mining Journey Begins!",htmlContent:`
<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Welcome to HashCoreX</title>
</head>
<body style="font-family: Arial, sans-serif; line-height: 1.6; color: #333; max-width: 600px; margin: 0 auto; padding: 20px;">
    <div style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); padding: 30px; text-align: center; border-radius: 10px 10px 0 0;">
        <h1 style="color: white; margin: 0; font-size: 28px;">Welcome to HashCoreX!</h1>
        <p style="color: #f0f0f0; margin: 10px 0 0 0;">Your Sustainable Mining Journey Starts Here</p>
    </div>

    <div style="background: #f9f9f9; padding: 30px; border-radius: 0 0 10px 10px;">
        <h2 style="color: #333; margin-top: 0;">Hello {{firstName}}!</h2>

        <p>Congratulations! Your HashCoreX account has been successfully created. You're now part of our sustainable mining community.</p>

        <div style="background: #fff; padding: 20px; margin: 20px 0; border-radius: 8px; border-left: 4px solid #667eea;">
            <h3 style="margin-top: 0; color: #667eea;">What's Next?</h3>
            <ul style="margin: 0; padding-left: 20px;">
                <li>Complete your KYC verification</li>
                <li>Explore our mining packages</li>
                <li>Start earning with sustainable mining</li>
                <li>Refer friends and earn bonuses</li>
            </ul>
        </div>

        <div style="text-align: center; margin: 30px 0;">
            <a href="#" style="background: #667eea; color: white; padding: 12px 30px; text-decoration: none; border-radius: 5px; display: inline-block;">Access Dashboard</a>
        </div>

        <div style="margin-top: 30px; padding-top: 20px; border-top: 1px solid #ddd; text-align: center; color: #666; font-size: 14px;">
            <p>Need help? Contact our support team anytime.</p>
            <p>Best regards,<br>The HashCoreX Team</p>
        </div>
    </div>
</body>
</html>`,textContent:`Hello {{firstName}}!

Congratulations! Your HashCoreX account has been successfully created. You're now part of our sustainable mining community.

What's Next?
- Complete your KYC verification
- Explore our mining packages
- Start earning with sustainable mining
- Refer friends and earn bonuses

Visit your dashboard to get started: [Dashboard Link]

Need help? Contact our support team anytime.

Best regards,
The HashCoreX Team`},{name:"deposit_success",subject:"Deposit Confirmed - HashCoreX",htmlContent:`
<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Deposit Confirmed</title>
</head>
<body style="font-family: Arial, sans-serif; line-height: 1.6; color: #333; max-width: 600px; margin: 0 auto; padding: 20px;">
    <div style="background: linear-gradient(135deg, #10b981 0%, #059669 100%); padding: 30px; text-align: center; border-radius: 10px 10px 0 0;">
        <h1 style="color: white; margin: 0; font-size: 28px;">HashCoreX</h1>
        <p style="color: #f0f0f0; margin: 10px 0 0 0;">Deposit Confirmed!</p>
    </div>

    <div style="background: #f9f9f9; padding: 30px; border-radius: 0 0 10px 10px;">
        <h2 style="color: #333; margin-top: 0;">Hello {{firstName}}!</h2>

        <p>Great news! Your deposit has been successfully confirmed and credited to your account.</p>

        <div style="background: #fff; padding: 20px; margin: 20px 0; border-radius: 8px; border-left: 4px solid #10b981;">
            <h3 style="margin-top: 0; color: #10b981;">Deposit Details</h3>
            <p><strong>Amount:</strong> {{amount}} {{currency}}</p>
            <p><strong>Transaction ID:</strong> {{transactionId}}</p>
            <p><strong>Status:</strong> <span style="color: #10b981;">Confirmed</span></p>
        </div>

        <div style="text-align: center; margin: 30px 0;">
            <a href="#" style="background: #10b981; color: white; padding: 12px 30px; text-decoration: none; border-radius: 5px; display: inline-block;">View Dashboard</a>
        </div>

        <p>You can now use these funds to purchase mining packages and start earning!</p>

        <div style="margin-top: 30px; padding-top: 20px; border-top: 1px solid #ddd; text-align: center; color: #666; font-size: 14px;">
            <p>Best regards,<br>The HashCoreX Team</p>
            <p style="margin-top: 20px;">
                <a href="#" style="color: #10b981; text-decoration: none;">Visit HashCoreX</a> |
                <a href="#" style="color: #10b981; text-decoration: none;">Support</a>
            </p>
        </div>
    </div>
</body>
</html>`,textContent:`Hello {{firstName}}!

Great news! Your deposit has been successfully confirmed and credited to your account.

Deposit Details:
- Amount: {{amount}} {{currency}}
- Transaction ID: {{transactionId}}
- Status: Confirmed

You can now use these funds to purchase mining packages and start earning!

Visit your dashboard to get started: [Dashboard Link]

Best regards,
The HashCoreX Team`},{name:"kyc_approved",subject:"KYC Verification Approved - HashCoreX",htmlContent:`
<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>KYC Approved</title>
</head>
<body style="font-family: Arial, sans-serif; line-height: 1.6; color: #333; max-width: 600px; margin: 0 auto; padding: 20px;">
    <div style="background: linear-gradient(135deg, #10b981 0%, #059669 100%); padding: 30px; text-align: center; border-radius: 10px 10px 0 0;">
        <h1 style="color: white; margin: 0; font-size: 28px;">HashCoreX</h1>
        <p style="color: #f0f0f0; margin: 10px 0 0 0;">KYC Verification Approved!</p>
    </div>

    <div style="background: #f9f9f9; padding: 30px; border-radius: 0 0 10px 10px;">
        <h2 style="color: #333; margin-top: 0;">Hello {{firstName}}!</h2>

        <p>Congratulations! Your KYC verification has been approved. Your account is now fully verified and you have access to all HashCoreX features.</p>

        <div style="background: #fff; padding: 20px; margin: 20px 0; border-radius: 8px; border-left: 4px solid #10b981;">
            <h3 style="margin-top: 0; color: #10b981;">What's Now Available?</h3>
            <ul style="margin: 0; padding-left: 20px;">
                <li>Full access to mining packages</li>
                <li>Unlimited deposits and withdrawals</li>
                <li>Access to premium features</li>
                <li>Higher referral commissions</li>
            </ul>
        </div>

        <div style="text-align: center; margin: 30px 0;">
            <a href="#" style="background: #10b981; color: white; padding: 12px 30px; text-decoration: none; border-radius: 5px; display: inline-block;">Access Dashboard</a>
        </div>

        <div style="margin-top: 30px; padding-top: 20px; border-top: 1px solid #ddd; text-align: center; color: #666; font-size: 14px;">
            <p>Best regards,<br>The HashCoreX Team</p>
            <p style="margin-top: 20px;">
                <a href="#" style="color: #10b981; text-decoration: none;">Visit HashCoreX</a> |
                <a href="#" style="color: #10b981; text-decoration: none;">Support</a>
            </p>
        </div>
    </div>
</body>
</html>`,textContent:`Hello {{firstName}}!

Congratulations! Your KYC verification has been approved. Your account is now fully verified and you have access to all HashCoreX features.

What's Now Available?
- Full access to mining packages
- Unlimited deposits and withdrawals
- Access to premium features
- Higher referral commissions

Visit your dashboard to get started: [Dashboard Link]

Best regards,
The HashCoreX Team`},{name:"kyc_rejected",subject:"KYC Verification Update - HashCoreX",htmlContent:`
<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>KYC Update</title>
</head>
<body style="font-family: Arial, sans-serif; line-height: 1.6; color: #333; max-width: 600px; margin: 0 auto; padding: 20px;">
    <div style="background: linear-gradient(135deg, #ef4444 0%, #dc2626 100%); padding: 30px; text-align: center; border-radius: 10px 10px 0 0;">
        <h1 style="color: white; margin: 0; font-size: 28px;">HashCoreX</h1>
        <p style="color: #f0f0f0; margin: 10px 0 0 0;">KYC Verification Update</p>
    </div>

    <div style="background: #f9f9f9; padding: 30px; border-radius: 0 0 10px 10px;">
        <h2 style="color: #333; margin-top: 0;">Hello {{firstName}}!</h2>

        <p>We have reviewed your KYC verification documents. Unfortunately, we need additional information to complete your verification.</p>

        <div style="background: #fff; padding: 20px; margin: 20px 0; border-radius: 8px; border-left: 4px solid #ef4444;">
            <h3 style="margin-top: 0; color: #ef4444;">Reason for Review</h3>
            <p>{{rejectionReason}}</p>
        </div>

        <div style="background: #fff; padding: 20px; margin: 20px 0; border-radius: 8px; border-left: 4px solid #667eea;">
            <h3 style="margin-top: 0; color: #667eea;">Next Steps</h3>
            <ul style="margin: 0; padding-left: 20px;">
                <li>Review the feedback above</li>
                <li>Prepare updated documents</li>
                <li>Resubmit your KYC application</li>
                <li>Contact support if you need help</li>
            </ul>
        </div>

        <div style="text-align: center; margin: 30px 0;">
            <a href="#" style="background: #667eea; color: white; padding: 12px 30px; text-decoration: none; border-radius: 5px; display: inline-block;">Resubmit KYC</a>
        </div>

        <div style="margin-top: 30px; padding-top: 20px; border-top: 1px solid #ddd; text-align: center; color: #666; font-size: 14px;">
            <p>Best regards,<br>The HashCoreX Team</p>
            <p style="margin-top: 20px;">
                <a href="#" style="color: #667eea; text-decoration: none;">Visit HashCoreX</a> |
                <a href="#" style="color: #667eea; text-decoration: none;">Support</a>
            </p>
        </div>
    </div>
</body>
</html>`,textContent:`Hello {{firstName}}!

We have reviewed your KYC verification documents. Unfortunately, we need additional information to complete your verification.

Reason for Review:
{{rejectionReason}}

Next Steps:
- Review the feedback above
- Prepare updated documents
- Resubmit your KYC application
- Contact support if you need help

Visit your dashboard to resubmit: [Dashboard Link]

Best regards,
The HashCoreX Team`},{name:"withdrawal_approved",subject:"Withdrawal Approved - HashCoreX",htmlContent:`
<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Withdrawal Approved</title>
</head>
<body style="font-family: Arial, sans-serif; line-height: 1.6; color: #333; max-width: 600px; margin: 0 auto; padding: 20px;">
    <div style="background: linear-gradient(135deg, #10b981 0%, #059669 100%); padding: 30px; text-align: center; border-radius: 10px 10px 0 0;">
        <h1 style="color: white; margin: 0; font-size: 28px;">HashCoreX</h1>
        <p style="color: #f0f0f0; margin: 10px 0 0 0;">Withdrawal Approved!</p>
    </div>

    <div style="background: #f9f9f9; padding: 30px; border-radius: 0 0 10px 10px;">
        <h2 style="color: #333; margin-top: 0;">Hello {{firstName}}!</h2>

        <p>Good news! Your withdrawal request has been approved and is being processed.</p>

        <div style="background: #fff; padding: 20px; margin: 20px 0; border-radius: 8px; border-left: 4px solid #10b981;">
            <h3 style="margin-top: 0; color: #10b981;">Withdrawal Details</h3>
            <p><strong>Amount:</strong> {{amount}} USDT</p>
            <p><strong>Destination:</strong> {{usdtAddress}}</p>
            <p><strong>Status:</strong> <span style="color: #10b981;">Approved - Processing</span></p>
        </div>

        <p>Your funds will be transferred to your wallet within 24 hours. You will receive another email once the transaction is completed.</p>

        <div style="text-align: center; margin: 30px 0;">
            <a href="#" style="background: #10b981; color: white; padding: 12px 30px; text-decoration: none; border-radius: 5px; display: inline-block;">View Transaction</a>
        </div>

        <div style="margin-top: 30px; padding-top: 20px; border-top: 1px solid #ddd; text-align: center; color: #666; font-size: 14px;">
            <p>Best regards,<br>The HashCoreX Team</p>
            <p style="margin-top: 20px;">
                <a href="#" style="color: #10b981; text-decoration: none;">Visit HashCoreX</a> |
                <a href="#" style="color: #10b981; text-decoration: none;">Support</a>
            </p>
        </div>
    </div>
</body>
</html>`,textContent:`Hello {{firstName}}!

Good news! Your withdrawal request has been approved and is being processed.

Withdrawal Details:
- Amount: {{amount}} USDT
- Destination: {{usdtAddress}}
- Status: Approved - Processing

Your funds will be transferred to your wallet within 24 hours. You will receive another email once the transaction is completed.

Visit your dashboard: [Dashboard Link]

Best regards,
The HashCoreX Team`},{name:"withdrawal_completed",subject:"Withdrawal Completed - HashCoreX",htmlContent:`
<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Withdrawal Completed</title>
</head>
<body style="font-family: Arial, sans-serif; line-height: 1.6; color: #333; max-width: 600px; margin: 0 auto; padding: 20px;">
    <div style="background: linear-gradient(135deg, #10b981 0%, #059669 100%); padding: 30px; text-align: center; border-radius: 10px 10px 0 0;">
        <h1 style="color: white; margin: 0; font-size: 28px;">HashCoreX</h1>
        <p style="color: #f0f0f0; margin: 10px 0 0 0;">Withdrawal Completed!</p>
    </div>

    <div style="background: #f9f9f9; padding: 30px; border-radius: 0 0 10px 10px;">
        <h2 style="color: #333; margin-top: 0;">Hello {{firstName}}!</h2>

        <p>Your withdrawal has been successfully completed! The funds have been transferred to your wallet.</p>

        <div style="background: #fff; padding: 20px; margin: 20px 0; border-radius: 8px; border-left: 4px solid #10b981;">
            <h3 style="margin-top: 0; color: #10b981;">Transaction Details</h3>
            <p><strong>Amount:</strong> {{amount}} USDT</p>
            <p><strong>Destination:</strong> {{usdtAddress}}</p>
            <p><strong>Transaction Hash:</strong> {{transactionHash}}</p>
            <p><strong>Status:</strong> <span style="color: #10b981;">Completed</span></p>
        </div>

        <p>You can verify this transaction on the blockchain using the transaction hash above.</p>

        <div style="text-align: center; margin: 30px 0;">
            <a href="#" style="background: #10b981; color: white; padding: 12px 30px; text-decoration: none; border-radius: 5px; display: inline-block;">View on Blockchain</a>
        </div>

        <div style="margin-top: 30px; padding-top: 20px; border-top: 1px solid #ddd; text-align: center; color: #666; font-size: 14px;">
            <p>Best regards,<br>The HashCoreX Team</p>
            <p style="margin-top: 20px;">
                <a href="#" style="color: #10b981; text-decoration: none;">Visit HashCoreX</a> |
                <a href="#" style="color: #10b981; text-decoration: none;">Support</a>
            </p>
        </div>
    </div>
</body>
</html>`,textContent:`Hello {{firstName}}!

Your withdrawal has been successfully completed! The funds have been transferred to your wallet.

Transaction Details:
- Amount: {{amount}} USDT
- Destination: {{usdtAddress}}
- Transaction Hash: {{transactionHash}}
- Status: Completed

You can verify this transaction on the blockchain using the transaction hash above.

Visit your dashboard: [Dashboard Link]

Best regards,
The HashCoreX Team`},{name:"withdrawal_rejected",subject:"Withdrawal Update - HashCoreX",htmlContent:`
<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Withdrawal Update</title>
</head>
<body style="font-family: Arial, sans-serif; line-height: 1.6; color: #333; max-width: 600px; margin: 0 auto; padding: 20px;">
    <div style="background: linear-gradient(135deg, #ef4444 0%, #dc2626 100%); padding: 30px; text-align: center; border-radius: 10px 10px 0 0;">
        <h1 style="color: white; margin: 0; font-size: 28px;">HashCoreX</h1>
        <p style="color: #f0f0f0; margin: 10px 0 0 0;">Withdrawal Update</p>
    </div>

    <div style="background: #f9f9f9; padding: 30px; border-radius: 0 0 10px 10px;">
        <h2 style="color: #333; margin-top: 0;">Hello {{firstName}}!</h2>

        <p>We regret to inform you that your withdrawal request could not be processed at this time.</p>

        <div style="background: #fff; padding: 20px; margin: 20px 0; border-radius: 8px; border-left: 4px solid #ef4444;">
            <h3 style="margin-top: 0; color: #ef4444;">Withdrawal Details</h3>
            <p><strong>Amount:</strong> {{amount}} USDT</p>
            <p><strong>Reason:</strong> {{rejectionReason}}</p>
        </div>

        <p>The requested amount has been returned to your account balance. Please review the reason above and contact our support team if you need assistance.</p>

        <div style="text-align: center; margin: 30px 0;">
            <a href="#" style="background: #667eea; color: white; padding: 12px 30px; text-decoration: none; border-radius: 5px; display: inline-block;">Contact Support</a>
        </div>

        <div style="margin-top: 30px; padding-top: 20px; border-top: 1px solid #ddd; text-align: center; color: #666; font-size: 14px;">
            <p>Best regards,<br>The HashCoreX Team</p>
            <p style="margin-top: 20px;">
                <a href="#" style="color: #667eea; text-decoration: none;">Visit HashCoreX</a> |
                <a href="#" style="color: #667eea; text-decoration: none;">Support</a>
            </p>
        </div>
    </div>
</body>
</html>`,textContent:`Hello {{firstName}}!

We regret to inform you that your withdrawal request could not be processed at this time.

Withdrawal Details:
- Amount: {{amount}} USDT
- Reason: {{rejectionReason}}

The requested amount has been returned to your account balance. Please review the reason above and contact our support team if you need assistance.

Visit your dashboard: [Dashboard Link]

Best regards,
The HashCoreX Team`}];async function h(e){try{let{authenticated:t,user:r}=await (0,d.b9)(e);if(!t||!r)return n.NextResponse.json({success:!1,error:"Unauthorized"},{status:401});if(!await (0,d.qc)(r.id))return n.NextResponse.json({success:!1,error:"Admin access required"},{status:403});let a=[];for(let e of c)try{if(await l.vo.findByName(e.name)){a.push({name:e.name,status:"exists"});continue}await l.vo.create(e),a.push({name:e.name,status:"created"})}catch(t){a.push({name:e.name,status:"error",error:t.message})}return n.NextResponse.json({success:!0,message:"Default templates seeded successfully",data:a})}catch(t){return console.error("Seed email templates error:",t),await p.v5.logApiError(e,t,"SEED_EMAIL_TEMPLATES_ERROR"),n.NextResponse.json({success:!1,error:"Internal server error"},{status:500})}}let u=new o.AppRouteRouteModule({definition:{kind:i.RouteKind.APP_ROUTE,page:"/api/admin/email-templates/seed/route",pathname:"/api/admin/email-templates/seed",filename:"route",bundlePath:"app/api/admin/email-templates/seed/route"},resolvedPagePath:"C:\\Users\\<USER>\\Desktop\\Hash_Minings\\hashcorex\\src\\app\\api\\admin\\email-templates\\seed\\route.ts",nextConfigOutput:"",userland:a}),{workAsyncStorage:m,workUnitAsyncStorage:g,serverHooks:f}=u;function x(){return(0,s.patchFetch)({workAsyncStorage:m,workUnitAsyncStorage:g})}},96330:e=>{e.exports=require("@prisma/client")}};var t=require("../../../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),a=t.X(0,[4447,580,5315,3306],()=>r(88231));module.exports=a})();