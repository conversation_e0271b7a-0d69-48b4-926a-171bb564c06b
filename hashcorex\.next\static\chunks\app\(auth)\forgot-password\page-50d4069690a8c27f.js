(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[5890],{913:(e,t,a)=>{Promise.resolve().then(a.bind(a,9540))},1470:(e,t,a)=>{"use strict";a.d(t,{CU:()=>o});var r=a(5155),s=a(2115),i=a(5695),u=a(4105),n=a(6469);let c=e=>{let{children:t,requireAuth:a=!1,requireGuest:c=!1,redirectTo:o}=e,{user:l,loading:d}=(0,u.A)(),h=(0,i.useRouter)(),f=(0,i.usePathname)();return((0,s.useEffect)(()=>{if(!d){if(a&&!l){let e="/login?redirect=".concat(encodeURIComponent(f));h.replace(e);return}if(c&&l){let e=o||"/dashboard";h.replace(e);return}}},[l,d,a,c,h,f,o]),d)?(0,r.jsx)("div",{className:"min-h-screen flex items-center justify-center bg-gray-50",children:(0,r.jsxs)("div",{className:"text-center",children:[(0,r.jsx)(n.Rh,{}),(0,r.jsx)("p",{className:"mt-4 text-gray-600",children:"Checking authentication..."})]})}):a&&!l||c&&l?null:(0,r.jsx)(r.Fragment,{children:t})},o=e=>{let{children:t,redirectTo:a}=e;return(0,r.jsx)(c,{requireGuest:!0,redirectTo:a,children:t})}},4105:(e,t,a)=>{"use strict";a.d(t,{A:()=>n,AuthProvider:()=>u});var r=a(5155),s=a(2115);let i=(0,s.createContext)(void 0),u=e=>{let{children:t}=e,[a,u]=(0,s.useState)(null),[n,c]=(0,s.useState)(!0);(0,s.useEffect)(()=>{o()},[]);let o=async()=>{try{let e=await fetch("/api/auth/me",{credentials:"include"});if(e.ok){let t=await e.json();t.success&&u(t.data)}}catch(e){console.error("Auth check failed:",e)}finally{c(!1)}},l=async(e,t)=>{c(!0);try{let a=await fetch("/api/auth/login",{method:"POST",headers:{"Content-Type":"application/json"},credentials:"include",body:JSON.stringify({email:e,password:t})}),r=await a.json();if(!r.success)throw Error(r.error||"Login failed");u(r.data.user),setTimeout(()=>{o()},100)}catch(e){throw e}finally{c(!1)}},d=async(e,t,a,r,s,i,n,o)=>{c(!0);try{let c=await fetch(n?"/api/auth/register?side=".concat(n):"/api/auth/register",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({email:e,firstName:t,lastName:a,password:r,confirmPassword:s,referralCode:i,otp:o})}),l=await c.json();if(!l.success)throw Error(l.error||"Registration failed");u({id:l.data.user.id,email:l.data.user.email,firstName:l.data.user.firstName||"",lastName:l.data.user.lastName||"",referralId:l.data.user.referralId,role:l.data.user.role||"USER",kycStatus:l.data.user.kycStatus,isActive:l.data.user.isActive||!0,profilePicture:l.data.user.profilePicture||null,createdAt:l.data.user.createdAt,updatedAt:l.data.user.updatedAt})}catch(e){throw e}finally{c(!1)}},h=async()=>{try{await fetch("/api/auth/logout",{method:"POST",credentials:"include"})}catch(e){console.error("Logout error:",e)}finally{u(null)}},f=async()=>{await o()};return(0,r.jsx)(i.Provider,{value:{user:a,loading:n,login:l,register:d,logout:h,refreshUser:f},children:t})},n=()=>{let e=(0,s.useContext)(i);if(void 0===e)throw Error("useAuth must be used within an AuthProvider");return e}},5695:(e,t,a)=>{"use strict";var r=a(8999);a.o(r,"usePathname")&&a.d(t,{usePathname:function(){return r.usePathname}}),a.o(r,"useRouter")&&a.d(t,{useRouter:function(){return r.useRouter}}),a.o(r,"useSearchParams")&&a.d(t,{useSearchParams:function(){return r.useSearchParams}})},9540:(e,t,a)=>{"use strict";a.r(t),a.d(t,{default:()=>o});var r=a(5155),s=a(2115),i=a(5695),u=a(9434),n=a(1470);function c(){(0,i.useRouter)();let[e,t]=(0,s.useState)("email"),[a,n]=(0,s.useState)(""),[c,o]=(0,s.useState)(""),[l,d]=(0,s.useState)(""),[h,f]=(0,s.useState)(!1),[m,p]=(0,s.useState)(!1),[y,S]=(0,s.useState)(!1),[j,x]=(0,s.useState)(""),[v,g]=(0,s.useState)("");return(0,u.Oj)(c),(0,r.jsx)("div",{className:"min-h-screen",children:(0,r.jsxs)("div",{className:"container mx-auto p-4",children:[(0,r.jsx)("h1",{children:"Forgot Password"}),(0,r.jsx)("p",{children:"This page is under construction."})]})})}function o(){return(0,r.jsx)(n.CU,{redirectTo:"/dashboard",children:(0,r.jsx)(c,{})})}}},e=>{var t=t=>e(e.s=t);e.O(0,[3473,6469,8441,1684,7358],()=>t(913)),_N_E=e.O()}]);