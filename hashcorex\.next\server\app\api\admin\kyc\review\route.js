"use strict";(()=>{var e={};e.id=2040,e.ids=[2040],e.modules={3295:e=>{e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},12909:(e,r,t)=>{t.d(r,{DT:()=>y,DY:()=>f,HU:()=>c,Lx:()=>h,b9:()=>w,qc:()=>x});var a=t(85663),s=t(43205),i=t.n(s),n=t(6710);let o=process.env.JWT_SECRET||"fallback-secret-key",u=process.env.JWT_EXPIRES_IN||"30d",d=async e=>await a.Ay.hash(e,12),l=async(e,r)=>await a.Ay.compare(e,r),c=e=>i().sign(e,o,{expiresIn:u}),p=e=>{try{return i().verify(e,o)}catch(e){return null}},m=()=>{let e="ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789",r="HC";for(let t=0;t<8;t++)r+=e.charAt(Math.floor(Math.random()*e.length));return r},w=async e=>{let r=e.headers.get("authorization")?.replace("Bearer ","")||e.cookies.get("auth-token")?.value;if(!r)return{authenticated:!1,user:null};let t=p(r);if(!t)return{authenticated:!1,user:null};let a=await n.Gy.findByEmail(t.email);return a?{authenticated:!0,user:a}:{authenticated:!1,user:null}},f=async e=>{let r,a;if(await n.Gy.findByEmail(e.email))throw Error("User already exists with this email");if(e.referralCode){let t=await n.Gy.findByReferralId(e.referralCode);if(!t)throw Error("Invalid referral code");r=t.id}let s=await d(e.password),i=!1;do a=m(),i=!await n.Gy.findByReferralId(a);while(!i);let o=await n.Gy.create({email:e.email,firstName:e.firstName,lastName:e.lastName,password:s,referralId:a});if(r){let{placeUserByReferralType:a}=await t.e(2746).then(t.bind(t,2746)),s="general";"left"===e.placementSide?s="left":"right"===e.placementSide&&(s="right"),await a(r,o.id,s)}return{id:o.id,email:o.email,referralId:o.referralId,kycStatus:o.kycStatus}},h=async e=>{let r=await n.Gy.findByEmail(e.email);if(!r||!await l(e.password,r.password))throw Error("Invalid email or password");return{token:c({userId:r.id,email:r.email}),user:{id:r.id,email:r.email,referralId:r.referralId,kycStatus:r.kycStatus}}},y=e=>/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(e),x=async e=>{let r=await n.Gy.findById(e);return r?.role==="ADMIN"}},21820:e=>{e.exports=require("os")},27910:e=>{e.exports=require("stream")},28354:e=>{e.exports=require("util")},29021:e=>{e.exports=require("fs")},29294:e=>{e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:e=>{e.exports=require("path")},34631:e=>{e.exports=require("tls")},37366:e=>{e.exports=require("dns")},44870:e=>{e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},55494:(e,r,t)=>{t.r(r),t.d(r,{patchFetch:()=>h,routeModule:()=>p,serverHooks:()=>f,workAsyncStorage:()=>m,workUnitAsyncStorage:()=>w});var a={};t.r(a),t.d(a,{POST:()=>c});var s=t(96559),i=t(48088),n=t(37719),o=t(32190),u=t(12909),d=t(6710),l=t(31183);async function c(e){try{let{authenticated:r,user:a}=await (0,u.b9)(e);if(!r||!a)return o.NextResponse.json({success:!1,error:"Not authenticated"},{status:401});if(!await (0,u.qc)(a.id))return o.NextResponse.json({success:!1,error:"Admin access required"},{status:403});let{userId:s,action:i,rejectionReason:n}=await e.json();if(!s||!i||!["APPROVE","REJECT"].includes(i))return o.NextResponse.json({success:!1,error:"Invalid request parameters"},{status:400});if("REJECT"===i&&!n)return o.NextResponse.json({success:!1,error:"Rejection reason is required"},{status:400});let c=await l.prisma.kYCDocument.findMany({where:{userId:s,status:"PENDING"}});if(0===c.length)return o.NextResponse.json({success:!1,error:"No pending documents found for this user"},{status:404});let p="APPROVE"===i?"APPROVED":"REJECTED",m=new Date;await l.prisma.kYCDocument.updateMany({where:{userId:s,status:"PENDING"},data:{status:p,reviewedAt:m,reviewedBy:a.email,rejectionReason:"REJECT"===i?n:null}});let w=await l.prisma.user.update({where:{id:s},data:{kycStatus:p}});try{let{emailNotificationService:e}=await Promise.all([t.e(9526),t.e(3161)]).then(t.bind(t,13161));await e.sendKYCStatusNotification({userId:w.id,email:w.email,firstName:w.firstName,lastName:w.lastName,status:p,rejectionReason:"REJECT"===i?n:void 0})}catch(e){console.error("Error sending KYC status email:",e)}return await d.AJ.create({action:"KYC_REVIEWED",adminId:a.id,details:{reviewedUserId:s,action:i,rejectionReason:"REJECT"===i?n:null,documentsCount:c.length,reviewedBy:a.email},ipAddress:e.headers.get("x-forwarded-for")||"unknown",userAgent:e.headers.get("user-agent")||"unknown"}),o.NextResponse.json({success:!0,message:`KYC ${i.toLowerCase()}d successfully`,data:{userId:s,status:p,reviewedAt:m,reviewedBy:a.email}})}catch(e){return console.error("KYC review error:",e),o.NextResponse.json({success:!1,error:"Failed to review KYC documents"},{status:500})}}let p=new s.AppRouteRouteModule({definition:{kind:i.RouteKind.APP_ROUTE,page:"/api/admin/kyc/review/route",pathname:"/api/admin/kyc/review",filename:"route",bundlePath:"app/api/admin/kyc/review/route"},resolvedPagePath:"C:\\Users\\<USER>\\Desktop\\Hash_Minings\\hashcorex\\src\\app\\api\\admin\\kyc\\review\\route.ts",nextConfigOutput:"",userland:a}),{workAsyncStorage:m,workUnitAsyncStorage:w,serverHooks:f}=p;function h(){return(0,n.patchFetch)({workAsyncStorage:m,workUnitAsyncStorage:w})}},55511:e=>{e.exports=require("crypto")},55591:e=>{e.exports=require("https")},63033:e=>{e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},74075:e=>{e.exports=require("zlib")},79428:e=>{e.exports=require("buffer")},79551:e=>{e.exports=require("url")},79646:e=>{e.exports=require("child_process")},81630:e=>{e.exports=require("http")},91645:e=>{e.exports=require("net")},94735:e=>{e.exports=require("events")},96330:e=>{e.exports=require("@prisma/client")}};var r=require("../../../../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),a=r.X(0,[4447,580,5315,3306],()=>t(55494));module.exports=a})();