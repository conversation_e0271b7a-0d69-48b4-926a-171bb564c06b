exports.id=3988,exports.ids=[3988],exports.modules={2470:(e,t,r)=>{Promise.resolve().then(r.bind(r,57445))},4780:(e,t,r)=>{"use strict";r.d(t,{D1:()=>x,Oj:()=>u,Yq:()=>o,ZU:()=>h,ZV:()=>i,cn:()=>n,jI:()=>m,lW:()=>c,r6:()=>d,vv:()=>l});var s=r(49384),a=r(82348);function n(...e){return(0,a.QP)((0,s.$)(e))}function l(e,t="USD"){return new Intl.NumberFormat("en-US",{style:"currency",currency:t,minimumFractionDigits:2,maximumFractionDigits:2}).format(e)}function i(e,t=2){return new Intl.NumberFormat("en-US",{minimumFractionDigits:t,maximumFractionDigits:t}).format(e)}function o(e){let t="string"==typeof e?new Date(e):e;return new Intl.DateTimeFormat("en-US",{year:"numeric",month:"short",day:"numeric"}).format(t)}function d(e){let t="string"==typeof e?new Date(e):e;return new Intl.DateTimeFormat("en-US",{year:"numeric",month:"short",day:"numeric",hour:"2-digit",minute:"2-digit"}).format(t)}function c(e){if(navigator.clipboard)return navigator.clipboard.writeText(e);let t=document.createElement("textarea");t.value=e,document.body.appendChild(t),t.focus(),t.select();try{return document.execCommand("copy"),Promise.resolve()}catch(e){return Promise.reject(e)}finally{document.body.removeChild(t)}}function u(e){let t=[],r=[],s=e.length>=8;r.push({valid:s,message:"At least 8 characters long"}),s||t.push("Password must be at least 8 characters long");let a=/[A-Z]/.test(e);r.push({valid:a,message:"At least one uppercase letter"}),a||t.push("Password must contain at least one uppercase letter");let n=/[a-z]/.test(e);r.push({valid:n,message:"At least one lowercase letter"}),n||t.push("Password must contain at least one lowercase letter");let l=/\d/.test(e);r.push({valid:l,message:"At least one number"}),l||t.push("Password must contain at least one number");let i=/[!@#$%^&*(),.?":{}|<>]/.test(e);return r.push({valid:i,message:"At least one special character"}),i||t.push("Password must contain at least one special character"),{isValid:0===t.length,errors:t,checks:r}}function m(e){return e>=1e3?`${(e/1e3).toFixed(1)}K TH/s`:`${e.toFixed(2)} TH/s`}function h(){let e=new Date,t=new Date;t.setUTCDate(e.getUTCDate()+(6-e.getUTCDay())),t.setUTCHours(15,0,0,0),e>t&&t.setUTCDate(t.getUTCDate()+7);let r=t.getTime()-e.getTime(),s=Math.floor(r/864e5),a=Math.floor(r%864e5/36e5);return{days:s,hours:a,minutes:Math.floor(r%36e5/6e4),seconds:Math.floor(r%6e4/1e3)}}function x(){let e=new Date,t=new Date;t.setUTCDate(e.getUTCDate()+(6-e.getUTCDay())),t.setUTCHours(15,0,0,0),e>t&&t.setUTCDate(t.getUTCDate()+7);let r=t.getTime()-e.getTime(),s=Math.floor(r/864e5),a=Math.floor(r%864e5/36e5);return{days:s,hours:a,minutes:Math.floor(r%36e5/6e4),seconds:Math.floor(r%6e4/1e3)}}},26207:(e,t,r)=>{Promise.resolve().then(r.t.bind(r,16444,23)),Promise.resolve().then(r.t.bind(r,16042,23)),Promise.resolve().then(r.t.bind(r,88170,23)),Promise.resolve().then(r.t.bind(r,49477,23)),Promise.resolve().then(r.t.bind(r,29345,23)),Promise.resolve().then(r.t.bind(r,12089,23)),Promise.resolve().then(r.t.bind(r,46577,23)),Promise.resolve().then(r.t.bind(r,31307,23))},34014:(e,t,r)=>{Promise.resolve().then(r.bind(r,49567))},49567:(e,t,r)=>{"use strict";r.d(t,{AuthProvider:()=>a});var s=r(12907);let a=(0,s.registerClientReference)(function(){throw Error("Attempted to call AuthProvider() from the server but AuthProvider is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Desktop\\Hash_Minings\\hashcorex\\src\\hooks\\useAuth.tsx","AuthProvider");(0,s.registerClientReference)(function(){throw Error("Attempted to call useAuth() from the server but useAuth is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Desktop\\Hash_Minings\\hashcorex\\src\\hooks\\useAuth.tsx","useAuth")},57445:(e,t,r)=>{"use strict";r.d(t,{A:()=>i,AuthProvider:()=>l});var s=r(60687),a=r(43210);let n=(0,a.createContext)(void 0),l=({children:e})=>{let[t,r]=(0,a.useState)(null),[l,i]=(0,a.useState)(!0);(0,a.useEffect)(()=>{o()},[]);let o=async()=>{try{let e=await fetch("/api/auth/me",{credentials:"include"});if(e.ok){let t=await e.json();t.success&&r(t.data)}}catch(e){console.error("Auth check failed:",e)}finally{i(!1)}},d=async(e,t)=>{i(!0);try{let s=await fetch("/api/auth/login",{method:"POST",headers:{"Content-Type":"application/json"},credentials:"include",body:JSON.stringify({email:e,password:t})}),a=await s.json();if(!a.success)throw Error(a.error||"Login failed");r(a.data.user),setTimeout(()=>{o()},100)}catch(e){throw e}finally{i(!1)}},c=async(e,t,s,a,n,l,o,d)=>{i(!0);try{let i=o?`/api/auth/register?side=${o}`:"/api/auth/register",c=await fetch(i,{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({email:e,firstName:t,lastName:s,password:a,confirmPassword:n,referralCode:l,otp:d})}),u=await c.json();if(!u.success)throw Error(u.error||"Registration failed");r({id:u.data.user.id,email:u.data.user.email,firstName:u.data.user.firstName||"",lastName:u.data.user.lastName||"",referralId:u.data.user.referralId,role:u.data.user.role||"USER",kycStatus:u.data.user.kycStatus,isActive:u.data.user.isActive||!0,profilePicture:u.data.user.profilePicture||null,createdAt:u.data.user.createdAt,updatedAt:u.data.user.updatedAt})}catch(e){throw e}finally{i(!1)}},u=async()=>{try{await fetch("/api/auth/logout",{method:"POST",credentials:"include"})}catch(e){console.error("Logout error:",e)}finally{r(null)}},m=async()=>{await o()};return(0,s.jsx)(n.Provider,{value:{user:t,loading:l,login:d,register:c,logout:u,refreshUser:m},children:e})},i=()=>{let e=(0,a.useContext)(n);if(void 0===e)throw Error("useAuth must be used within an AuthProvider");return e}},61135:()=>{},70440:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>a});var s=r(31658);let a=async e=>[{type:"image/x-icon",sizes:"16x16",url:(0,s.fillMetadataSegment)(".",await e.params,"favicon.ico")+""}]},77849:(e,t,r)=>{"use strict";r.d(t,{$n:()=>d,Zp:()=>c,Wu:()=>h,aR:()=>u,ZB:()=>m,pd:()=>x,Rh:()=>b,aF:()=>p,vp:()=>U,G_:()=>N,eC:()=>k});var s=r(60687),a=r(43210),n=r.n(a),l=r(24224),i=r(4780);let o=(0,l.F)("inline-flex items-center justify-center rounded-xl font-semibold focus:outline-none focus:ring-2 focus:ring-offset-2 disabled:opacity-50 disabled:pointer-events-none shadow-lg",{variants:{variant:{primary:"bg-yellow-500 text-white focus:ring-yellow-500",secondary:"bg-gray-200 text-gray-800 focus:ring-gray-500",success:"bg-emerald-500 text-white focus:ring-emerald-500",danger:"bg-red-500 text-white focus:ring-red-500",warning:"bg-yellow-500 text-white focus:ring-yellow-500",destructive:"bg-red-600 text-white focus:ring-red-500",outline:"border-2 border-yellow-500 bg-transparent text-yellow-600 focus:ring-yellow-500",ghost:"text-gray-600 focus:ring-yellow-500 rounded-lg",link:"text-yellow-600 underline-offset-4 focus:ring-yellow-500",premium:"bg-slate-800 text-white focus:ring-slate-500",glass:"glass-morphism text-slate-900 backdrop-blur-xl border border-white/20"},size:{sm:"h-10 px-4 text-sm rounded-lg",md:"h-12 px-6 text-base rounded-xl",lg:"h-14 px-8 text-lg rounded-xl",xl:"h-16 px-10 text-xl rounded-2xl font-bold",icon:"h-12 w-12 rounded-xl"}},defaultVariants:{variant:"primary",size:"md"}}),d=n().forwardRef(({className:e,variant:t,size:r,loading:a,leftIcon:n,rightIcon:l,children:d,disabled:c,...u},m)=>(0,s.jsxs)("button",{className:(0,i.cn)(o({variant:t,size:r,className:e})),ref:m,disabled:c||a,...u,children:[a&&(0,s.jsx)("div",{className:"mr-2",children:(0,s.jsx)("div",{className:"spinner"})}),n&&!a&&(0,s.jsx)("span",{className:"mr-2",children:n}),d,l&&!a&&(0,s.jsx)("span",{className:"ml-2",children:l})]}));d.displayName="Button";let c=n().forwardRef(({className:e,children:t,...r},a)=>(0,s.jsx)("div",{ref:a,className:(0,i.cn)("rounded-2xl border border-gray-200/50 bg-white/90 backdrop-blur-xl shadow-xl overflow-hidden",e),...r,children:t}));c.displayName="Card";let u=n().forwardRef(({className:e,children:t,...r},a)=>(0,s.jsx)("div",{ref:a,className:(0,i.cn)("flex flex-col space-y-1.5 p-6 pb-4",e),...r,children:t}));u.displayName="CardHeader";let m=n().forwardRef(({className:e,children:t,...r},a)=>(0,s.jsx)("h3",{ref:a,className:(0,i.cn)("text-xl font-semibold leading-none tracking-tight text-dark-900",e),...r,children:t}));m.displayName="CardTitle",n().forwardRef(({className:e,children:t,...r},a)=>(0,s.jsx)("p",{ref:a,className:(0,i.cn)("text-sm text-gray-500",e),...r,children:t})).displayName="CardDescription";let h=n().forwardRef(({className:e,children:t,...r},a)=>(0,s.jsx)("div",{ref:a,className:(0,i.cn)("p-6 pt-0",e),...r,children:t}));h.displayName="CardContent",n().forwardRef(({className:e,children:t,...r},a)=>(0,s.jsx)("div",{ref:a,className:(0,i.cn)("flex items-center p-6 pt-0",e),...r,children:t})).displayName="CardFooter";let x=n().forwardRef(({className:e,type:t,label:r,error:a,leftIcon:n,rightIcon:l,...o},d)=>(0,s.jsxs)("div",{className:"w-full",children:[r&&(0,s.jsx)("label",{className:"block text-sm font-medium text-dark-700 mb-2",children:r}),(0,s.jsxs)("div",{className:"relative",children:[n&&(0,s.jsx)("div",{className:"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none",children:(0,s.jsx)("span",{className:"text-gray-400",children:n})}),(0,s.jsx)("input",{type:t,className:(0,i.cn)("flex h-14 w-full rounded-xl border-2 border-gray-200 bg-white/90 backdrop-blur-sm px-4 py-4 text-base shadow-inner focus:shadow-lg placeholder:text-gray-400 focus:outline-none focus:ring-2 focus:ring-solar-500 focus:border-solar-500 disabled:cursor-not-allowed disabled:opacity-50 transition-all duration-300 hover:border-gray-300",n&&"pl-12",l&&"pr-12",a&&"border-red-500 focus:ring-red-500 focus:border-red-500",e),ref:d,...o}),l&&(0,s.jsx)("div",{className:"absolute inset-y-0 right-0 pr-3 flex items-center",children:(0,s.jsx)("span",{className:"text-gray-400",children:l})})]}),a&&(0,s.jsx)("p",{className:"mt-1 text-sm text-red-600",children:a})]}));x.displayName="Input";var f=r(51215),g=r(11860);let p=({isOpen:e,onClose:t,title:r,children:n,size:l="md",showCloseButton:o=!0,darkMode:c=!1})=>{if((0,a.useEffect)(()=>(e?document.body.style.overflow="hidden":document.body.style.overflow="unset",()=>{document.body.style.overflow="unset"}),[e]),(0,a.useEffect)(()=>{let r=e=>{"Escape"===e.key&&t()};return e&&document.addEventListener("keydown",r),()=>{document.removeEventListener("keydown",r)}},[e,t]),!e)return null;let u=(0,s.jsxs)("div",{className:"fixed inset-0 z-50 flex items-center justify-center p-4",children:[(0,s.jsx)("div",{className:"fixed inset-0 bg-black bg-opacity-50 transition-opacity",onClick:t}),(0,s.jsxs)("div",{className:(0,i.cn)("relative w-full rounded-xl shadow-xl transform transition-all",c?"bg-slate-800":"bg-white",{sm:"max-w-md",md:"max-w-lg",lg:"max-w-2xl",xl:"max-w-4xl"}[l]),onClick:e=>e.stopPropagation(),children:[(0,s.jsxs)("div",{className:(0,i.cn)("flex items-center justify-between p-6 border-b",c?"border-slate-700":"border-gray-200"),children:[(0,s.jsx)("h2",{className:(0,i.cn)("text-xl font-semibold",c?"text-white":"text-dark-900"),children:r}),o&&(0,s.jsx)(d,{variant:"ghost",size:"icon",onClick:t,className:"h-8 w-8 rounded-full",children:(0,s.jsx)(g.A,{className:"h-4 w-4"})})]}),(0,s.jsx)("div",{className:"p-6",children:n})]})]});return(0,f.createPortal)(u,document.body)},b=({size:e="md",className:t,text:r})=>(0,s.jsxs)("div",{className:(0,i.cn)("flex flex-col items-center justify-center",t),children:[(0,s.jsx)("div",{className:(0,i.cn)("animate-spin rounded-full border-2 border-gray-300 border-t-solar-500",{sm:"h-4 w-4",md:"h-8 w-8",lg:"h-12 w-12"}[e])}),r&&(0,s.jsx)("p",{className:"mt-2 text-sm text-gray-600",children:r})]});var w=r(43649),y=r(5336),v=r(96882);let j=({isOpen:e,onClose:t,onConfirm:r,title:a,message:n,confirmText:l="Confirm",cancelText:o="Cancel",variant:c="default",darkMode:u=!1,loading:m=!1})=>{if(!e)return null;let h=(0,s.jsxs)("div",{className:"fixed inset-0 z-50 flex items-center justify-center p-4",children:[(0,s.jsx)("div",{className:"absolute inset-0 bg-black bg-opacity-50 transition-opacity",onClick:t}),(0,s.jsxs)("div",{className:(0,i.cn)("relative w-full max-w-md rounded-xl shadow-xl transform transition-all",u?"bg-slate-800 border border-slate-700":"bg-white"),onClick:e=>e.stopPropagation(),children:[(0,s.jsxs)("div",{className:(0,i.cn)("flex items-center justify-between p-6 border-b",u?"border-slate-700":"border-gray-200"),children:[(0,s.jsxs)("div",{className:"flex items-center space-x-3",children:[(()=>{switch(c){case"danger":return(0,s.jsx)(w.A,{className:"h-6 w-6 text-red-500"});case"warning":return(0,s.jsx)(w.A,{className:"h-6 w-6 text-yellow-500"});case"success":return(0,s.jsx)(y.A,{className:"h-6 w-6 text-green-500"});default:return(0,s.jsx)(v.A,{className:"h-6 w-6 text-blue-500"})}})(),(0,s.jsx)("h2",{className:(0,i.cn)("text-lg font-semibold",u?"text-white":"text-gray-900"),children:a})]}),(0,s.jsx)(d,{variant:"ghost",size:"icon",onClick:t,disabled:m,className:"h-8 w-8 rounded-full",children:(0,s.jsx)(g.A,{className:"h-4 w-4"})})]}),(0,s.jsx)("div",{className:"p-6",children:"string"==typeof n?(0,s.jsx)("p",{className:(0,i.cn)("text-sm leading-relaxed",u?"text-slate-300":"text-gray-600"),children:n}):(0,s.jsx)("div",{className:(0,i.cn)("text-sm leading-relaxed",u?"text-slate-300":"text-gray-600"),children:n})}),(0,s.jsxs)("div",{className:(0,i.cn)("flex items-center justify-end space-x-3 p-6 border-t",u?"border-slate-700":"border-gray-200"),children:[(0,s.jsx)(d,{variant:"outline",onClick:t,disabled:m,className:(0,i.cn)(u?"border-slate-600 text-slate-300 hover:bg-slate-700 hover:text-white":""),children:o}),(0,s.jsx)(d,{variant:(()=>{switch(c){case"danger":return"destructive";case"warning":return"warning";default:return"default"}})(),onClick:r,disabled:m,className:(0,i.cn)(m&&"opacity-50 cursor-not-allowed"),children:m?"Processing...":l})]})]})]});return(0,f.createPortal)(h,document.body)},N=()=>{let[e,t]=n().useState({isOpen:!1,title:"",message:"",onConfirm:()=>{}}),[r,a]=n().useState(!1),l=()=>{r||t(e=>({...e,isOpen:!1}))};return{showConfirm:e=>{t({isOpen:!0,...e,onConfirm:async()=>{a(!0);try{await e.onConfirm(),t(e=>({...e,isOpen:!1}))}catch(e){console.error("Confirm action failed:",e)}finally{a(!1)}}})},hideConfirm:l,ConfirmDialog:()=>(0,s.jsx)(j,{isOpen:e.isOpen,onClose:l,onConfirm:e.onConfirm,title:e.title,message:e.message,variant:e.variant,confirmText:e.confirmText,cancelText:e.cancelText,darkMode:e.darkMode,loading:r}),loading:r}};var C=r(93613);let P=({isOpen:e,onClose:t,title:r,message:a,variant:n="info",darkMode:l=!1,showCloseButton:o=!0,buttonText:c="OK",size:u="md"})=>{if(!e)return null;let m=(0,s.jsxs)("div",{className:"fixed inset-0 z-50 flex items-center justify-center p-4",onClick:t,children:[(0,s.jsx)("div",{className:"absolute inset-0 bg-black/50 backdrop-blur-sm"}),(0,s.jsxs)("div",{className:(0,i.cn)("relative w-full rounded-xl shadow-xl transform transition-all",l?"bg-slate-800 border border-slate-700":"bg-white",{sm:"max-w-sm",md:"max-w-md",lg:"max-w-lg"}[u]),onClick:e=>e.stopPropagation(),children:[(0,s.jsxs)("div",{className:(0,i.cn)("flex items-center justify-between p-6 border-b",l?"border-slate-700":"border-gray-200"),children:[(0,s.jsxs)("div",{className:"flex items-center space-x-3",children:[(()=>{switch(n){case"error":return(0,s.jsx)(C.A,{className:"h-6 w-6 text-red-500"});case"warning":return(0,s.jsx)(w.A,{className:"h-6 w-6 text-yellow-500"});case"success":return(0,s.jsx)(y.A,{className:"h-6 w-6 text-green-500"});default:return(0,s.jsx)(v.A,{className:"h-6 w-6 text-blue-500"})}})(),(0,s.jsx)("h2",{className:(0,i.cn)("text-lg font-semibold",l?"text-white":"text-gray-900"),children:r})]}),o&&(0,s.jsx)(d,{variant:"ghost",size:"icon",onClick:t,className:"h-8 w-8 rounded-full",children:(0,s.jsx)(g.A,{className:"h-4 w-4"})})]}),(0,s.jsx)("div",{className:"p-6",children:"string"==typeof a?(0,s.jsx)("p",{className:(0,i.cn)("text-sm leading-relaxed",l?"text-slate-300":"text-gray-600"),children:a}):(0,s.jsx)("div",{className:(0,i.cn)("text-sm leading-relaxed",l?"text-slate-300":"text-gray-600"),children:a})}),(0,s.jsx)("div",{className:(0,i.cn)("flex items-center justify-end p-6 border-t",l?"border-slate-700":"border-gray-200"),children:(0,s.jsx)(d,{variant:(()=>{switch(n){case"error":return"danger";case"warning":return"warning";case"success":return"success";default:return"primary"}})(),onClick:t,className:"min-w-[80px]",children:c})})]})]});return(0,f.createPortal)(m,document.body)},k=()=>{let[e,t]=n().useState({isOpen:!1,title:"",message:""}),r=()=>{t(e=>({...e,isOpen:!1}))};return{showMessage:e=>{t({isOpen:!0,...e})},hideMessage:r,MessageBoxComponent:()=>(0,s.jsx)(P,{...e,onClose:r})}};var A=r(58869),D=r(51361),T=r(16023),S=r(30474);let U=({currentPicture:e,onUpload:t,onRemove:r,loading:n=!1,disabled:l=!1})=>{let[i,o]=(0,a.useState)(null),[c,u]=(0,a.useState)(!1),m=(0,a.useRef)(null),h=e=>{if(!e)return;if(!e.type.startsWith("image/"))return void alert("Please select an image file");if(e.size>5242880)return void alert("File size must be less than 5MB");let r=new FileReader;r.onload=e=>{o(e.target?.result)},r.readAsDataURL(e),t(e)},x=i||e;return(0,s.jsxs)("div",{className:"flex flex-col items-center space-y-4",children:[(0,s.jsxs)("div",{className:"relative",children:[(0,s.jsx)("div",{className:"w-32 h-32 rounded-full overflow-hidden border-4 border-gray-200 bg-gray-100 flex items-center justify-center",children:x?(0,s.jsx)(S.default,{src:x,alt:"Profile Picture",width:128,height:128,className:"w-full h-full object-cover"}):(0,s.jsx)(A.A,{className:"w-16 h-16 text-gray-400"})}),n&&(0,s.jsx)("div",{className:"absolute inset-0 bg-black bg-opacity-50 rounded-full flex items-center justify-center",children:(0,s.jsx)("div",{className:"animate-spin rounded-full h-8 w-8 border-b-2 border-white"})}),x&&!n&&(0,s.jsx)("button",{onClick:()=>{o(null),r&&r()},disabled:l,className:"absolute -top-2 -right-2 bg-red-500 text-white rounded-full p-1 hover:bg-red-600 transition-colors",children:(0,s.jsx)(g.A,{className:"w-4 h-4"})})]}),(0,s.jsx)("div",{className:`border-2 border-dashed rounded-lg p-6 text-center transition-colors ${c?"border-blue-400 bg-blue-50":"border-gray-300 hover:border-gray-400"} ${l?"opacity-50 cursor-not-allowed":"cursor-pointer"}`,onDrop:e=>{e.preventDefault(),u(!1);let t=e.dataTransfer.files[0];t&&h(t)},onDragOver:e=>{e.preventDefault(),u(!0)},onDragLeave:e=>{e.preventDefault(),u(!1)},onClick:()=>!l&&m.current?.click(),children:(0,s.jsxs)("div",{className:"flex flex-col items-center space-y-2",children:[(0,s.jsx)(D.A,{className:"w-8 h-8 text-gray-400"}),(0,s.jsxs)("div",{children:[(0,s.jsx)("p",{className:"text-sm font-medium text-gray-700",children:"Click to upload or drag and drop"}),(0,s.jsx)("p",{className:"text-xs text-gray-500",children:"PNG, JPG, GIF up to 5MB"})]})]})}),(0,s.jsx)("input",{ref:m,type:"file",accept:"image/*",onChange:e=>{let t=e.target.files?.[0];t&&h(t)},className:"hidden",disabled:l}),(0,s.jsxs)(d,{onClick:()=>m.current?.click(),disabled:l||n,variant:"outline",size:"sm",className:"flex items-center space-x-2",children:[(0,s.jsx)(T.A,{className:"w-4 h-4"}),(0,s.jsx)("span",{children:"Choose File"})]})]})}},86455:(e,t,r)=>{Promise.resolve().then(r.t.bind(r,86346,23)),Promise.resolve().then(r.t.bind(r,27924,23)),Promise.resolve().then(r.t.bind(r,35656,23)),Promise.resolve().then(r.t.bind(r,40099,23)),Promise.resolve().then(r.t.bind(r,38243,23)),Promise.resolve().then(r.t.bind(r,28827,23)),Promise.resolve().then(r.t.bind(r,62763,23)),Promise.resolve().then(r.t.bind(r,97173,23))},94431:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>o,metadata:()=>i});var s=r(37413),a=r(25091),n=r.n(a);r(61135);var l=r(49567);let i={title:"HashCoreX - Solar-Powered Cloud Mining",description:"Sustainable cryptocurrency mining with solar energy. Earn daily ROI with our eco-friendly mining platform."};function o({children:e}){return(0,s.jsx)("html",{lang:"en",children:(0,s.jsx)("body",{className:`${n().variable} font-sans antialiased`,children:(0,s.jsx)(l.AuthProvider,{children:e})})})}}};