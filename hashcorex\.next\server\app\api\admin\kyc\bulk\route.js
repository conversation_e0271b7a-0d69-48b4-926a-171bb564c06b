"use strict";(()=>{var e={};e.id=868,e.ids=[868],e.modules={3295:e=>{e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},12909:(e,r,t)=>{t.d(r,{DT:()=>w,DY:()=>h,HU:()=>c,Lx:()=>y,b9:()=>m,qc:()=>k});var a=t(85663),s=t(43205),i=t.n(s),n=t(6710);let o=process.env.JWT_SECRET||"fallback-secret-key",u=process.env.JWT_EXPIRES_IN||"30d",l=async e=>await a.Ay.hash(e,12),d=async(e,r)=>await a.Ay.compare(e,r),c=e=>i().sign(e,o,{expiresIn:u}),p=e=>{try{return i().verify(e,o)}catch(e){return null}},f=()=>{let e="ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789",r="HC";for(let t=0;t<8;t++)r+=e.charAt(Math.floor(Math.random()*e.length));return r},m=async e=>{let r=e.headers.get("authorization")?.replace("Bearer ","")||e.cookies.get("auth-token")?.value;if(!r)return{authenticated:!1,user:null};let t=p(r);if(!t)return{authenticated:!1,user:null};let a=await n.Gy.findByEmail(t.email);return a?{authenticated:!0,user:a}:{authenticated:!1,user:null}},h=async e=>{let r,a;if(await n.Gy.findByEmail(e.email))throw Error("User already exists with this email");if(e.referralCode){let t=await n.Gy.findByReferralId(e.referralCode);if(!t)throw Error("Invalid referral code");r=t.id}let s=await l(e.password),i=!1;do a=f(),i=!await n.Gy.findByReferralId(a);while(!i);let o=await n.Gy.create({email:e.email,firstName:e.firstName,lastName:e.lastName,password:s,referralId:a});if(r){let{placeUserByReferralType:a}=await t.e(2746).then(t.bind(t,2746)),s="general";"left"===e.placementSide?s="left":"right"===e.placementSide&&(s="right"),await a(r,o.id,s)}return{id:o.id,email:o.email,referralId:o.referralId,kycStatus:o.kycStatus}},y=async e=>{let r=await n.Gy.findByEmail(e.email);if(!r||!await d(e.password,r.password))throw Error("Invalid email or password");return{token:c({userId:r.id,email:r.email}),user:{id:r.id,email:r.email,referralId:r.referralId,kycStatus:r.kycStatus}}},w=e=>/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(e),k=async e=>{let r=await n.Gy.findById(e);return r?.role==="ADMIN"}},27910:e=>{e.exports=require("stream")},28354:e=>{e.exports=require("util")},29294:e=>{e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},44870:e=>{e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},55511:e=>{e.exports=require("crypto")},63033:e=>{e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},79428:e=>{e.exports=require("buffer")},90742:(e,r,t)=>{t.r(r),t.d(r,{patchFetch:()=>y,routeModule:()=>p,serverHooks:()=>h,workAsyncStorage:()=>f,workUnitAsyncStorage:()=>m});var a={};t.r(a),t.d(a,{POST:()=>c});var s=t(96559),i=t(48088),n=t(37719),o=t(32190),u=t(12909),l=t(31183),d=t(6710);async function c(e){try{let{authenticated:r,user:t}=await (0,u.b9)(e);if(!r||!t)return o.NextResponse.json({success:!1,error:"Not authenticated"},{status:401});if(!await (0,u.qc)(t.id))return o.NextResponse.json({success:!1,error:"Admin access required"},{status:403});let{userIds:a,action:s,rejectionReason:i}=await e.json();if(!a||!Array.isArray(a)||0===a.length)return o.NextResponse.json({success:!1,error:"User IDs array is required"},{status:400});if(!s||!["APPROVE","REJECT"].includes(s))return o.NextResponse.json({success:!1,error:"Invalid action"},{status:400});if("REJECT"===s&&!i)return o.NextResponse.json({success:!1,error:"Rejection reason is required"},{status:400});let n="APPROVE"===s?"APPROVED":"REJECTED",c=new Date,p=[];for(let r of a)try{let a=await l.prisma.kYCDocument.findMany({where:{userId:r,status:"PENDING"}});if(0===a.length){p.push({userId:r,success:!1,error:"No pending documents found"});continue}await l.prisma.kYCDocument.updateMany({where:{userId:r,status:"PENDING"},data:{status:n,reviewedAt:c,reviewedBy:t.email,rejectionReason:"REJECT"===s?i:null}}),await l.prisma.user.update({where:{id:r},data:{kycStatus:n}}),await d.AJ.create({action:"KYC_BULK_REVIEWED",adminId:t.id,details:{reviewedUserId:r,action:s,rejectionReason:"REJECT"===s?i:null,documentsCount:a.length,reviewedBy:t.email,bulkOperation:!0},ipAddress:e.headers.get("x-forwarded-for")||"unknown",userAgent:e.headers.get("user-agent")||"unknown"}),p.push({userId:r,success:!0,status:n})}catch(e){console.error(`Error processing user ${r}:`,e),p.push({userId:r,success:!1,error:"Processing failed"})}let f=p.filter(e=>e.success).length,m=p.filter(e=>!e.success).length;return o.NextResponse.json({success:!0,message:`Bulk operation completed. ${f} successful, ${m} failed.`,data:{action:s,totalProcessed:a.length,successCount:f,failureCount:m,results:p}})}catch(e){return console.error("Bulk KYC operation error:",e),o.NextResponse.json({success:!1,error:"Failed to perform bulk operation"},{status:500})}}let p=new s.AppRouteRouteModule({definition:{kind:i.RouteKind.APP_ROUTE,page:"/api/admin/kyc/bulk/route",pathname:"/api/admin/kyc/bulk",filename:"route",bundlePath:"app/api/admin/kyc/bulk/route"},resolvedPagePath:"C:\\Users\\<USER>\\Desktop\\Hash_Minings\\hashcorex\\src\\app\\api\\admin\\kyc\\bulk\\route.ts",nextConfigOutput:"",userland:a}),{workAsyncStorage:f,workUnitAsyncStorage:m,serverHooks:h}=p;function y(){return(0,n.patchFetch)({workAsyncStorage:f,workUnitAsyncStorage:m})}},96330:e=>{e.exports=require("@prisma/client")}};var r=require("../../../../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),a=r.X(0,[4447,580,5315,3306],()=>t(90742));module.exports=a})();