"use strict";(()=>{var e={};e.id=1956,e.ids=[1956],e.modules={3295:e=>{e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},12909:(e,r,t)=>{t.d(r,{DT:()=>h,DY:()=>y,HU:()=>p,Lx:()=>w,b9:()=>m,qc:()=>x});var s=t(85663),i=t(43205),a=t.n(i),o=t(6710);let n=process.env.JWT_SECRET||"fallback-secret-key",u=process.env.JWT_EXPIRES_IN||"30d",l=async e=>await s.Ay.hash(e,12),c=async(e,r)=>await s.Ay.compare(e,r),p=e=>a().sign(e,n,{expiresIn:u}),d=e=>{try{return a().verify(e,n)}catch(e){return null}},f=()=>{let e="ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789",r="HC";for(let t=0;t<8;t++)r+=e.charAt(Math.floor(Math.random()*e.length));return r},m=async e=>{let r=e.headers.get("authorization")?.replace("Bearer ","")||e.cookies.get("auth-token")?.value;if(!r)return{authenticated:!1,user:null};let t=d(r);if(!t)return{authenticated:!1,user:null};let s=await o.Gy.findByEmail(t.email);return s?{authenticated:!0,user:s}:{authenticated:!1,user:null}},y=async e=>{let r,s;if(await o.Gy.findByEmail(e.email))throw Error("User already exists with this email");if(e.referralCode){let t=await o.Gy.findByReferralId(e.referralCode);if(!t)throw Error("Invalid referral code");r=t.id}let i=await l(e.password),a=!1;do s=f(),a=!await o.Gy.findByReferralId(s);while(!a);let n=await o.Gy.create({email:e.email,firstName:e.firstName,lastName:e.lastName,password:i,referralId:s});if(r){let{placeUserByReferralType:s}=await t.e(2746).then(t.bind(t,2746)),i="general";"left"===e.placementSide?i="left":"right"===e.placementSide&&(i="right"),await s(r,n.id,i)}return{id:n.id,email:n.email,referralId:n.referralId,kycStatus:n.kycStatus}},w=async e=>{let r=await o.Gy.findByEmail(e.email);if(!r||!await c(e.password,r.password))throw Error("Invalid email or password");return{token:p({userId:r.id,email:r.email}),user:{id:r.id,email:r.email,referralId:r.referralId,kycStatus:r.kycStatus}}},h=e=>/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(e),x=async e=>{let r=await o.Gy.findById(e);return r?.role==="ADMIN"}},23870:(e,r,t)=>{t.d(r,{A:()=>u});var s=t(55511);let i={randomUUID:s.randomUUID},a=new Uint8Array(256),o=a.length,n=[];for(let e=0;e<256;++e)n.push((e+256).toString(16).slice(1));let u=function(e,r,t){if(i.randomUUID&&!r&&!e)return i.randomUUID();let u=(e=e||{}).random??e.rng?.()??(o>a.length-16&&((0,s.randomFillSync)(a),o=0),a.slice(o,o+=16));if(u.length<16)throw Error("Random bytes length must be >= 16");if(u[6]=15&u[6]|64,u[8]=63&u[8]|128,r){if((t=t||0)<0||t+16>r.length)throw RangeError(`UUID byte range ${t}:${t+15} is out of buffer bounds`);for(let e=0;e<16;++e)r[t+e]=u[e];return r}return function(e,r=0){return(n[e[r+0]]+n[e[r+1]]+n[e[r+2]]+n[e[r+3]]+"-"+n[e[r+4]]+n[e[r+5]]+"-"+n[e[r+6]]+n[e[r+7]]+"-"+n[e[r+8]]+n[e[r+9]]+"-"+n[e[r+10]]+n[e[r+11]]+n[e[r+12]]+n[e[r+13]]+n[e[r+14]]+n[e[r+15]]).toLowerCase()}(u)}},27910:e=>{e.exports=require("stream")},28354:e=>{e.exports=require("util")},29021:e=>{e.exports=require("fs")},29294:e=>{e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:e=>{e.exports=require("path")},44870:e=>{e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},55511:e=>{e.exports=require("crypto")},63033:e=>{e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},71055:(e,r,t)=>{t.r(r),t.d(r,{patchFetch:()=>v,routeModule:()=>w,serverHooks:()=>g,workAsyncStorage:()=>h,workUnitAsyncStorage:()=>x});var s={};t.r(s),t.d(s,{DELETE:()=>y,POST:()=>m});var i=t(96559),a=t(48088),o=t(37719),n=t(32190),u=t(12909),l=t(6710),c=t(79748),p=t(33873),d=t(23870),f=t(29021);async function m(e){try{let{authenticated:r,user:t}=await (0,u.b9)(e);if(!r||!t)return n.NextResponse.json({success:!1,error:"Unauthorized"},{status:401});let s=(await e.formData()).get("profilePicture");if(!s)return n.NextResponse.json({success:!1,error:"No file provided"},{status:400});if(!s.type.startsWith("image/"))return n.NextResponse.json({success:!1,error:"File must be an image"},{status:400});if(s.size>5242880)return n.NextResponse.json({success:!1,error:"File size must be less than 5MB"},{status:400});let i=(0,p.join)(process.cwd(),"public","uploads","profile-pictures");(0,f.existsSync)(i)||await (0,c.mkdir)(i,{recursive:!0});let a=s.name.split(".").pop(),o=`${t.id}_${(0,d.A)()}.${a}`,m=(0,p.join)(i,o),y=`/uploads/profile-pictures/${o}`,w=await l.Gy.findById(t.id);if(w?.profilePicture){let e=(0,p.join)(process.cwd(),"public",w.profilePicture);try{(0,f.existsSync)(e)&&await (0,c.unlink)(e)}catch(e){console.warn("Failed to delete old profile picture:",e)}}let h=await s.arrayBuffer(),x=Buffer.from(h);await (0,c.writeFile)(m,x);let g=await l.Gy.updateProfilePicture(t.id,y);return n.NextResponse.json({success:!0,message:"Profile picture updated successfully",data:{profilePicture:y,user:g}})}catch(e){return console.error("Profile picture upload error:",e),n.NextResponse.json({success:!1,error:"Failed to upload profile picture"},{status:500})}}async function y(e){try{let{authenticated:r,user:t}=await (0,u.b9)(e);if(!r||!t)return n.NextResponse.json({success:!1,error:"Unauthorized"},{status:401});let s=await l.Gy.findById(t.id);if(!s?.profilePicture)return n.NextResponse.json({success:!1,error:"No profile picture to remove"},{status:400});let i=(0,p.join)(process.cwd(),"public",s.profilePicture);try{(0,f.existsSync)(i)&&await (0,c.unlink)(i)}catch(e){console.warn("Failed to delete profile picture file:",e)}let a=await l.Gy.updateProfilePicture(t.id,null);return n.NextResponse.json({success:!0,message:"Profile picture removed successfully",data:{user:a}})}catch(e){return console.error("Profile picture removal error:",e),n.NextResponse.json({success:!1,error:"Failed to remove profile picture"},{status:500})}}let w=new i.AppRouteRouteModule({definition:{kind:a.RouteKind.APP_ROUTE,page:"/api/user/profile-picture/route",pathname:"/api/user/profile-picture",filename:"route",bundlePath:"app/api/user/profile-picture/route"},resolvedPagePath:"C:\\Users\\<USER>\\Desktop\\Hash_Minings\\hashcorex\\src\\app\\api\\user\\profile-picture\\route.ts",nextConfigOutput:"",userland:s}),{workAsyncStorage:h,workUnitAsyncStorage:x,serverHooks:g}=w;function v(){return(0,o.patchFetch)({workAsyncStorage:h,workUnitAsyncStorage:x})}},79428:e=>{e.exports=require("buffer")},79748:e=>{e.exports=require("fs/promises")},96330:e=>{e.exports=require("@prisma/client")}};var r=require("../../../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),s=r.X(0,[4447,580,5315,3306],()=>t(71055));module.exports=s})();