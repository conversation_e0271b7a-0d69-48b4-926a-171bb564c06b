"use strict";(()=>{var e={};e.id=6950,e.ids=[6950],e.modules={1304:(e,t,r)=>{r.r(t),r.d(t,{patchFetch:()=>y,routeModule:()=>d,serverHooks:()=>f,workAsyncStorage:()=>p,workUnitAsyncStorage:()=>m});var a={};r.r(a),r.d(a,{GET:()=>c});var s=r(96559),i=r(48088),n=r(37719),l=r(32190),o=r(12909),u=r(31183);async function c(e){try{let{authenticated:t,user:r}=await (0,o.b9)(e);if(!t||!r)return l.NextResponse.json({error:"Not authenticated"},{status:401});if(!await (0,o.qc)(r.id))return l.NextResponse.json({error:"Admin access required"},{status:403});let{searchParams:a}=new URL(e.url),s=parseInt(a.get("page")||"1"),i=parseInt(a.get("limit")||"20"),n=a.get("search")||"",c=a.get("status")||"all",d=a.get("sortBy")||"",p=a.get("sortOrder")||"desc",m={};n&&(m.OR=[{email:{contains:n,mode:"insensitive"}},{firstName:{contains:n,mode:"insensitive"}},{lastName:{contains:n,mode:"insensitive"}},{referralId:{contains:n,mode:"insensitive"}}]),"all"!==c&&(m.isActive="active"===c);let[f,y]=await Promise.all([u.prisma.user.findMany({where:m,select:{id:!0,email:!0,firstName:!0,lastName:!0,referralId:!0,role:!0,isActive:!0,kycStatus:!0,createdAt:!0,walletBalance:{select:{availableBalance:!0}},miningUnits:{select:{thsAmount:!0,status:!0,expiryDate:!0}},kycDocuments:{select:{id:!0,status:!0}},_count:{select:{miningUnits:!0,transactions:!0}}},orderBy:function(e,t){let r="asc"===t?"asc":"desc";switch(e){case"createdAt":return{createdAt:r};case"walletBalance":return{walletBalance:{availableBalance:r}};default:return{createdAt:"desc"}}}(d,p),skip:(s-1)*i,take:i}),u.prisma.user.count({where:m})]),h=Math.ceil(y/i),w=f.map(e=>{let t=e.miningUnits.reduce((e,t)=>{let r="ACTIVE"===t.status&&t.expiryDate>new Date;return e+(r?t.thsAmount:0)},0),r=e.kycStatus;"PENDING"===e.kycStatus&&0===e.kycDocuments.length&&(r="NOT_SUBMITTED");let{miningUnits:a,kycDocuments:s,...i}=e;return{...i,kycStatus:r,activeTHS:t}});return"activeTHS"===d&&w.sort((e,t)=>{let r=e.activeTHS-t.activeTHS;return"asc"===p?r:-r}),l.NextResponse.json({success:!0,data:{users:w,pagination:{currentPage:s,totalPages:h,totalCount:y,hasNext:s<h,hasPrev:s>1}}})}catch(e){return console.error("Admin users fetch error:",e),l.NextResponse.json({success:!1,error:"Failed to fetch users"},{status:500})}}let d=new s.AppRouteRouteModule({definition:{kind:i.RouteKind.APP_ROUTE,page:"/api/admin/users/route",pathname:"/api/admin/users",filename:"route",bundlePath:"app/api/admin/users/route"},resolvedPagePath:"C:\\Users\\<USER>\\Desktop\\Hash_Minings\\hashcorex\\src\\app\\api\\admin\\users\\route.ts",nextConfigOutput:"",userland:a}),{workAsyncStorage:p,workUnitAsyncStorage:m,serverHooks:f}=d;function y(){return(0,n.patchFetch)({workAsyncStorage:p,workUnitAsyncStorage:m})}},3295:e=>{e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},12909:(e,t,r)=>{r.d(t,{DT:()=>w,DY:()=>y,HU:()=>d,Lx:()=>h,b9:()=>f,qc:()=>x});var a=r(85663),s=r(43205),i=r.n(s),n=r(6710);let l=process.env.JWT_SECRET||"fallback-secret-key",o=process.env.JWT_EXPIRES_IN||"30d",u=async e=>await a.Ay.hash(e,12),c=async(e,t)=>await a.Ay.compare(e,t),d=e=>i().sign(e,l,{expiresIn:o}),p=e=>{try{return i().verify(e,l)}catch(e){return null}},m=()=>{let e="ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789",t="HC";for(let r=0;r<8;r++)t+=e.charAt(Math.floor(Math.random()*e.length));return t},f=async e=>{let t=e.headers.get("authorization")?.replace("Bearer ","")||e.cookies.get("auth-token")?.value;if(!t)return{authenticated:!1,user:null};let r=p(t);if(!r)return{authenticated:!1,user:null};let a=await n.Gy.findByEmail(r.email);return a?{authenticated:!0,user:a}:{authenticated:!1,user:null}},y=async e=>{let t,a;if(await n.Gy.findByEmail(e.email))throw Error("User already exists with this email");if(e.referralCode){let r=await n.Gy.findByReferralId(e.referralCode);if(!r)throw Error("Invalid referral code");t=r.id}let s=await u(e.password),i=!1;do a=m(),i=!await n.Gy.findByReferralId(a);while(!i);let l=await n.Gy.create({email:e.email,firstName:e.firstName,lastName:e.lastName,password:s,referralId:a});if(t){let{placeUserByReferralType:a}=await r.e(2746).then(r.bind(r,2746)),s="general";"left"===e.placementSide?s="left":"right"===e.placementSide&&(s="right"),await a(t,l.id,s)}return{id:l.id,email:l.email,referralId:l.referralId,kycStatus:l.kycStatus}},h=async e=>{let t=await n.Gy.findByEmail(e.email);if(!t||!await c(e.password,t.password))throw Error("Invalid email or password");return{token:d({userId:t.id,email:t.email}),user:{id:t.id,email:t.email,referralId:t.referralId,kycStatus:t.kycStatus}}},w=e=>/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(e),x=async e=>{let t=await n.Gy.findById(e);return t?.role==="ADMIN"}},27910:e=>{e.exports=require("stream")},28354:e=>{e.exports=require("util")},29294:e=>{e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},44870:e=>{e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},55511:e=>{e.exports=require("crypto")},63033:e=>{e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},79428:e=>{e.exports=require("buffer")},96330:e=>{e.exports=require("@prisma/client")}};var t=require("../../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),a=t.X(0,[4447,580,5315,3306],()=>r(1304));module.exports=a})();