"use strict";(()=>{var e={};e.id=1767,e.ids=[1767],e.modules={3295:e=>{e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},12909:(e,r,t)=>{t.d(r,{DT:()=>x,DY:()=>h,HU:()=>c,Lx:()=>f,b9:()=>m,qc:()=>y});var a=t(85663),s=t(43205),i=t.n(s),n=t(6710);let o=process.env.JWT_SECRET||"fallback-secret-key",d=process.env.JWT_EXPIRES_IN||"30d",u=async e=>await a.Ay.hash(e,12),l=async(e,r)=>await a.Ay.compare(e,r),c=e=>i().sign(e,o,{expiresIn:d}),p=e=>{try{return i().verify(e,o)}catch(e){return null}},w=()=>{let e="ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789",r="HC";for(let t=0;t<8;t++)r+=e.charAt(Math.floor(Math.random()*e.length));return r},m=async e=>{let r=e.headers.get("authorization")?.replace("Bearer ","")||e.cookies.get("auth-token")?.value;if(!r)return{authenticated:!1,user:null};let t=p(r);if(!t)return{authenticated:!1,user:null};let a=await n.Gy.findByEmail(t.email);return a?{authenticated:!0,user:a}:{authenticated:!1,user:null}},h=async e=>{let r,a;if(await n.Gy.findByEmail(e.email))throw Error("User already exists with this email");if(e.referralCode){let t=await n.Gy.findByReferralId(e.referralCode);if(!t)throw Error("Invalid referral code");r=t.id}let s=await u(e.password),i=!1;do a=w(),i=!await n.Gy.findByReferralId(a);while(!i);let o=await n.Gy.create({email:e.email,firstName:e.firstName,lastName:e.lastName,password:s,referralId:a});if(r){let{placeUserByReferralType:a}=await t.e(2746).then(t.bind(t,2746)),s="general";"left"===e.placementSide?s="left":"right"===e.placementSide&&(s="right"),await a(r,o.id,s)}return{id:o.id,email:o.email,referralId:o.referralId,kycStatus:o.kycStatus}},f=async e=>{let r=await n.Gy.findByEmail(e.email);if(!r||!await l(e.password,r.password))throw Error("Invalid email or password");return{token:c({userId:r.id,email:r.email}),user:{id:r.id,email:r.email,referralId:r.referralId,kycStatus:r.kycStatus}}},x=e=>/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(e),y=async e=>{let r=await n.Gy.findById(e);return r?.role==="ADMIN"}},21820:e=>{e.exports=require("os")},27910:e=>{e.exports=require("stream")},28354:e=>{e.exports=require("util")},29021:e=>{e.exports=require("fs")},29294:e=>{e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:e=>{e.exports=require("path")},34631:e=>{e.exports=require("tls")},37366:e=>{e.exports=require("dns")},44870:e=>{e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},55511:e=>{e.exports=require("crypto")},55591:e=>{e.exports=require("https")},63033:e=>{e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},65430:(e,r,t)=>{t.r(r),t.d(r,{patchFetch:()=>f,routeModule:()=>p,serverHooks:()=>h,workAsyncStorage:()=>w,workUnitAsyncStorage:()=>m});var a={};t.r(a),t.d(a,{POST:()=>c});var s=t(96559),i=t(48088),n=t(37719),o=t(32190),d=t(12909),u=t(31183),l=t(6710);async function c(e){try{let r,{authenticated:a,user:s}=await (0,d.b9)(e);if(!a||!s)return o.NextResponse.json({error:"Not authenticated"},{status:401});if(!await (0,d.qc)(s.id))return o.NextResponse.json({error:"Admin access required"},{status:403});let{withdrawalId:i,action:n,rejectionReason:c,transactionHash:p}=await e.json();if(!i||!n)return o.NextResponse.json({error:"Withdrawal ID and action are required"},{status:400});let w=await u.prisma.withdrawalRequest.findUnique({where:{id:i},include:{user:{select:{id:!0,email:!0,firstName:!0,lastName:!0}}}});if(!w)return o.NextResponse.json({error:"Withdrawal request not found"},{status:404});let m="",h={withdrawalId:i,userId:w.userId,amount:w.amount,userEmail:w.user.email};switch(n){case"APPROVE":r=await u.prisma.withdrawalRequest.update({where:{id:i},data:{status:"APPROVED",processedBy:s.id,processedAt:new Date}}),await u.prisma.transaction.updateMany({where:{reference:i,type:"WITHDRAWAL",status:"PENDING"},data:{status:"COMPLETED"}}),m="WITHDRAWAL_APPROVED";try{let{emailNotificationService:e}=await Promise.all([t.e(9526),t.e(3161)]).then(t.bind(t,13161));await e.sendWithdrawalStatusNotification({userId:w.userId,email:w.user.email,firstName:w.user.firstName,lastName:w.user.lastName,amount:w.amount,status:"APPROVED",usdtAddress:w.usdtAddress})}catch(e){console.error("Error sending withdrawal approval email:",e)}break;case"REJECT":if(!c)return o.NextResponse.json({error:"Rejection reason is required"},{status:400});let f=parseFloat(await l.rs.get("withdrawalFeeFixed")||"3"),x=parseFloat(await l.rs.get("withdrawalFeePercentage")||"1"),y=w.amount*x/100,E=w.amount+(f+y),A=await l.k_.getOrCreate(w.userId);await l.k_.updateBalance(w.userId,{availableBalance:A.availableBalance+E}),r=await u.prisma.withdrawalRequest.update({where:{id:i},data:{status:"REJECTED",processedBy:s.id,processedAt:new Date,rejectionReason:c}}),await u.prisma.transaction.updateMany({where:{reference:i,type:"WITHDRAWAL",status:"PENDING"},data:{status:"FAILED"}}),m="WITHDRAWAL_REJECTED",h.rejectionReason=c,h.restoredAmount=E;try{let{emailNotificationService:e}=await Promise.all([t.e(9526),t.e(3161)]).then(t.bind(t,13161));await e.sendWithdrawalStatusNotification({userId:w.userId,email:w.user.email,firstName:w.user.firstName,lastName:w.user.lastName,amount:w.amount,status:"REJECTED",rejectionReason:c,usdtAddress:w.usdtAddress})}catch(e){console.error("Error sending withdrawal rejection email:",e)}break;case"COMPLETE":if(!p)return o.NextResponse.json({error:"Transaction hash is required"},{status:400});r=await u.prisma.withdrawalRequest.update({where:{id:i},data:{status:"COMPLETED",processedBy:s.id,processedAt:new Date,txid:p}});let N=await u.prisma.transaction.findFirst({where:{reference:i,type:"WITHDRAWAL"}});N&&await u.prisma.transaction.update({where:{id:N.id},data:{status:"COMPLETED",description:`${N.description} - TX: ${p}`}}),m="WITHDRAWAL_COMPLETED",h.transactionHash=p;try{let{emailNotificationService:e}=await Promise.all([t.e(9526),t.e(3161)]).then(t.bind(t,13161));await e.sendWithdrawalStatusNotification({userId:w.userId,email:w.user.email,firstName:w.user.firstName,lastName:w.user.lastName,amount:w.amount,status:"COMPLETED",transactionHash:p,usdtAddress:w.usdtAddress})}catch(e){console.error("Error sending withdrawal completion email:",e)}break;default:return o.NextResponse.json({error:"Invalid action"},{status:400})}return await l.AJ.create({action:m,userId:s.id,details:h,ipAddress:e.headers.get("x-forwarded-for")||"unknown",userAgent:e.headers.get("user-agent")||"unknown"}),o.NextResponse.json({success:!0,message:`Withdrawal ${n.toLowerCase()}d successfully`,data:r})}catch(e){return console.error("Admin withdrawal action error:",e),o.NextResponse.json({success:!1,error:"Failed to perform withdrawal action"},{status:500})}}let p=new s.AppRouteRouteModule({definition:{kind:i.RouteKind.APP_ROUTE,page:"/api/admin/withdrawals/action/route",pathname:"/api/admin/withdrawals/action",filename:"route",bundlePath:"app/api/admin/withdrawals/action/route"},resolvedPagePath:"C:\\Users\\<USER>\\Desktop\\Hash_Minings\\hashcorex\\src\\app\\api\\admin\\withdrawals\\action\\route.ts",nextConfigOutput:"",userland:a}),{workAsyncStorage:w,workUnitAsyncStorage:m,serverHooks:h}=p;function f(){return(0,n.patchFetch)({workAsyncStorage:w,workUnitAsyncStorage:m})}},74075:e=>{e.exports=require("zlib")},79428:e=>{e.exports=require("buffer")},79551:e=>{e.exports=require("url")},79646:e=>{e.exports=require("child_process")},81630:e=>{e.exports=require("http")},91645:e=>{e.exports=require("net")},94735:e=>{e.exports=require("events")},96330:e=>{e.exports=require("@prisma/client")}};var r=require("../../../../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),a=r.X(0,[4447,580,5315,3306],()=>t(65430));module.exports=a})();