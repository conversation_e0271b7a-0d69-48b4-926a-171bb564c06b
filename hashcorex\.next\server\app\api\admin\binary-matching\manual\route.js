"use strict";(()=>{var e={};e.id=1068,e.ids=[1068],e.modules={3295:e=>{e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},12909:(e,r,a)=>{a.d(r,{DT:()=>g,DY:()=>h,HU:()=>c,Lx:()=>f,b9:()=>y,qc:()=>w});var t=a(85663),s=a(43205),i=a.n(s),n=a(6710);let o=process.env.JWT_SECRET||"fallback-secret-key",l=process.env.JWT_EXPIRES_IN||"30d",u=async e=>await t.Ay.hash(e,12),d=async(e,r)=>await t.Ay.compare(e,r),c=e=>i().sign(e,o,{expiresIn:l}),m=e=>{try{return i().verify(e,o)}catch(e){return null}},p=()=>{let e="ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789",r="HC";for(let a=0;a<8;a++)r+=e.charAt(Math.floor(Math.random()*e.length));return r},y=async e=>{let r=e.headers.get("authorization")?.replace("Bearer ","")||e.cookies.get("auth-token")?.value;if(!r)return{authenticated:!1,user:null};let a=m(r);if(!a)return{authenticated:!1,user:null};let t=await n.Gy.findByEmail(a.email);return t?{authenticated:!0,user:t}:{authenticated:!1,user:null}},h=async e=>{let r,t;if(await n.Gy.findByEmail(e.email))throw Error("User already exists with this email");if(e.referralCode){let a=await n.Gy.findByReferralId(e.referralCode);if(!a)throw Error("Invalid referral code");r=a.id}let s=await u(e.password),i=!1;do t=p(),i=!await n.Gy.findByReferralId(t);while(!i);let o=await n.Gy.create({email:e.email,firstName:e.firstName,lastName:e.lastName,password:s,referralId:t});if(r){let{placeUserByReferralType:t}=await a.e(2746).then(a.bind(a,2746)),s="general";"left"===e.placementSide?s="left":"right"===e.placementSide&&(s="right"),await t(r,o.id,s)}return{id:o.id,email:o.email,referralId:o.referralId,kycStatus:o.kycStatus}},f=async e=>{let r=await n.Gy.findByEmail(e.email);if(!r||!await d(e.password,r.password))throw Error("Invalid email or password");return{token:c({userId:r.id,email:r.email}),user:{id:r.id,email:r.email,referralId:r.referralId,kycStatus:r.kycStatus}}},g=e=>/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(e),w=async e=>{let r=await n.Gy.findById(e);return r?.role==="ADMIN"}},27910:e=>{e.exports=require("stream")},28354:e=>{e.exports=require("util")},29294:e=>{e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},37707:(e,r,a)=>{a.r(r),a.d(r,{patchFetch:()=>f,routeModule:()=>m,serverHooks:()=>h,workAsyncStorage:()=>p,workUnitAsyncStorage:()=>y});var t={};a.r(t),a.d(t,{POST:()=>c});var s=a(96559),i=a(48088),n=a(37719),o=a(32190),l=a(12909),u=a(2746),d=a(6710);async function c(e){try{let{authenticated:r,user:a}=await (0,l.b9)(e);if(!r||!a)return o.NextResponse.json({success:!1,error:"Authentication required"},{status:401});if("ADMIN"!==a.role)return o.NextResponse.json({success:!1,error:"Admin access required"},{status:403});console.log(`Manual binary matching initiated by admin: ${a.email}`);let t=await (0,u.E5)();return await d.AJ.create({action:"MANUAL_BINARY_MATCHING_TRIGGERED",details:{triggeredBy:a.email,adminId:a.id,result:t,timestamp:new Date().toISOString()}}),console.log(`Manual binary matching completed. Processed ${t.usersProcessed} users with total payouts: $${t.totalPayouts.toFixed(2)}`),o.NextResponse.json({success:!0,message:"Binary matching process completed successfully",data:{usersProcessed:t.usersProcessed,totalPayouts:t.totalPayouts,matchingResults:t.matchingResults}})}catch(r){console.error("Manual binary matching error:",r);try{let{user:a}=await (0,l.b9)(e);await d.AJ.create({action:"MANUAL_BINARY_MATCHING_ERROR",details:{triggeredBy:a?.email||"unknown",error:r.message,timestamp:new Date().toISOString()}})}catch(e){console.error("Failed to log manual binary matching error:",e)}return o.NextResponse.json({success:!1,error:"Failed to process binary matching"},{status:500})}}let m=new s.AppRouteRouteModule({definition:{kind:i.RouteKind.APP_ROUTE,page:"/api/admin/binary-matching/manual/route",pathname:"/api/admin/binary-matching/manual",filename:"route",bundlePath:"app/api/admin/binary-matching/manual/route"},resolvedPagePath:"C:\\Users\\<USER>\\Desktop\\Hash_Minings\\hashcorex\\src\\app\\api\\admin\\binary-matching\\manual\\route.ts",nextConfigOutput:"",userland:t}),{workAsyncStorage:p,workUnitAsyncStorage:y,serverHooks:h}=m;function f(){return(0,n.patchFetch)({workAsyncStorage:p,workUnitAsyncStorage:y})}},44870:e=>{e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},55511:e=>{e.exports=require("crypto")},63033:e=>{e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},79428:e=>{e.exports=require("buffer")},96330:e=>{e.exports=require("@prisma/client")}};var r=require("../../../../../webpack-runtime.js");r.C(e);var a=e=>r(r.s=e),t=r.X(0,[4447,580,5315,3306,2746],()=>a(37707));module.exports=t})();