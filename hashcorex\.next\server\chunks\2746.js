"use strict";exports.id=2746,exports.ids=[2746],exports.modules={2746:(e,r,t)=>{t.d(r,{$D:()=>y,E5:()=>N,OZ:()=>L,PM:()=>D,QS:()=>A,Yn:()=>B,fB:()=>S,g5:()=>G,gS:()=>E,gj:()=>M,l6:()=>b,lv:()=>F,oS:()=>U,placeUserByReferralType:()=>g});var a=t(31183),i=t(6710),n=t(39794);async function s(e){try{return await a.prisma.miningUnit.count({where:{userId:e,status:"ACTIVE",expiryDate:{gt:new Date}}})>0}catch(e){return console.error("Error checking active mining units:",e),!1}}async function o(e,r){try{return(await P(e,r)).length}catch(e){return console.error("Downline count calculation error:",e),0}}async function l(e){try{let r=await o(e,"LEFT"),t=await o(e,"RIGHT"),a=r<=t?"LEFT":"RIGHT",n=await I(e,a);if(n)return n;let s=await I(e,"LEFT"===a?"RIGHT":"LEFT");if(s)return s;let l=await i.cc.findByReferrerId(e),d=l.some(e=>"LEFT"===e.placementSide),c=l.some(e=>"RIGHT"===e.placementSide);if(!d)return{userId:e,side:"LEFT"};if(!c)return{userId:e,side:"RIGHT"};return{userId:e,side:a}}catch(r){return console.error("Optimal placement position error:",r),{userId:e,side:"LEFT"}}}async function d(e,r){try{let t=await l(e);await i.cc.create({referrerId:t.userId,referredId:r,placementSide:t.side});let n="LEFT"===t.side?{leftReferralId:r}:{rightReferralId:r};return await a.prisma.user.update({where:{id:t.userId},data:n}),await c(e,r),await v(e,t.userId),t.side}catch(e){throw console.error("Binary tree placement error:",e),e}}async function c(e,r){try{await a.prisma.user.update({where:{id:r},data:{referrerId:e}}),await a.prisma.user.update({where:{id:e},data:{directReferralCount:{increment:1},updatedAt:new Date}}),await a.prisma.referral.updateMany({where:{referrerId:e,referredId:r},data:{isDirectSponsor:!0}})}catch(e){console.error("Sponsor relationship creation error:",e)}}async function u(e){try{let r=await o(e,"LEFT"),t=await o(e,"RIGHT");await a.prisma.user.update({where:{id:e},data:{totalLeftDownline:r,totalRightDownline:t,lastTreeUpdate:new Date}})}catch(e){console.error("Update cached downline counts error:",e)}}async function f(e){try{let r=await a.prisma.user.findUnique({where:{id:e},select:{totalLeftDownline:!0,totalRightDownline:!0,lastTreeUpdate:!0}});if(!r)return{left:0,right:0,total:0};if((r.lastTreeUpdate?Date.now()-r.lastTreeUpdate.getTime():1/0)<18e5&&null!==r.totalLeftDownline&&null!==r.totalRightDownline)return{left:r.totalLeftDownline,right:r.totalRightDownline,total:r.totalLeftDownline+r.totalRightDownline};{let r=await o(e,"LEFT"),t=await o(e,"RIGHT");return u(e).catch(console.error),{left:r,right:t,total:r+t}}}catch(e){return console.error("Get cached downline counts error:",e),{left:0,right:0,total:0}}}async function m(e){try{let r=e,t=0;for(;t<20;){if(!await a.prisma.user.findUnique({where:{id:r},select:{id:!0}}))return{userId:e,side:"LEFT"};let n=await i.cc.findByReferrerId(r);if(!n.some(e=>"LEFT"===e.placementSide))break;let s=n.find(e=>"LEFT"===e.placementSide);if(!s)break;r=s.referredId,t++}return{userId:r,side:"LEFT"}}catch(r){return console.error("Find deepest left position error:",r),{userId:e,side:"LEFT"}}}async function h(e){try{let r=e,t=0;for(;t<20;){if(!await a.prisma.user.findUnique({where:{id:r},select:{id:!0}}))return{userId:e,side:"RIGHT"};let n=await i.cc.findByReferrerId(r);if(!n.some(e=>"RIGHT"===e.placementSide))break;let s=n.find(e=>"RIGHT"===e.placementSide);if(!s)break;r=s.referredId,t++}return{userId:r,side:"RIGHT"}}catch(r){return console.error("Find deepest right position error:",r),{userId:e,side:"RIGHT"}}}async function w(e,r){try{let t=await m(e);return await i.cc.create({referrerId:t.userId,referredId:r,placementSide:t.side}),await a.prisma.user.update({where:{id:t.userId},data:{leftReferralId:r}}),await c(e,r),await v(e,t.userId),t.side}catch(e){throw console.error("Left side only placement error:",e),e}}async function p(e,r){try{let t=await h(e);return await i.cc.create({referrerId:t.userId,referredId:r,placementSide:t.side}),await a.prisma.user.update({where:{id:t.userId},data:{rightReferralId:r}}),await c(e,r),await v(e,t.userId),t.side}catch(e){throw console.error("Right side only placement error:",e),e}}async function g(e,r,t){try{switch(t){case"left":return await w(e,r);case"right":return await p(e,r);default:return await d(e,r)}}catch(e){throw console.error("Placement by referral type error:",e),e}}async function I(e,r){try{let t=(await i.cc.findByReferrerId(e)).find(e=>e.placementSide===r);if(!t)return{userId:e,side:r};let a=[t.referredId];for(;a.length>0;){let e=a.shift(),r=await i.cc.findByReferrerId(e),t=r.some(e=>"LEFT"===e.placementSide),n=r.some(e=>"RIGHT"===e.placementSide);if(!t)return{userId:e,side:"LEFT"};if(!n)return{userId:e,side:"RIGHT"};r.forEach(e=>{a.push(e.referredId)})}return null}catch(e){return console.error("Find available spot error:",e),null}}async function y(e,r,t){try{if(!await s(e))return console.log(`Skipping direct referral bonus for inactive sponsor ${e} - no active mining units`),0;if(t){let r=await a.prisma.user.findUnique({where:{id:t},select:{hasReceivedFirstCommission:!0,firstName:!0,lastName:!0}});if(r?.hasReceivedFirstCommission)return console.log(`Skipping direct referral bonus - sponsor ${e} already received first commission from user ${t} (${r.firstName} ${r.lastName})`),0}let o=parseFloat(await i.rs.get("DIRECT_REFERRAL_BONUS")||"10"),l=r*o/100,d=await i.DR.create({userId:e,type:"DIRECT_REFERRAL",amount:l,description:`Direct referral bonus (${o}% of $${r}) - First purchase`,reference:t?`from_user:${t}`:"direct_referral",status:"COMPLETED"});try{let r=await (0,n.Py)(e,l,"DIRECT_REFERRAL",d.id,`Direct referral commission from ${t?"user purchase":"referral"}`);console.log(`Allocated ${l} referral bonus to ${r.length} mining units for sponsor ${e}`),await i.k_.addEarnings(e,l),console.log(`Added ${l} referral bonus to wallet balance for sponsor ${e}`)}catch(r){console.error(`Failed to allocate referral bonus to mining units for ${e}:`,r),await i.k_.addEarnings(e,l),console.log(`Fallback: Added ${l} referral bonus directly to wallet for sponsor ${e}`)}return await a.prisma.referral.updateMany({where:{referrerId:e,referred:{miningUnits:{some:{investmentAmount:r}}}},data:{commissionEarned:{increment:l}}}),t&&await a.prisma.user.update({where:{id:t},data:{hasReceivedFirstCommission:!0}}),console.log(`First-time direct referral bonus of $${l} awarded to active sponsor ${e} from user ${t}`),l}catch(e){throw console.error("Direct referral bonus error:",e),e}}async function E(e,r){try{let t=Math.round(r/100*100)/100;if(t<=0)return;for(let r of(await R(e))){if(!await s(r.id)){console.log(`Skipping inactive user ${r.id} - no active mining units`);continue}let a=await T(r.id,e);if(a){let e=await i.FW.findByUserId(r.id),n=parseFloat(await i.rs.get("MAX_BINARY_POINTS_PER_SIDE")||"10"),s=e?.leftPoints||0,o=e?.rightPoints||0,l=0;if("LEFT"===a){if(s>=n){console.log(`User ${r.id} left side has reached maximum (${s}/${n}). No points added.`);continue}l=Math.min(t,n-s)}else{if(o>=n){console.log(`User ${r.id} right side has reached maximum (${o}/${n}). No points added.`);continue}l=Math.min(t,n-o)}if(l>0){let e="LEFT"===a?{leftPoints:l}:{rightPoints:l};await i.FW.upsert({userId:r.id,...e}),console.log(`Added ${l} points to ${a} side for active user ${r.id} (${l<t?"capped at limit":"full amount"})`)}}}}catch(e){throw console.error("Binary points addition error:",e),e}}async function R(e){try{let r=[],t=e;for(let e=0;e<10;e++){let e=await a.prisma.referral.findFirst({where:{referredId:t},include:{referrer:{select:{id:!0,email:!0}}}});if(!e)break;r.push(e.referrer),t=e.referrerId}return r}catch(e){return console.error("Upline users fetch error:",e),[]}}async function T(e,r){try{let t=await a.prisma.referral.findFirst({where:{referrerId:e,referredId:r}});if(t)return t.placementSide;let i=await P(e,"LEFT"),n=await P(e,"RIGHT");if(i.some(e=>e.id===r))return"LEFT";if(n.some(e=>e.id===r))return"RIGHT";return null}catch(e){return console.error("Placement side determination error:",e),null}}async function P(e,r){try{let t=[],i=new Set,n=(await a.prisma.referral.findMany({where:{referrerId:e,placementSide:r},select:{referredId:!0}})).map(e=>e.referredId);for(;n.length>0;){let e=n.shift();if(!i.has(e))for(let r of(i.add(e),t.push({id:e}),await a.prisma.referral.findMany({where:{referrerId:e},select:{referredId:!0}})))i.has(r.referredId)||n.push(r.referredId)}return t}catch(e){return console.error("Downline users fetch error:",e),[]}}async function $(e){try{let r=[],t=new Set,i=(await a.prisma.referral.findMany({where:{referrerId:e},select:{referredId:!0}})).map(e=>e.referredId);for(;i.length>0;){let e=i.shift();if(t.has(e))continue;t.add(e);let n=await a.prisma.user.findUnique({where:{id:e},select:{id:!0,isActive:!0}});if(n)for(let s of(r.push({id:n.id,isActive:n.isActive}),await a.prisma.referral.findMany({where:{referrerId:e},select:{referredId:!0}})))t.has(s.referredId)||i.push(s.referredId)}return r}catch(e){return console.error("All downline users fetch error:",e),[]}}async function N(){try{console.log("Starting binary matching process...");let e=parseFloat(await i.rs.get("MAX_BINARY_POINTS_PER_SIDE")||"10"),r=parseFloat(await i.rs.get("BINARY_POINT_VALUE")||"10"),t=await a.prisma.binaryPoints.findMany({where:{OR:[{leftPoints:{gt:0}},{rightPoints:{gt:0}}]},include:{user:{select:{id:!0,email:!0}}}});console.log(`Processing binary matching for ${t.length} users`);let s=[];for(let o of t)try{let t=Math.min(o.leftPoints,e),l=Math.min(o.rightPoints,e),d=Math.min(t,l);if(d>0){let e=d*r;try{let t=await i.DR.create({userId:o.userId,type:"BINARY_BONUS",amount:e,description:`Binary matching bonus - ${d} points matched at $${r} per point`,status:"COMPLETED"});try{let r=await (0,n.Py)(o.userId,e,"BINARY_BONUS",t.id,`Binary matching bonus - ${d} points matched`);console.log(`Allocated ${e} binary bonus to ${r.length} mining units for user ${o.userId}`),await i.k_.addEarnings(o.userId,e),console.log(`Added ${e} binary bonus to wallet balance for user ${o.userId}`)}catch(r){console.error(`Failed to allocate binary bonus to mining units for ${o.userId}:`,r),await i.k_.addEarnings(o.userId,e),console.log(`Fallback: Added ${e} binary bonus directly to wallet for user ${o.userId}`)}let l=Math.max(0,o.leftPoints-d),c=Math.max(0,o.rightPoints-d),u=o.leftPoints>o.rightPoints?l:0,f=o.rightPoints>o.leftPoints?c:0;await a.prisma.binaryPoints.update({where:{id:o.id},data:{leftPoints:u,rightPoints:f,matchedPoints:{increment:d},totalMatched:{increment:d},lastMatchDate:new Date,flushDate:new Date}}),s.push({userId:o.userId,matchedPoints:d,payout:e,remainingLeftPoints:u,remainingRightPoints:f}),console.log(`User ${o.userId}: ${d} points matched, $${e.toFixed(2)} payout, remaining: L${u} R${f}`)}catch(e){console.error(`Error processing payout for user ${o.userId}:`,e)}}else{let r=Math.max(0,o.leftPoints-e),t=Math.max(0,o.rightPoints-e);if(r>0||t>0)try{await a.prisma.binaryPoints.update({where:{id:o.id},data:{leftPoints:Math.min(o.leftPoints,e),rightPoints:Math.min(o.rightPoints,e),flushDate:new Date}}),console.log(`User ${o.userId}: Excess points reset - L${r} R${t} points flushed`)}catch(e){console.error(`Error flushing excess points for user ${o.userId}:`,e)}}}catch(e){console.error(`Error processing binary matching for user ${o.userId}:`,e)}return await i.AJ.create({action:"BINARY_MATCHING_PROCESSED",details:{usersProcessed:t.length,totalMatchedPoints:s.reduce((e,r)=>e+r.matchedPoints,0),pointValue:r,totalPayouts:s.reduce((e,r)=>e+r.payout,0),timestamp:new Date().toISOString()}}),console.log(`Binary matching completed. Processed ${s.length} users with total payouts: $${s.reduce((e,r)=>e+r.payout,0).toFixed(2)}`),{success:!0,usersProcessed:s.length,totalPayouts:s.reduce((e,r)=>e+r.payout,0),matchingResults:s}}catch(e){throw console.error("Binary matching process error:",e),e}}async function F(e){try{let r=await a.prisma.user.findUnique({where:{id:e},select:{referrerId:!0}});if(!r?.referrerId)return null;return await a.prisma.user.findUnique({where:{id:r.referrerId},select:{id:!0,email:!0,firstName:!0,lastName:!0}})}catch(e){return console.error("Sponsor info fetch error:",e),null}}async function A(e){try{return await a.prisma.user.count({where:{referrerId:e}})}catch(e){return console.error("Direct referral count error:",e),0}}async function b(e){try{return await f(e)}catch(e){return console.error("Total team count error:",e),{left:0,right:0,total:0}}}async function S(e){try{let r=await a.prisma.user.findUnique({where:{id:e},select:{directReferralCount:!0}}),t=await f(e),i=(await $(e)).filter(e=>e.isActive).length,n=new Date(Date.now()-2592e6),s=await a.prisma.user.count({where:{referrerId:e,createdAt:{gte:n}}});return{directReferrals:r?.directReferralCount||0,leftTeam:t.left,rightTeam:t.right,totalTeam:t.total,activeMembers:i,recentJoins:s}}catch(e){return console.error("Detailed team stats error:",e),{directReferrals:0,leftTeam:0,rightTeam:0,totalTeam:0,activeMembers:0,recentJoins:0}}}async function U(e,r){try{if(r<=0)return[];let t=[{id:e,side:null}];for(let e=1;e<=r;e++){let e=[];for(let r of t)for(let t of(await a.prisma.referral.findMany({where:{referrerId:r.id},include:{referred:{select:{id:!0,email:!0,firstName:!0,lastName:!0,createdAt:!0}}}})))e.push({id:t.referredId,side:t.placementSide});t=e}return(await Promise.all(t.map(async e=>({...await a.prisma.user.findUnique({where:{id:e.id},select:{id:!0,email:!0,firstName:!0,lastName:!0,createdAt:!0}}),placementSide:e.side})))).filter(Boolean)}catch(e){return console.error("Users by generation error:",e),[]}}async function D(e,r=3,t=new Set){try{let n=async(e,r,o="")=>{if(r<=0)return null;let l=await a.prisma.user.findUnique({where:{id:e},select:{id:!0,email:!0,firstName:!0,lastName:!0,profilePicture:!0,createdAt:!0}});if(!l)return null;let d=await s(e),c=await F(e),u=await A(e),f=await b(e),m=await a.prisma.referral.findFirst({where:{referrerId:e,placementSide:"LEFT"},include:{referred:{select:{id:!0,email:!0,firstName:!0,lastName:!0,createdAt:!0}}}}),h=await a.prisma.referral.findFirst({where:{referrerId:e,placementSide:"RIGHT"},include:{referred:{select:{id:!0,email:!0,firstName:!0,lastName:!0,createdAt:!0}}}}),w=await i.FW.findByUserId(e),p=o.length<3,g=t.has(e),I=r>1&&(p||g),y=null!==m,E=null!==h;return{user:{...l,isActive:d},sponsorInfo:c,directReferralCount:u,teamCounts:f,binaryPoints:w||{leftPoints:0,rightPoints:0,matchedPoints:0},hasLeftChild:y,hasRightChild:E,leftChild:I&&m?await n(m.referredId,r-1,o+"L"):null,rightChild:I&&h?await n(h.referredId,r-1,o+"R"):null}};return await n(e,r)}catch(e){throw console.error("Binary tree structure error:",e),e}}async function L(e){try{let r=await a.prisma.referral.findFirst({where:{referrerId:e,placementSide:"LEFT"},include:{referred:{select:{id:!0,email:!0,firstName:!0,lastName:!0,profilePicture:!0,createdAt:!0}}}}),t=await a.prisma.referral.findFirst({where:{referrerId:e,placementSide:"RIGHT"},include:{referred:{select:{id:!0,email:!0,firstName:!0,lastName:!0,profilePicture:!0,createdAt:!0}}}}),n=async e=>{if(!e)return null;let r=e.referredId,t=await s(r),n=await F(r),o=await A(r),l=await b(r),d=await i.FW.findByUserId(r),c=await a.prisma.referral.findFirst({where:{referrerId:r,placementSide:"LEFT"},select:{id:!0}})!==null,u=await a.prisma.referral.findFirst({where:{referrerId:r,placementSide:"RIGHT"},select:{id:!0}})!==null;return{user:{...e.referred,isActive:t},sponsorInfo:n,directReferralCount:o,teamCounts:l,binaryPoints:d||{leftPoints:0,rightPoints:0,matchedPoints:0},hasLeftChild:c,hasRightChild:u,leftChild:null,rightChild:null}},o=await n(r),l=await n(t);return{leftChild:o,rightChild:l}}catch(e){return console.error("Load node children error:",e),{leftChild:null,rightChild:null}}}async function M(e,r,t=20){try{r.toLowerCase();let i=await P(e,"LEFT"),n=await P(e,"RIGHT"),s=[...i,...n].map(e=>e.id);if(0===s.length)return[];let o=await a.prisma.user.findMany({where:{id:{in:s},OR:[{email:{contains:r,mode:"insensitive"}},{firstName:{contains:r,mode:"insensitive"}},{lastName:{contains:r,mode:"insensitive"}}]},select:{id:!0,email:!0,firstName:!0,lastName:!0,createdAt:!0,referrerId:!0},take:t});return await Promise.all(o.map(async r=>{let t,i=await _(e,r.id),n=i.split("-").length;return r.referrerId&&(t=await a.prisma.user.findUnique({where:{id:r.referrerId},select:{id:!0,email:!0,firstName:!0,lastName:!0}})),{id:r.id,email:r.email,firstName:r.firstName,lastName:r.lastName,createdAt:r.createdAt,placementPath:i,generation:n,sponsorInfo:t||void 0}}))}catch(e){return console.error("Search users in tree error:",e),[]}}async function _(e,r){try{if(e===r)return"ROOT";let t=[],i=r;for(;i!==e;){let e=await a.prisma.referral.findFirst({where:{referredId:i}});if(!e||(t.unshift("LEFT"===e.placementSide?"L":"R"),i=e.referrerId,t.length>20))break}return t.join("-")||"UNKNOWN"}catch(e){return console.error("Get placement path error:",e),"UNKNOWN"}}async function v(e,r){try{e!==r&&await u(e),await u(r);let t=(await R(r)).map(e=>u(e.id));await Promise.all(t)}catch(e){console.error("Update tree counts after placement error:",e)}}async function B(e){try{let r=e.map(e=>u(e));await Promise.all(r)}catch(e){console.error("Bulk update tree counts error:",e)}}async function G(e){try{let r=await f(e),t=r.total,i=Math.min(r.left,r.right),n=Math.max(r.left,r.right),s=0,o=0,l=0,d=[{userId:e,depth:0}],c=new Set;for(;d.length>0;){let{userId:e,depth:r}=d.shift();if(!c.has(e))for(let t of(c.add(e),s=Math.max(s,r),o+=r,l++,await a.prisma.referral.findMany({where:{referrerId:e},select:{referredId:!0}})))c.has(t.referredId)||d.push({userId:t.referredId,depth:r+1})}let u=l>0?o/l:0,m=Math.pow(2,s+1)-1,h=Math.max(0,m-t);return{totalUsers:t,balanceRatio:n>0?i/n:1,averageDepth:u,maxDepth:s,emptyPositions:h}}catch(e){return console.error("Tree health stats error:",e),{totalUsers:0,balanceRatio:1,averageDepth:0,maxDepth:0,emptyPositions:0}}}},39794:(e,r,t)=>{t.d(r,{Py:()=>s,k8:()=>d,kp:()=>l});var a=t(31183),i=t(6710);async function n(e){return await a.prisma.miningUnit.findMany({where:{userId:e,status:"ACTIVE",expiryDate:{gt:new Date}},orderBy:{createdAt:"asc"}})}async function s(e,r,t,s,l){let d=await n(e);if(0===d.length)throw Error("No active mining units found for earnings allocation");let c=[],u=r;for(let e of d){var f;if(u<=0)break;let r=Math.max(0,5*(f=e).investmentAmount-(f.miningEarnings+f.referralEarnings+f.binaryEarnings));if(r<=0)continue;let i=Math.min(u,r);if(i>0){let n={};switch(t){case"MINING_EARNINGS":n.miningEarnings={increment:i};break;case"DIRECT_REFERRAL":n.referralEarnings={increment:i};break;case"BINARY_BONUS":n.binaryEarnings={increment:i}}n.totalEarned={increment:i},await a.prisma.miningUnit.update({where:{id:e.id},data:n}),await a.prisma.miningUnitEarningsAllocation.create({data:{miningUnitId:e.id,transactionId:s,earningType:t,amount:i,description:l}}),c.push({miningUnitId:e.id,amount:i,remainingCapacity:r-i}),u-=i;let d=await a.prisma.miningUnit.findUnique({where:{id:e.id}});d&&function(e){let r=5*e.investmentAmount;return e.miningEarnings+e.referralEarnings+e.binaryEarnings>=r}(d)&&await o(e.id,"5x_investment_reached")}}return u>0&&(console.warn(`Unable to allocate ${u} to mining units - all units at capacity`),await i.AJ.create({action:"EARNINGS_ALLOCATION_OVERFLOW",userId:e,details:{totalAmount:r,allocatedAmount:r-u,overflowAmount:u,earningType:t,reason:"all_units_at_capacity"}})),c}async function o(e,r){let t=await a.prisma.miningUnit.findUnique({where:{id:e}});if(!t)throw Error(`Mining unit ${e} not found`);await a.prisma.miningUnit.update({where:{id:e},data:{status:"EXPIRED"}});let n=t.miningEarnings+t.referralEarnings+t.binaryEarnings;await i.AJ.create({action:"MINING_UNIT_EXPIRED",userId:t.userId,details:{miningUnitId:e,reason:r,totalEarned:n,miningEarnings:t.miningEarnings,referralEarnings:t.referralEarnings,binaryEarnings:t.binaryEarnings,investmentAmount:t.investmentAmount,multiplier:n/t.investmentAmount}}),console.log(`Mining unit ${e} expired due to ${r}. Total earnings: ${n}`)}async function l(e){return await a.prisma.miningUnit.findMany({where:{userId:e},orderBy:{createdAt:"asc"}})}async function d(e){return await a.prisma.miningUnitEarningsAllocation.findMany({where:{miningUnitId:e},include:{transaction:!0},orderBy:{allocatedAt:"desc"}})}}};