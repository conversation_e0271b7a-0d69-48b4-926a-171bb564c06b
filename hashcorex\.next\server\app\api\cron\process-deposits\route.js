"use strict";(()=>{var e={};e.id=7660,e.ids=[7660],e.modules={803:(e,t,n)=>{n.d(t,{x:()=>o});var s=n(92731),r=n(2746),i=n(6710);class a{constructor(){this.tasks=new Map,this.isInitialized=!1}static getInstance(){return a.instance||(a.instance=new a),a.instance}async initialize(){if(this.isInitialized)return void console.log("Scheduler service already initialized");console.log("Initializing server-side scheduler service...");try{this.scheduleDailyROI(),this.scheduleWeeklyPayout(),this.scheduleBinaryMatching(),this.scheduleDepositProcessing(),this.isInitialized=!0,console.log("Server-side scheduler service initialized successfully"),await i.AJ.create({action:"SCHEDULER_SERVICE_INITIALIZED",details:{tasksScheduled:this.tasks.size,initTime:new Date().toISOString(),tasks:Array.from(this.tasks.keys())}})}catch(e){throw console.error("Error initializing scheduler service:",e),e}}scheduleDailyROI(){let e="daily-roi",t=new Date,n=new Date;n.setUTCHours(0,1,0,0),n<=t&&n.setUTCDate(n.getUTCDate()+1);let s=n.getTime()-t.getTime();console.log(`Scheduling daily ROI calculation for ${n.toISOString()}`),setTimeout(()=>{this.runDailyROI();let t=setInterval(()=>{this.runDailyROI()},864e5);this.tasks.set(e,{name:e,interval:t,isRunning:!1,lastRun:null,nextRun:new Date(Date.now()+864e5),runCount:0})},s),this.tasks.set(e,{name:e,interval:null,isRunning:!1,lastRun:null,nextRun:n,runCount:0})}scheduleWeeklyPayout(){let e="weekly-payout",t=this.getNextWeeklyTime(6,15),n=t.getTime()-Date.now();console.log(`Scheduling weekly payout for ${t.toISOString()}`),setTimeout(()=>{this.runWeeklyPayout();let t=setInterval(()=>{this.runWeeklyPayout()},6048e5);this.tasks.set(e,{name:e,interval:t,isRunning:!1,lastRun:null,nextRun:new Date(Date.now()+6048e5),runCount:0})},n),this.tasks.set(e,{name:e,interval:null,isRunning:!1,lastRun:null,nextRun:t,runCount:0})}scheduleBinaryMatching(){let e="binary-matching",t=this.getNextWeeklyTime(6,15),n=t.getTime()-Date.now();console.log(`Scheduling binary matching for ${t.toISOString()}`),setTimeout(()=>{this.runBinaryMatching();let t=setInterval(()=>{this.runBinaryMatching()},6048e5);this.tasks.set(e,{name:e,interval:t,isRunning:!1,lastRun:null,nextRun:new Date(Date.now()+6048e5),runCount:0})},n),this.tasks.set(e,{name:e,interval:null,isRunning:!1,lastRun:null,nextRun:t,runCount:0})}scheduleDepositProcessing(){let e="deposit-processing";console.log("Scheduling deposit processing every 10 minutes");let t=setInterval(()=>{this.runDepositProcessing()},6e5);this.runDepositProcessing(),this.tasks.set(e,{name:e,interval:t,isRunning:!1,lastRun:null,nextRun:new Date(Date.now()+6e5),runCount:0})}getNextWeeklyTime(e,t){let n=new Date,s=new Date;s.setUTCHours(t,0,0,0);let r=e-n.getUTCDay();return(r<0||0===r&&n.getUTCHours()>=t)&&(r+=7),s.setUTCDate(n.getUTCDate()+r),s}async runDailyROI(){let e=this.tasks.get("daily-roi");if(!e||e.isRunning)return void console.log("Daily ROI task already running or not found");e.isRunning=!0,e.lastRun=new Date,e.runCount++;try{console.log("Starting scheduled daily ROI calculation...");let t=await (0,s.eB)();console.log(`Expired ${t} old mining units`);let n=await (0,s.WL)();console.log(`Processed ${n.length} mining units for daily ROI`);let r=n.reduce((e,t)=>e+t.earnings,0),a=n.filter(e=>e.expired).length;await i.AJ.create({action:"DAILY_ROI_SCHEDULED_EXECUTED",details:{unitsProcessed:n.length,totalEarnings:r,expiredUnits:a,oldUnitsExpired:t,executionTime:new Date().toISOString(),runCount:e.runCount}}),console.log(`Daily ROI calculation completed successfully (run #${e.runCount})`)}catch(t){console.error("Daily ROI scheduled task error:",t),await i.AJ.create({action:"DAILY_ROI_SCHEDULED_ERROR",details:{error:t instanceof Error?t.message:"Unknown error",stack:t instanceof Error?t.stack:void 0,timestamp:new Date().toISOString(),runCount:e.runCount}})}finally{e.isRunning=!1,e.nextRun=new Date(Date.now()+864e5)}}async runWeeklyPayout(){let e=this.tasks.get("weekly-payout");if(!e||e.isRunning)return void console.log("Weekly payout task already running or not found");e.isRunning=!0,e.lastRun=new Date,e.runCount++;try{console.log("Starting scheduled weekly earnings payout...");let t=await (0,s.Oh)();console.log(`Processed earnings for ${t.length} users`);let n=t.reduce((e,t)=>e+t.totalEarnings,0);await i.AJ.create({action:"WEEKLY_PAYOUT_SCHEDULED_EXECUTED",details:{usersProcessed:t.length,totalDistributed:n,executionTime:new Date().toISOString(),payoutDay:"Saturday",payoutTime:"15:00 UTC",runCount:e.runCount}}),console.log(`Weekly payout completed successfully (run #${e.runCount})`)}catch(t){console.error("Weekly payout scheduled task error:",t),await i.AJ.create({action:"WEEKLY_PAYOUT_SCHEDULED_ERROR",details:{error:t instanceof Error?t.message:"Unknown error",stack:t instanceof Error?t.stack:void 0,timestamp:new Date().toISOString(),runCount:e.runCount}})}finally{e.isRunning=!1,e.nextRun=new Date(Date.now()+6048e5)}}async runBinaryMatching(){let e=this.tasks.get("binary-matching");if(!e||e.isRunning)return void console.log("Binary matching task already running or not found");e.isRunning=!0,e.lastRun=new Date,e.runCount++;try{console.log("Starting scheduled binary matching...");let t=await (0,r.E5)(),n=t.reduce((e,t)=>e+t.payout,0),s=t.reduce((e,t)=>e+t.matchedPoints,0);await i.AJ.create({action:"BINARY_MATCHING_SCHEDULED_EXECUTED",details:{usersProcessed:t.length,totalPayouts:n,totalMatchedPoints:s,executionTime:new Date().toISOString(),matchingTime:"15:00 UTC (Weekly)",runCount:e.runCount}}),console.log(`Binary matching completed successfully (run #${e.runCount})`)}catch(t){console.error("Binary matching scheduled task error:",t),await i.AJ.create({action:"BINARY_MATCHING_SCHEDULED_ERROR",details:{error:t instanceof Error?t.message:"Unknown error",stack:t instanceof Error?t.stack:void 0,timestamp:new Date().toISOString(),runCount:e.runCount}})}finally{e.isRunning=!1,e.nextRun=new Date(Date.now()+6048e5)}}async runDepositProcessing(){let e=this.tasks.get("deposit-processing");if(!e||e.isRunning)return void console.log("Deposit processing task already running or not found");e.isRunning=!0,e.lastRun=new Date,e.runCount++;try{console.log("Starting scheduled deposit processing...");let{processDeposits:t}=await Promise.all([n.e(9480),n.e(9046)]).then(n.bind(n,79046)),s=await t();await i.AJ.create({action:"DEPOSIT_PROCESSING_SCHEDULED_EXECUTED",details:{...s,executionTime:new Date().toISOString(),runCount:e.runCount}}),console.log(`Deposit processing completed successfully (run #${e.runCount})`)}catch(t){console.error("Deposit processing scheduled task error:",t),await i.AJ.create({action:"DEPOSIT_PROCESSING_SCHEDULED_ERROR",details:{error:t instanceof Error?t.message:"Unknown error",stack:t instanceof Error?t.stack:void 0,timestamp:new Date().toISOString(),runCount:e.runCount}})}finally{e.isRunning=!1,e.nextRun=new Date(Date.now()+6e5)}}stop(){for(let[e,t]of(console.log("Stopping scheduler service..."),this.tasks))t.interval&&(clearInterval(t.interval),console.log(`Stopped task: ${e}`));this.tasks.clear(),this.isInitialized=!1,console.log("Scheduler service stopped")}getStatus(){return{isInitialized:this.isInitialized,totalTasks:this.tasks.size,tasks:Array.from(this.tasks.entries()).map(([e,t])=>({name:e,isRunning:t.isRunning,lastRun:t.lastRun?.toISOString()||null,nextRun:t.nextRun?.toISOString()||null,runCount:t.runCount}))}}async triggerTask(e){switch(e){case"daily-roi":await this.runDailyROI();break;case"weekly-payout":await this.runWeeklyPayout();break;case"binary-matching":await this.runBinaryMatching();break;case"deposit-processing":await this.runDepositProcessing();break;default:throw Error(`Unknown task: ${e}`)}}}let o=a.getInstance()},3295:e=>{e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},4381:(e,t,n)=>{n.r(t),n.d(t,{patchFetch:()=>y,routeModule:()=>d,serverHooks:()=>h,workAsyncStorage:()=>g,workUnitAsyncStorage:()=>p});var s={};n.r(s),n.d(s,{POST:()=>c});var r=n(96559),i=n(48088),a=n(37719),o=n(32190),l=n(803),u=n(6710);async function c(e){try{let t=e.headers.get("authorization"),n=process.env.CRON_SECRET;if(!n||t!==`Bearer ${n}`)return o.NextResponse.json({success:!1,error:"Unauthorized"},{status:401});return console.log("DEPRECATED: Deposit processing cron endpoint called - redirecting to scheduler service..."),await u.AJ.create({action:"DEPRECATED_CRON_ENDPOINT_USED",details:{endpoint:"/api/cron/process-deposits",message:"This endpoint is deprecated. Deposit processing is now handled by server-side scheduler.",timestamp:new Date().toISOString()}}),await l.x.triggerTask("deposit-processing"),o.NextResponse.json({success:!0,message:"Deposit processing triggered via scheduler service",deprecated:!0,notice:"This endpoint is deprecated. Deposit processing is now handled automatically by server-side scheduler."})}catch(e){return console.error("Deposit processing cron job error:",e),await u.AJ.create({action:"DEPRECATED_CRON_ERROR",details:{endpoint:"/api/cron/process-deposits",error:e instanceof Error?e.message:"Unknown error",stack:e instanceof Error?e.stack:void 0,timestamp:new Date().toISOString()}}),o.NextResponse.json({success:!1,error:"Deposit processing failed"},{status:500})}}let d=new r.AppRouteRouteModule({definition:{kind:i.RouteKind.APP_ROUTE,page:"/api/cron/process-deposits/route",pathname:"/api/cron/process-deposits",filename:"route",bundlePath:"app/api/cron/process-deposits/route"},resolvedPagePath:"C:\\Users\\<USER>\\Desktop\\Hash_Minings\\hashcorex\\src\\app\\api\\cron\\process-deposits\\route.ts",nextConfigOutput:"",userland:s}),{workAsyncStorage:g,workUnitAsyncStorage:p,serverHooks:h}=d;function y(){return(0,a.patchFetch)({workAsyncStorage:g,workUnitAsyncStorage:p})}},10846:e=>{e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},29294:e=>{e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},44870:e=>{e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},55511:e=>{e.exports=require("crypto")},63033:e=>{e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},96330:e=>{e.exports=require("@prisma/client")}};var t=require("../../../../webpack-runtime.js");t.C(e);var n=e=>t(t.s=e),s=t.X(0,[4447,580,3306,2746,5112],()=>n(4381));module.exports=s})();