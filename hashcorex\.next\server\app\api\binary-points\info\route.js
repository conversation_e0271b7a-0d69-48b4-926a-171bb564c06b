"use strict";(()=>{var e={};e.id=1826,e.ids=[1826],e.modules={3295:e=>{e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},12909:(e,t,r)=>{r.d(t,{DT:()=>w,DY:()=>f,HU:()=>p,Lx:()=>y,b9:()=>m,qc:()=>g});var a=r(85663),i=r(43205),s=r.n(i),n=r(6710);let o=process.env.JWT_SECRET||"fallback-secret-key",l=process.env.JWT_EXPIRES_IN||"30d",d=async e=>await a.Ay.hash(e,12),u=async(e,t)=>await a.Ay.compare(e,t),p=e=>s().sign(e,o,{expiresIn:l}),c=e=>{try{return s().verify(e,o)}catch(e){return null}},h=()=>{let e="ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789",t="HC";for(let r=0;r<8;r++)t+=e.charAt(Math.floor(Math.random()*e.length));return t},m=async e=>{let t=e.headers.get("authorization")?.replace("Bearer ","")||e.cookies.get("auth-token")?.value;if(!t)return{authenticated:!1,user:null};let r=c(t);if(!r)return{authenticated:!1,user:null};let a=await n.Gy.findByEmail(r.email);return a?{authenticated:!0,user:a}:{authenticated:!1,user:null}},f=async e=>{let t,a;if(await n.Gy.findByEmail(e.email))throw Error("User already exists with this email");if(e.referralCode){let r=await n.Gy.findByReferralId(e.referralCode);if(!r)throw Error("Invalid referral code");t=r.id}let i=await d(e.password),s=!1;do a=h(),s=!await n.Gy.findByReferralId(a);while(!s);let o=await n.Gy.create({email:e.email,firstName:e.firstName,lastName:e.lastName,password:i,referralId:a});if(t){let{placeUserByReferralType:a}=await r.e(2746).then(r.bind(r,2746)),i="general";"left"===e.placementSide?i="left":"right"===e.placementSide&&(i="right"),await a(t,o.id,i)}return{id:o.id,email:o.email,referralId:o.referralId,kycStatus:o.kycStatus}},y=async e=>{let t=await n.Gy.findByEmail(e.email);if(!t||!await u(e.password,t.password))throw Error("Invalid email or password");return{token:p({userId:t.id,email:t.email}),user:{id:t.id,email:t.email,referralId:t.referralId,kycStatus:t.kycStatus}}},w=e=>/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(e),g=async e=>{let t=await n.Gy.findById(e);return t?.role==="ADMIN"}},27910:e=>{e.exports=require("stream")},28354:e=>{e.exports=require("util")},29189:(e,t,r)=>{r.r(t),r.d(t,{patchFetch:()=>y,routeModule:()=>c,serverHooks:()=>f,workAsyncStorage:()=>h,workUnitAsyncStorage:()=>m});var a={};r.r(a),r.d(a,{GET:()=>p});var i=r(96559),s=r(48088),n=r(37719),o=r(32190),l=r(12909),d=r(6710),u=r(31183);async function p(e){try{let{authenticated:t,user:r}=await (0,l.b9)(e);if(!t||!r)return o.NextResponse.json({success:!1,error:"Not authenticated"},{status:401});let a=await d.FW.findByUserId(r.id),i=parseFloat(await d.rs.get("MAX_BINARY_POINTS_PER_SIDE")||"10"),s=parseFloat(await d.rs.get("BINARY_POINT_VALUE")||"10"),n=a?.leftPoints||0,p=a?.rightPoints||0,c=a?.matchedPoints||0,h=a?.totalMatched||0,m=Math.min(n,i),f=Math.min(p,i),y=Math.min(m,f),w=Math.max(0,n-i),g=Math.max(0,p-i),x=w+g,I=new Date(Date.now()-24192e5),k=await u.prisma.transaction.findMany({where:{userId:r.id,type:"BINARY_BONUS",status:"COMPLETED",createdAt:{gte:I}},orderBy:{createdAt:"desc"},take:4}),v=m/i*100,A=f/i*100,E=[];n>.9*i&&E.push(`Left side approaching cap (${n.toFixed(0)}/${i})`),p>.9*i&&E.push(`Right side approaching cap (${p.toFixed(0)}/${i})`),x>0&&E.push(`${x.toFixed(0)} points will be flushed due to pressure-out`);let M={currentPoints:{left:n,right:p,leftCapped:m,rightCapped:f,matchable:y,matched:c,totalMatched:h},limits:{maxPointsPerSide:i,pointValue:s},progress:{leftProgress:v,rightProgress:A,leftNearCap:v>90,rightNearCap:A>90},pressureOut:{leftAmount:w,rightAmount:g,totalAmount:x,willOccur:x>0},earnings:{estimatedPayout:y*s,pointValue:s,matchablePoints:y},history:{recentMatches:k.map(e=>({amount:e.amount,date:e.createdAt,description:e.description})),averageWeeklyEarnings:k.length>0?k.reduce((e,t)=>e+t.amount,0)/k.length:0},warnings:E,hasWarnings:E.length>0,nextMatching:{schedule:"Weekly on Saturdays at 15:00 UTC",description:"Binary points are matched weekly, not daily"}};return o.NextResponse.json({success:!0,data:M})}catch(e){return console.error("Binary points info fetch error:",e),o.NextResponse.json({success:!1,error:"Failed to fetch binary points information"},{status:500})}}let c=new i.AppRouteRouteModule({definition:{kind:s.RouteKind.APP_ROUTE,page:"/api/binary-points/info/route",pathname:"/api/binary-points/info",filename:"route",bundlePath:"app/api/binary-points/info/route"},resolvedPagePath:"C:\\Users\\<USER>\\Desktop\\Hash_Minings\\hashcorex\\src\\app\\api\\binary-points\\info\\route.ts",nextConfigOutput:"",userland:a}),{workAsyncStorage:h,workUnitAsyncStorage:m,serverHooks:f}=c;function y(){return(0,n.patchFetch)({workAsyncStorage:h,workUnitAsyncStorage:m})}},29294:e=>{e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},44870:e=>{e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},55511:e=>{e.exports=require("crypto")},63033:e=>{e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},79428:e=>{e.exports=require("buffer")},96330:e=>{e.exports=require("@prisma/client")}};var t=require("../../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),a=t.X(0,[4447,580,5315,3306],()=>r(29189));module.exports=a})();