"use strict";(()=>{var e={};e.id=9530,e.ids=[9530],e.modules={3295:e=>{e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},12909:(e,t,r)=>{r.d(t,{DT:()=>I,DY:()=>m,HU:()=>c,Lx:()=>g,b9:()=>w,qc:()=>h});var a=r(85663),s=r(43205),i=r.n(s),n=r(6710);let o=process.env.JWT_SECRET||"fallback-secret-key",u=process.env.JWT_EXPIRES_IN||"30d",d=async e=>await a.Ay.hash(e,12),l=async(e,t)=>await a.Ay.compare(e,t),c=e=>i().sign(e,o,{expiresIn:u}),p=e=>{try{return i().verify(e,o)}catch(e){return null}},f=()=>{let e="ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789",t="HC";for(let r=0;r<8;r++)t+=e.charAt(Math.floor(Math.random()*e.length));return t},w=async e=>{let t=e.headers.get("authorization")?.replace("Bearer ","")||e.cookies.get("auth-token")?.value;if(!t)return{authenticated:!1,user:null};let r=p(t);if(!r)return{authenticated:!1,user:null};let a=await n.Gy.findByEmail(r.email);return a?{authenticated:!0,user:a}:{authenticated:!1,user:null}},m=async e=>{let t,a;if(await n.Gy.findByEmail(e.email))throw Error("User already exists with this email");if(e.referralCode){let r=await n.Gy.findByReferralId(e.referralCode);if(!r)throw Error("Invalid referral code");t=r.id}let s=await d(e.password),i=!1;do a=f(),i=!await n.Gy.findByReferralId(a);while(!i);let o=await n.Gy.create({email:e.email,firstName:e.firstName,lastName:e.lastName,password:s,referralId:a});if(t){let{placeUserByReferralType:a}=await r.e(2746).then(r.bind(r,2746)),s="general";"left"===e.placementSide?s="left":"right"===e.placementSide&&(s="right"),await a(t,o.id,s)}return{id:o.id,email:o.email,referralId:o.referralId,kycStatus:o.kycStatus}},g=async e=>{let t=await n.Gy.findByEmail(e.email);if(!t||!await l(e.password,t.password))throw Error("Invalid email or password");return{token:c({userId:t.id,email:t.email}),user:{id:t.id,email:t.email,referralId:t.referralId,kycStatus:t.kycStatus}}},I=e=>/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(e),h=async e=>{let t=await n.Gy.findById(e);return t?.role==="ADMIN"}},21820:e=>{e.exports=require("os")},27910:e=>{e.exports=require("stream")},28354:e=>{e.exports=require("util")},29021:e=>{e.exports=require("fs")},29294:e=>{e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:e=>{e.exports=require("path")},34631:e=>{e.exports=require("tls")},37366:e=>{e.exports=require("dns")},44870:e=>{e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},45492:(e,t,r)=>{r.r(t),r.d(t,{patchFetch:()=>h,routeModule:()=>w,serverHooks:()=>I,workAsyncStorage:()=>m,workUnitAsyncStorage:()=>g});var a={};r.r(a),r.d(a,{POST:()=>f});var s=r(96559),i=r(48088),n=r(37719),o=r(32190),u=r(12909),d=r(6710),l=r(59480),c=r(1827);let p=new Map;async function f(e){try{let{authenticated:t,user:r}=await (0,u.b9)(e);if(!t||!r)return o.NextResponse.json({success:!1,error:"Not authenticated"},{status:401});if(!function(e){let t=Date.now(),r=p.get(e);return!r||t>r.resetTime?(p.set(e,{count:1,resetTime:t+6e4}),!0):!(r.count>=5)&&(r.count++,!0)}(r.id))return o.NextResponse.json({success:!1,error:"Too many verification requests. Please wait before trying again."},{status:429});let{transactionId:a}=await e.json();if(!a||"string"!=typeof a)return o.NextResponse.json({success:!1,error:"Transaction ID is required"},{status:400});if(!(0,l.TA)(a))return o.NextResponse.json({success:!1,error:"Invalid Tron transaction ID format"},{status:400});let s=await d.J6.findByTransactionId(a);if(s)return await d.AJ.create({action:"DEPOSIT_DUPLICATE_ATTEMPT",userId:r.id,details:{transactionId:a,existingDepositId:s.id,existingStatus:s.status},ipAddress:e.headers.get("x-forwarded-for")||e.headers.get("x-real-ip")||"unknown",userAgent:e.headers.get("user-agent")||"unknown"}),o.NextResponse.json({success:!1,error:"This transaction ID has already been submitted. Please check your deposit history.",data:{existingStatus:s.status,submittedAt:s.createdAt}},{status:400});let i=await d.rs.get("usdtDepositAddress");i||(i=await d.rs.get("USDT_DEPOSIT_ADDRESS")),i&&(i=i.replace(/['"]/g,"").trim());let n=await d.rs.get("minDepositAmount");n||(n=await d.rs.get("MIN_DEPOSIT_AMOUNT")),n=parseFloat(n||"10");let f=await d.rs.get("maxDepositAmount");f||(f=await d.rs.get("MAX_DEPOSIT_AMOUNT")),f=parseFloat(f||"10000");let w=await d.rs.get("depositEnabled");w||(w=await d.rs.get("DEPOSIT_ENABLED")),w="true"===w||!0===w;let m=await d.rs.get("minConfirmations");if(m||(m=await d.rs.get("MIN_CONFIRMATIONS")),m=parseInt(m||"1"),!w)return o.NextResponse.json({success:!1,error:"Deposits are currently disabled"},{status:503});if(!i)return o.NextResponse.json({success:!1,error:"Deposit address not configured. Please contact support."},{status:503});return await d.AJ.create({action:"DEPOSIT_VERIFICATION_ATTEMPT",userId:r.id,details:{transactionId:a},ipAddress:e.headers.get("x-forwarded-for")||e.headers.get("x-real-ip")||"unknown",userAgent:e.headers.get("user-agent")||"unknown"}),await d.J6.create({userId:r.id,transactionId:a,amount:0,usdtAmount:0,tronAddress:i,senderAddress:"",blockNumber:"",blockTimestamp:new Date,confirmations:0}),await d.J6.updateStatus(a,"PENDING_VERIFICATION"),await d.DR.create({userId:r.id,type:"DEPOSIT",amount:0,description:`USDT TRC20 Deposit - TX: ${a}`,status:"PENDING"}),await c.j.addTransactionForVerification(a,i),await d.AJ.create({action:"DEPOSIT_SUBMITTED",userId:r.id,details:{transactionId:a,depositAddress:i,status:"PENDING_VERIFICATION"},ipAddress:e.headers.get("x-forwarded-for")||e.headers.get("x-real-ip")||"unknown",userAgent:e.headers.get("user-agent")||"unknown"}),o.NextResponse.json({success:!0,message:"Deposit transaction submitted successfully. We are now verifying your transaction. This may take up to 2 minutes.",data:{transactionId:a,status:"PENDING_VERIFICATION",estimatedVerificationTime:"Within 2 minutes",nextSteps:["Transaction verification in progress","Confirmation checking will begin once transaction is found",`Wallet will be credited automatically after ${m} confirmations`]}})}catch(t){if(console.error("Deposit verification error:",t),e.headers.get("authorization"))try{let{user:r}=await (0,u.b9)(e);r&&await d.AJ.create({action:"DEPOSIT_VERIFICATION_ERROR",userId:r.id,details:{error:t instanceof Error?t.message:"Unknown error"},ipAddress:e.headers.get("x-forwarded-for")||e.headers.get("x-real-ip")||"unknown",userAgent:e.headers.get("user-agent")||"unknown"})}catch(e){console.error("Failed to log error:",e)}return o.NextResponse.json({success:!1,error:"Failed to verify deposit. Please try again later."},{status:500})}}let w=new s.AppRouteRouteModule({definition:{kind:i.RouteKind.APP_ROUTE,page:"/api/wallet/deposit/verify/route",pathname:"/api/wallet/deposit/verify",filename:"route",bundlePath:"app/api/wallet/deposit/verify/route"},resolvedPagePath:"C:\\Users\\<USER>\\Desktop\\Hash_Minings\\hashcorex\\src\\app\\api\\wallet\\deposit\\verify\\route.ts",nextConfigOutput:"",userland:a}),{workAsyncStorage:m,workUnitAsyncStorage:g,serverHooks:I}=w;function h(){return(0,n.patchFetch)({workAsyncStorage:m,workUnitAsyncStorage:g})}},55511:e=>{e.exports=require("crypto")},55591:e=>{e.exports=require("https")},63033:e=>{e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},74075:e=>{e.exports=require("zlib")},79428:e=>{e.exports=require("buffer")},79551:e=>{e.exports=require("url")},79646:e=>{e.exports=require("child_process")},81630:e=>{e.exports=require("http")},91645:e=>{e.exports=require("net")},94735:e=>{e.exports=require("events")},96330:e=>{e.exports=require("@prisma/client")}};var t=require("../../../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),a=t.X(0,[4447,580,5315,9526,3306,9480,1827],()=>r(45492));module.exports=a})();