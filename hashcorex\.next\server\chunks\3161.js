"use strict";exports.id=3161,exports.ids=[3161],exports.modules={13161:(t,e,a)=>{a.d(e,{emailNotificationService:()=>o});var r=a(21111),s=a(6710);class i{async sendDepositSuccessNotification(t){try{let e=await r.gm.getEmailTemplate("deposit_success");e||console.warn('Email template "deposit_success" not found. Using default template.');let a=e?.subject||"Deposit Confirmed - HashCoreX",i=e?.htmlContent||this.getDefaultDepositSuccessTemplate(),o=e?.textContent||"";i=this.replaceVariables(i,{firstName:t.firstName,lastName:t.lastName,email:t.email,amount:t.amount.toString(),transactionId:t.transactionId,currency:t.currency}),o=this.replaceVariables(o,{firstName:t.firstName,lastName:t.lastName,email:t.email,amount:t.amount.toString(),transactionId:t.transactionId,currency:t.currency});let n=await s.XB.create({to:t.email,subject:a,template:"deposit_success",status:"PENDING"});if(await r.gm.sendEmail({to:t.email,subject:a,html:i,text:o||`Deposit Confirmed

Amount: ${t.amount} ${t.currency}
Transaction ID: ${t.transactionId}`}))return await s.XB.updateStatus(n.id,"SENT"),!0;return await s.XB.updateStatus(n.id,"FAILED","Email service error"),!1}catch(t){return console.error("Error sending deposit success notification:",t),!1}}async sendKYCStatusNotification(t){try{let e="APPROVED"===t.status?"kyc_approved":"kyc_rejected",a=await r.gm.getEmailTemplate(e);a||console.warn(`Email template "${e}" not found. Using default template.`);let i=a?.subject||`KYC ${"APPROVED"===t.status?"Approved":"Rejected"} - HashCoreX`,o=a?.htmlContent||this.getDefaultKYCTemplate(t.status),n=a?.textContent||"";o=this.replaceVariables(o,{firstName:t.firstName,lastName:t.lastName,email:t.email,status:t.status,rejectionReason:t.rejectionReason||""}),n=this.replaceVariables(n,{firstName:t.firstName,lastName:t.lastName,email:t.email,status:t.status,rejectionReason:t.rejectionReason||""});let l=await s.XB.create({to:t.email,subject:i,template:e,status:"PENDING"});if(await r.gm.sendEmail({to:t.email,subject:i,html:o,text:n||`KYC ${t.status}

${t.rejectionReason?`Reason: ${t.rejectionReason}`:"Your KYC has been approved."}`}))return await s.XB.updateStatus(l.id,"SENT"),!0;return await s.XB.updateStatus(l.id,"FAILED","Email service error"),!1}catch(t){return console.error("Error sending KYC status notification:",t),!1}}async sendWithdrawalStatusNotification(t){try{let e=this.getWithdrawalTemplateName(t.status),a=await r.gm.getEmailTemplate(e);a||console.warn(`Email template "${e}" not found. Using default template.`);let i=a?.subject||`Withdrawal ${this.getWithdrawalStatusText(t.status)} - HashCoreX`,o=a?.htmlContent||this.getDefaultWithdrawalTemplate(t.status),n=a?.textContent||"";o=this.replaceVariables(o,{firstName:t.firstName,lastName:t.lastName,email:t.email,amount:t.amount.toString(),status:t.status,transactionHash:t.transactionHash||"",rejectionReason:t.rejectionReason||"",usdtAddress:t.usdtAddress||""}),n=this.replaceVariables(n,{firstName:t.firstName,lastName:t.lastName,email:t.email,amount:t.amount.toString(),status:t.status,transactionHash:t.transactionHash||"",rejectionReason:t.rejectionReason||"",usdtAddress:t.usdtAddress||""});let l=await s.XB.create({to:t.email,subject:i,template:e,status:"PENDING"});if(await r.gm.sendEmail({to:t.email,subject:i,html:o,text:n||this.getDefaultWithdrawalText(t)}))return await s.XB.updateStatus(l.id,"SENT"),!0;return await s.XB.updateStatus(l.id,"FAILED","Email service error"),!1}catch(t){return console.error("Error sending withdrawal status notification:",t),!1}}replaceVariables(t,e){let a=t;for(let[t,r]of Object.entries(e)){let e=RegExp(`{{${t}}}`,"g");a=a.replace(e,r)}return a}getWithdrawalTemplateName(t){switch(t){case"APPROVED":return"withdrawal_approved";case"REJECTED":return"withdrawal_rejected";case"COMPLETED":return"withdrawal_completed";case"FAILED":return"withdrawal_failed";default:return"withdrawal_status"}}getWithdrawalStatusText(t){switch(t){case"APPROVED":return"Approved";case"REJECTED":return"Rejected";case"COMPLETED":return"Completed";case"FAILED":return"Failed";default:return"Updated"}}getDefaultDepositSuccessTemplate(){return`
      <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
        <div style="background: linear-gradient(135deg, #ffd60a 0%, #10b981 100%); padding: 20px; text-align: center;">
          <h1 style="color: white; margin: 0;">HashCoreX</h1>
        </div>
        <div style="padding: 30px; background: #f9f9f9;">
          <h2 style="color: #333;">Deposit Confirmed!</h2>
          <p>Hello {{firstName}},</p>
          <p>Great news! Your deposit has been successfully confirmed and credited to your account.</p>
          <div style="background: white; padding: 20px; margin: 20px 0; border-radius: 8px; border-left: 4px solid #10b981;">
            <h3 style="margin: 0 0 10px 0; color: #10b981;">Deposit Details</h3>
            <p><strong>Amount:</strong> {{amount}} {{currency}}</p>
            <p><strong>Transaction ID:</strong> {{transactionId}}</p>
            <p><strong>Status:</strong> Confirmed</p>
          </div>
          <p>Your funds are now available in your wallet and you can start using them immediately.</p>
          <p>Best regards,<br>The HashCoreX Team</p>
        </div>
        <div style="background: #333; color: white; padding: 20px; text-align: center; font-size: 12px;">
          <p>&copy; 2024 HashCoreX. All rights reserved.</p>
        </div>
      </div>
    `}getDefaultKYCTemplate(t){let e="APPROVED"===t;return`
      <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
        <div style="background: linear-gradient(135deg, #ffd60a 0%, ${e?"#10b981":"#ef4444"} 100%); padding: 20px; text-align: center;">
          <h1 style="color: white; margin: 0;">HashCoreX</h1>
        </div>
        <div style="padding: 30px; background: #f9f9f9;">
          <h2 style="color: #333;">${e?"KYC Approved!":"KYC Rejected"}</h2>
          <p>Hello {{firstName}},</p>
          <p>${e?"Congratulations! Your KYC verification has been approved. You now have full access to all platform features.":"Unfortunately, your KYC verification has been rejected. Please review the reason below and resubmit with correct documents."}</p>
          ${!e?'<div style="background: white; padding: 20px; margin: 20px 0; border-radius: 8px; border-left: 4px solid #ef4444;"><p><strong>Rejection Reason:</strong> {{rejectionReason}}</p></div>':""}
          <p>Best regards,<br>The HashCoreX Team</p>
        </div>
        <div style="background: #333; color: white; padding: 20px; text-align: center; font-size: 12px;">
          <p>&copy; 2024 HashCoreX. All rights reserved.</p>
        </div>
      </div>
    `}getDefaultWithdrawalTemplate(t){let e=t=>{switch(t){case"APPROVED":case"COMPLETED":return"#10b981";case"REJECTED":case"FAILED":return"#ef4444";default:return"#6b7280"}};return`
      <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
        <div style="background: linear-gradient(135deg, #ffd60a 0%, ${e(t)} 100%); padding: 20px; text-align: center;">
          <h1 style="color: white; margin: 0;">HashCoreX</h1>
        </div>
        <div style="padding: 30px; background: #f9f9f9;">
          <h2 style="color: #333;">Withdrawal {{status}}</h2>
          <p>Hello {{firstName}},</p>
          <p>Your withdrawal request has been {{status}}.</p>
          <div style="background: white; padding: 20px; margin: 20px 0; border-radius: 8px; border-left: 4px solid ${e(t)};">
            <h3 style="margin: 0 0 10px 0; color: ${e(t)};">Withdrawal Details</h3>
            <p><strong>Amount:</strong> {{amount}} USDT</p>
            <p><strong>Status:</strong> {{status}}</p>
            {{#if usdtAddress}}<p><strong>Address:</strong> {{usdtAddress}}</p>{{/if}}
            {{#if transactionHash}}<p><strong>Transaction Hash:</strong> {{transactionHash}}</p>{{/if}}
            {{#if rejectionReason}}<p><strong>Reason:</strong> {{rejectionReason}}</p>{{/if}}
          </div>
          <p>Best regards,<br>The HashCoreX Team</p>
        </div>
        <div style="background: #333; color: white; padding: 20px; text-align: center; font-size: 12px;">
          <p>&copy; 2024 HashCoreX. All rights reserved.</p>
        </div>
      </div>
    `}getDefaultWithdrawalText(t){let e=`Withdrawal ${t.status}

Amount: ${t.amount} USDT
Status: ${t.status}`;return t.usdtAddress&&(e+=`
Address: ${t.usdtAddress}`),t.transactionHash&&(e+=`
Transaction Hash: ${t.transactionHash}`),t.rejectionReason&&(e+=`
Reason: ${t.rejectionReason}`),e}}let o=new i},21111:(t,e,a)=>{a.d(e,{XG:()=>n,gm:()=>o});var r=a(49526),s=a(6710);class i{async getEmailConfig(){try{let t=await s.T8.getEmailSettings();if(!t||!t.smtpHost||!t.smtpUser||!t.smtpPassword)return console.warn("Email configuration not found or incomplete"),null;return{host:t.smtpHost,port:t.smtpPort||587,secure:t.smtpSecure||!1,user:t.smtpUser,password:t.smtpPassword,fromName:t.fromName||"HashCoreX",fromEmail:t.fromEmail||t.smtpUser}}catch(t){return console.error("Failed to get email configuration:",t),null}}async initializeTransporter(){try{if(this.config=await this.getEmailConfig(),!this.config)return!1;return this.transporter=r.createTransport({host:this.config.host,port:this.config.port,secure:this.config.secure,auth:{user:this.config.user,pass:this.config.password},tls:{rejectUnauthorized:!1}}),await this.transporter.verify(),console.log("Email transporter initialized successfully"),!0}catch(t){return console.error("Failed to initialize email transporter:",t),this.transporter=null,!1}}async sendEmail(t){try{if((!this.transporter||!this.config)&&!await this.initializeTransporter())throw Error("Email service not configured");let e={from:`"${this.config.fromName}" <${this.config.fromEmail}>`,to:t.to,subject:t.subject,html:t.html,text:t.text},a=await this.transporter.sendMail(e);return console.log("Email sent successfully:",a.messageId),!0}catch(t){return console.error("Failed to send email:",t),!1}}async sendOTPEmail(t,e,a,r="email_verification"){let s="password_reset"===r?"password_reset_otp":"otp_verification",i=await this.getEmailTemplate(s);if(!i)return console.error(`Email template '${s}' not found. Please ensure email templates are seeded.`),!1;let o=i.htmlContent,n=i.textContent||"";return o=(o=o.replace(/{{firstName}}/g,a||"User")).replace(/{{otp}}/g,e),n=(n=n.replace(/{{firstName}}/g,a||"User")).replace(/{{otp}}/g,e),await this.sendEmail({to:t,subject:i.subject,html:o,text:n})}async getEmailTemplate(t){try{return await s.T8.getEmailTemplate(t)}catch(t){return console.error("Failed to get email template:",t),null}}async testConnection(){try{if(this.config=await this.getEmailConfig(),!this.config)return console.error("Email configuration not found or incomplete"),!1;return this.transporter=r.createTransport({host:this.config.host,port:this.config.port,secure:this.config.secure,auth:{user:this.config.user,pass:this.config.password},tls:{rejectUnauthorized:!1}}),await this.transporter.verify(),console.log("Email connection test successful"),!0}catch(t){throw console.error("Email connection test failed:",t),this.transporter=null,t}}constructor(){this.transporter=null,this.config=null}}let o=new i,n=()=>Math.floor(1e5+9e5*Math.random()).toString()}};