"use strict";(()=>{var e={};e.id=7368,e.ids=[7368],e.modules={3295:e=>{e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},10994:(e,r,t)=>{t.r(r),t.d(r,{patchFetch:()=>h,routeModule:()=>p,serverHooks:()=>y,workAsyncStorage:()=>m,workUnitAsyncStorage:()=>f});var s={};t.r(s),t.d(s,{POST:()=>c});var a=t(96559),i=t(48088),n=t(37719),o=t(32190),u=t(12909),d=t(31183),l=t(6710);async function c(e){try{let{authenticated:r,user:t}=await (0,u.b9)(e);if(!r||!t)return o.NextResponse.json({success:!1,error:"Not authenticated"},{status:401});if("APPROVED"===t.kycStatus)return o.NextResponse.json({success:!1,error:"KYC already approved"},{status:400});if("PENDING"===t.kycStatus)return o.NextResponse.json({success:!1,error:"KYC submission already pending review"},{status:400});let s=await d.prisma.kYCDocument.findMany({where:{userId:t.id}});if(0===s.length)return o.NextResponse.json({success:!1,error:"No documents uploaded"},{status:400});let a=s.some(e=>"SELFIE"===e.documentType);if(!a)return o.NextResponse.json({success:!1,error:"Selfie document is required"},{status:400});let i=s.filter(e=>"ID_DOCUMENT"===e.documentType);if(0===i.length)return o.NextResponse.json({success:!1,error:"ID document is required"},{status:400});let n=i[0].idType,c=!1;if("PASSPORT"===n)c=i.some(e=>"FRONT"===e.documentSide);else{let e=i.some(e=>"FRONT"===e.documentSide),r=i.some(e=>"BACK"===e.documentSide);c=e&&r}if(!c)return o.NextResponse.json({success:!1,error:"PASSPORT"===n?"Passport front side is required":"Both front and back sides of ID are required"},{status:400});return await d.prisma.user.update({where:{id:t.id},data:{kycStatus:"PENDING"}}),await d.prisma.kYCDocument.updateMany({where:{userId:t.id},data:{status:"PENDING"}}),await l.AJ.create({action:"KYC_SUBMITTED",userId:t.id,details:{documentsCount:s.length,idType:n,hasCompleteIdDocuments:c,hasSelfie:a},ipAddress:e.headers.get("x-forwarded-for")||"unknown",userAgent:e.headers.get("user-agent")||"unknown"}),o.NextResponse.json({success:!0,message:"KYC submitted successfully for review",data:{status:"PENDING",documentsCount:s.length,submittedAt:new Date().toISOString()}})}catch(e){return console.error("KYC submission error:",e),o.NextResponse.json({success:!1,error:"Failed to submit KYC"},{status:500})}}let p=new a.AppRouteRouteModule({definition:{kind:i.RouteKind.APP_ROUTE,page:"/api/kyc/submit/route",pathname:"/api/kyc/submit",filename:"route",bundlePath:"app/api/kyc/submit/route"},resolvedPagePath:"C:\\Users\\<USER>\\Desktop\\Hash_Minings\\hashcorex\\src\\app\\api\\kyc\\submit\\route.ts",nextConfigOutput:"",userland:s}),{workAsyncStorage:m,workUnitAsyncStorage:f,serverHooks:y}=p;function h(){return(0,n.patchFetch)({workAsyncStorage:m,workUnitAsyncStorage:f})}},12909:(e,r,t)=>{t.d(r,{DT:()=>w,DY:()=>y,HU:()=>c,Lx:()=>h,b9:()=>f,qc:()=>x});var s=t(85663),a=t(43205),i=t.n(a),n=t(6710);let o=process.env.JWT_SECRET||"fallback-secret-key",u=process.env.JWT_EXPIRES_IN||"30d",d=async e=>await s.Ay.hash(e,12),l=async(e,r)=>await s.Ay.compare(e,r),c=e=>i().sign(e,o,{expiresIn:u}),p=e=>{try{return i().verify(e,o)}catch(e){return null}},m=()=>{let e="ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789",r="HC";for(let t=0;t<8;t++)r+=e.charAt(Math.floor(Math.random()*e.length));return r},f=async e=>{let r=e.headers.get("authorization")?.replace("Bearer ","")||e.cookies.get("auth-token")?.value;if(!r)return{authenticated:!1,user:null};let t=p(r);if(!t)return{authenticated:!1,user:null};let s=await n.Gy.findByEmail(t.email);return s?{authenticated:!0,user:s}:{authenticated:!1,user:null}},y=async e=>{let r,s;if(await n.Gy.findByEmail(e.email))throw Error("User already exists with this email");if(e.referralCode){let t=await n.Gy.findByReferralId(e.referralCode);if(!t)throw Error("Invalid referral code");r=t.id}let a=await d(e.password),i=!1;do s=m(),i=!await n.Gy.findByReferralId(s);while(!i);let o=await n.Gy.create({email:e.email,firstName:e.firstName,lastName:e.lastName,password:a,referralId:s});if(r){let{placeUserByReferralType:s}=await t.e(2746).then(t.bind(t,2746)),a="general";"left"===e.placementSide?a="left":"right"===e.placementSide&&(a="right"),await s(r,o.id,a)}return{id:o.id,email:o.email,referralId:o.referralId,kycStatus:o.kycStatus}},h=async e=>{let r=await n.Gy.findByEmail(e.email);if(!r||!await l(e.password,r.password))throw Error("Invalid email or password");return{token:c({userId:r.id,email:r.email}),user:{id:r.id,email:r.email,referralId:r.referralId,kycStatus:r.kycStatus}}},w=e=>/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(e),x=async e=>{let r=await n.Gy.findById(e);return r?.role==="ADMIN"}},27910:e=>{e.exports=require("stream")},28354:e=>{e.exports=require("util")},29294:e=>{e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},44870:e=>{e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},55511:e=>{e.exports=require("crypto")},63033:e=>{e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},79428:e=>{e.exports=require("buffer")},96330:e=>{e.exports=require("@prisma/client")}};var r=require("../../../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),s=r.X(0,[4447,580,5315,3306],()=>t(10994));module.exports=s})();