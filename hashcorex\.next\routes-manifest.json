{"version": 3, "pages404": true, "caseSensitive": false, "basePath": "", "redirects": [{"source": "/:path+/", "destination": "/:path+", "internal": true, "statusCode": 308, "regex": "^(?:/((?:[^/]+?)(?:/(?:[^/]+?))*))/$"}], "headers": [], "dynamicRoutes": [{"page": "/api/admin/binary-points/user-history/[userId]", "regex": "^/api/admin/binary\\-points/user\\-history/([^/]+?)(?:/)?$", "routeKeys": {"nxtPuserId": "nxtPuserId"}, "namedRegex": "^/api/admin/binary\\-points/user\\-history/(?<nxtPuserId>[^/]+?)(?:/)?$"}, {"page": "/api/admin/email-templates/[name]", "regex": "^/api/admin/email\\-templates/([^/]+?)(?:/)?$", "routeKeys": {"nxtPname": "nxtPname"}, "namedRegex": "^/api/admin/email\\-templates/(?<nxtPname>[^/]+?)(?:/)?$"}, {"page": "/api/admin/support/tickets/[ticketId]", "regex": "^/api/admin/support/tickets/([^/]+?)(?:/)?$", "routeKeys": {"nxtPticketId": "nxtPticketId"}, "namedRegex": "^/api/admin/support/tickets/(?<nxtPticketId>[^/]+?)(?:/)?$"}, {"page": "/api/admin/support/tickets/[ticketId]/responses", "regex": "^/api/admin/support/tickets/([^/]+?)/responses(?:/)?$", "routeKeys": {"nxtPticketId": "nxtPticketId"}, "namedRegex": "^/api/admin/support/tickets/(?<nxtPticketId>[^/]+?)/responses(?:/)?$"}, {"page": "/api/admin/users/[userId]/details", "regex": "^/api/admin/users/([^/]+?)/details(?:/)?$", "routeKeys": {"nxtPuserId": "nxtPuserId"}, "namedRegex": "^/api/admin/users/(?<nxtPuserId>[^/]+?)/details(?:/)?$"}, {"page": "/api/mining-units/[id]/earnings", "regex": "^/api/mining\\-units/([^/]+?)/earnings(?:/)?$", "routeKeys": {"nxtPid": "nxtPid"}, "namedRegex": "^/api/mining\\-units/(?<nxtPid>[^/]+?)/earnings(?:/)?$"}, {"page": "/api/support/tickets/[ticketId]/responses", "regex": "^/api/support/tickets/([^/]+?)/responses(?:/)?$", "routeKeys": {"nxtPticketId": "nxtPticketId"}, "namedRegex": "^/api/support/tickets/(?<nxtPticketId>[^/]+?)/responses(?:/)?$"}, {"page": "/api/wallet/transactions/[id]", "regex": "^/api/wallet/transactions/([^/]+?)(?:/)?$", "routeKeys": {"nxtPid": "nxtPid"}, "namedRegex": "^/api/wallet/transactions/(?<nxtPid>[^/]+?)(?:/)?$"}], "staticRoutes": [{"page": "/", "regex": "^/(?:/)?$", "routeKeys": {}, "namedRegex": "^/(?:/)?$"}, {"page": "/_not-found", "regex": "^/_not\\-found(?:/)?$", "routeKeys": {}, "namedRegex": "^/_not\\-found(?:/)?$"}, {"page": "/about", "regex": "^/about(?:/)?$", "routeKeys": {}, "namedRegex": "^/about(?:/)?$"}, {"page": "/admin", "regex": "^/admin(?:/)?$", "routeKeys": {}, "namedRegex": "^/admin(?:/)?$"}, {"page": "/contact", "regex": "^/contact(?:/)?$", "routeKeys": {}, "namedRegex": "^/contact(?:/)?$"}, {"page": "/dashboard", "regex": "^/dashboard(?:/)?$", "routeKeys": {}, "namedRegex": "^/dashboard(?:/)?$"}, {"page": "/favicon.ico", "regex": "^/favicon\\.ico(?:/)?$", "routeKeys": {}, "namedRegex": "^/favicon\\.ico(?:/)?$"}, {"page": "/forgot-password", "regex": "^/forgot\\-password(?:/)?$", "routeKeys": {}, "namedRegex": "^/forgot\\-password(?:/)?$"}, {"page": "/how-it-works", "regex": "^/how\\-it\\-works(?:/)?$", "routeKeys": {}, "namedRegex": "^/how\\-it\\-works(?:/)?$"}, {"page": "/login", "regex": "^/login(?:/)?$", "routeKeys": {}, "namedRegex": "^/login(?:/)?$"}, {"page": "/privacy", "regex": "^/privacy(?:/)?$", "routeKeys": {}, "namedRegex": "^/privacy(?:/)?$"}, {"page": "/register", "regex": "^/register(?:/)?$", "routeKeys": {}, "namedRegex": "^/register(?:/)?$"}, {"page": "/terms", "regex": "^/terms(?:/)?$", "routeKeys": {}, "namedRegex": "^/terms(?:/)?$"}], "dataRoutes": [], "rsc": {"header": "RSC", "varyHeader": "RSC, Next-Router-State-Tree, Next-Router-Prefetch, Next-Router-Segment-Prefetch", "prefetchHeader": "Next-Router-Prefetch", "didPostponeHeader": "x-nextjs-postponed", "contentTypeHeader": "text/x-component", "suffix": ".rsc", "prefetchSuffix": ".prefetch.rsc", "prefetchSegmentHeader": "Next-Router-Segment-Prefetch", "prefetchSegmentSuffix": ".segment.rsc", "prefetchSegmentDirSuffix": ".segments"}, "rewriteHeaders": {"pathHeader": "x-nextjs-rewritten-path", "queryHeader": "x-nextjs-rewritten-query"}, "rewrites": []}