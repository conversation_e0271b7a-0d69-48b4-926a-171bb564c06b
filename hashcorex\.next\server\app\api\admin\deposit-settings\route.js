"use strict";(()=>{var e={};e.id=218,e.ids=[218],e.modules={3295:e=>{e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},8023:(e,t,r)=>{r.r(t),r.d(t,{patchFetch:()=>h,routeModule:()=>E,serverHooks:()=>g,workAsyncStorage:()=>S,workUnitAsyncStorage:()=>f});var s={};r.r(s),r.d(s,{GET:()=>p,POST:()=>c,PUT:()=>m});var a=r(96559),i=r(48088),n=r(37719),o=r(32190),u=r(12909),d=r(6710),l=r(59480);async function p(e){try{let{authenticated:t,user:r}=await (0,u.b9)(e);if(!t||!r)return o.NextResponse.json({success:!1,error:"Not authenticated"},{status:401});if(!await (0,u.qc)(r.id))return o.NextResponse.json({success:!1,error:"Admin access required"},{status:403});let s=await d.rs.get("USDT_DEPOSIT_ADDRESS"),a=await d.rs.get("MIN_DEPOSIT_AMOUNT"),i=await d.rs.get("MAX_DEPOSIT_AMOUNT"),n=await d.rs.get("DEPOSIT_ENABLED"),l=await d.rs.get("MIN_CONFIRMATIONS"),p=await d.rs.get("DEPOSIT_FEE_PERCENTAGE");return o.NextResponse.json({success:!0,data:{depositAddress:s||"",minDepositAmount:parseFloat(a||"10"),maxDepositAmount:parseFloat(i||"10000"),depositEnabled:"true"===n,minConfirmations:parseInt(l||"1"),depositFeePercentage:parseFloat(p||"0")}})}catch(e){return console.error("Deposit settings fetch error:",e),o.NextResponse.json({success:!1,error:"Failed to fetch deposit settings"},{status:500})}}async function c(e){try{let{authenticated:t,user:r}=await (0,u.b9)(e);if(!t||!r)return o.NextResponse.json({success:!1,error:"Not authenticated"},{status:401});if(!await (0,u.qc)(r.id))return o.NextResponse.json({success:!1,error:"Admin access required"},{status:403});let{depositAddress:s,minDepositAmount:a,maxDepositAmount:i,depositEnabled:n,minConfirmations:p,depositFeePercentage:c}=await e.json(),m=[];if(s&&!(0,l.af)(s)&&m.push("Invalid USDT TRC20 address format"),void 0!==a&&(a<0||a>1e6)&&m.push("Minimum deposit amount must be between 0 and 1,000,000"),void 0!==i&&(i<0||i>1e6)&&m.push("Maximum deposit amount must be between 0 and 1,000,000"),void 0!==a&&void 0!==i&&a>i&&m.push("Minimum deposit amount cannot be greater than maximum deposit amount"),void 0!==p&&(p<0||p>100)&&m.push("Minimum confirmations must be between 0 and 100"),void 0!==c&&(c<0||c>50)&&m.push("Deposit fee percentage must be between 0 and 50"),m.length>0)return o.NextResponse.json({success:!1,error:m.join(", ")},{status:400});let E=[];return void 0!==s&&(await d.rs.set("USDT_DEPOSIT_ADDRESS",s),E.push({key:"USDT_DEPOSIT_ADDRESS",value:s})),void 0!==a&&(await d.rs.set("MIN_DEPOSIT_AMOUNT",a.toString()),E.push({key:"MIN_DEPOSIT_AMOUNT",value:a.toString()})),void 0!==i&&(await d.rs.set("MAX_DEPOSIT_AMOUNT",i.toString()),E.push({key:"MAX_DEPOSIT_AMOUNT",value:i.toString()})),void 0!==n&&(await d.rs.set("DEPOSIT_ENABLED",n.toString()),E.push({key:"DEPOSIT_ENABLED",value:n.toString()})),void 0!==p&&(await d.rs.set("MIN_CONFIRMATIONS",p.toString()),E.push({key:"MIN_CONFIRMATIONS",value:p.toString()})),void 0!==c&&(await d.rs.set("DEPOSIT_FEE_PERCENTAGE",c.toString()),E.push({key:"DEPOSIT_FEE_PERCENTAGE",value:c.toString()})),await d.AJ.create({action:"DEPOSIT_SETTINGS_UPDATED",adminId:r.id,details:{updates:E},ipAddress:e.headers.get("x-forwarded-for")||e.headers.get("x-real-ip")||"unknown",userAgent:e.headers.get("user-agent")||"unknown"}),o.NextResponse.json({success:!0,message:"Deposit settings updated successfully"})}catch(e){return console.error("Deposit settings update error:",e),o.NextResponse.json({success:!1,error:"Failed to update deposit settings"},{status:500})}}async function m(e){try{let{authenticated:t,user:r}=await (0,u.b9)(e);if(!t||!r)return o.NextResponse.json({success:!1,error:"Not authenticated"},{status:401});if(!await (0,u.qc)(r.id))return o.NextResponse.json({success:!1,error:"Admin access required"},{status:403});let s=[{key:"USDT_DEPOSIT_ADDRESS",value:""},{key:"MIN_DEPOSIT_AMOUNT",value:"10"},{key:"MAX_DEPOSIT_AMOUNT",value:"10000"},{key:"DEPOSIT_ENABLED",value:"true"},{key:"MIN_CONFIRMATIONS",value:"1"},{key:"DEPOSIT_FEE_PERCENTAGE",value:"0"}];for(let e of s)await d.rs.set(e.key,e.value);return await d.AJ.create({action:"DEPOSIT_SETTINGS_RESET",adminId:r.id,details:{defaultSettings:s},ipAddress:e.headers.get("x-forwarded-for")||e.headers.get("x-real-ip")||"unknown",userAgent:e.headers.get("user-agent")||"unknown"}),o.NextResponse.json({success:!0,message:"Deposit settings reset to defaults"})}catch(e){return console.error("Deposit settings reset error:",e),o.NextResponse.json({success:!1,error:"Failed to reset deposit settings"},{status:500})}}let E=new a.AppRouteRouteModule({definition:{kind:i.RouteKind.APP_ROUTE,page:"/api/admin/deposit-settings/route",pathname:"/api/admin/deposit-settings",filename:"route",bundlePath:"app/api/admin/deposit-settings/route"},resolvedPagePath:"C:\\Users\\<USER>\\Desktop\\Hash_Minings\\hashcorex\\src\\app\\api\\admin\\deposit-settings\\route.ts",nextConfigOutput:"",userland:s}),{workAsyncStorage:S,workUnitAsyncStorage:f,serverHooks:g}=E;function h(){return(0,n.patchFetch)({workAsyncStorage:S,workUnitAsyncStorage:f})}},10846:e=>{e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},12909:(e,t,r)=>{r.d(t,{DT:()=>g,DY:()=>S,HU:()=>p,Lx:()=>f,b9:()=>E,qc:()=>h});var s=r(85663),a=r(43205),i=r.n(a),n=r(6710);let o=process.env.JWT_SECRET||"fallback-secret-key",u=process.env.JWT_EXPIRES_IN||"30d",d=async e=>await s.Ay.hash(e,12),l=async(e,t)=>await s.Ay.compare(e,t),p=e=>i().sign(e,o,{expiresIn:u}),c=e=>{try{return i().verify(e,o)}catch(e){return null}},m=()=>{let e="ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789",t="HC";for(let r=0;r<8;r++)t+=e.charAt(Math.floor(Math.random()*e.length));return t},E=async e=>{let t=e.headers.get("authorization")?.replace("Bearer ","")||e.cookies.get("auth-token")?.value;if(!t)return{authenticated:!1,user:null};let r=c(t);if(!r)return{authenticated:!1,user:null};let s=await n.Gy.findByEmail(r.email);return s?{authenticated:!0,user:s}:{authenticated:!1,user:null}},S=async e=>{let t,s;if(await n.Gy.findByEmail(e.email))throw Error("User already exists with this email");if(e.referralCode){let r=await n.Gy.findByReferralId(e.referralCode);if(!r)throw Error("Invalid referral code");t=r.id}let a=await d(e.password),i=!1;do s=m(),i=!await n.Gy.findByReferralId(s);while(!i);let o=await n.Gy.create({email:e.email,firstName:e.firstName,lastName:e.lastName,password:a,referralId:s});if(t){let{placeUserByReferralType:s}=await r.e(2746).then(r.bind(r,2746)),a="general";"left"===e.placementSide?a="left":"right"===e.placementSide&&(a="right"),await s(t,o.id,a)}return{id:o.id,email:o.email,referralId:o.referralId,kycStatus:o.kycStatus}},f=async e=>{let t=await n.Gy.findByEmail(e.email);if(!t||!await l(e.password,t.password))throw Error("Invalid email or password");return{token:p({userId:t.id,email:t.email}),user:{id:t.id,email:t.email,referralId:t.referralId,kycStatus:t.kycStatus}}},g=e=>/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(e),h=async e=>{let t=await n.Gy.findById(e);return t?.role==="ADMIN"}},27910:e=>{e.exports=require("stream")},28354:e=>{e.exports=require("util")},29294:e=>{e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},44870:e=>{e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},55511:e=>{e.exports=require("crypto")},63033:e=>{e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},79428:e=>{e.exports=require("buffer")},96330:e=>{e.exports=require("@prisma/client")}};var t=require("../../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),s=t.X(0,[4447,580,5315,3306,9480],()=>r(8023));module.exports=s})();