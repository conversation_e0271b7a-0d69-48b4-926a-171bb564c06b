"use strict";(()=>{var e={};e.id=5533,e.ids=[5533],e.modules={3295:e=>{e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},12909:(e,r,t)=>{t.d(r,{DT:()=>y,DY:()=>f,HU:()=>p,Lx:()=>m,b9:()=>h,qc:()=>g});var a=t(85663),s=t(43205),i=t.n(s),n=t(6710);let l=process.env.JWT_SECRET||"fallback-secret-key",o=process.env.JWT_EXPIRES_IN||"30d",d=async e=>await a.Ay.hash(e,12),u=async(e,r)=>await a.Ay.compare(e,r),p=e=>i().sign(e,l,{expiresIn:o}),c=e=>{try{return i().verify(e,l)}catch(e){return null}},w=()=>{let e="ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789",r="HC";for(let t=0;t<8;t++)r+=e.charAt(Math.floor(Math.random()*e.length));return r},h=async e=>{let r=e.headers.get("authorization")?.replace("Bearer ","")||e.cookies.get("auth-token")?.value;if(!r)return{authenticated:!1,user:null};let t=c(r);if(!t)return{authenticated:!1,user:null};let a=await n.Gy.findByEmail(t.email);return a?{authenticated:!0,user:a}:{authenticated:!1,user:null}},f=async e=>{let r,a;if(await n.Gy.findByEmail(e.email))throw Error("User already exists with this email");if(e.referralCode){let t=await n.Gy.findByReferralId(e.referralCode);if(!t)throw Error("Invalid referral code");r=t.id}let s=await d(e.password),i=!1;do a=w(),i=!await n.Gy.findByReferralId(a);while(!i);let l=await n.Gy.create({email:e.email,firstName:e.firstName,lastName:e.lastName,password:s,referralId:a});if(r){let{placeUserByReferralType:a}=await t.e(2746).then(t.bind(t,2746)),s="general";"left"===e.placementSide?s="left":"right"===e.placementSide&&(s="right"),await a(r,l.id,s)}return{id:l.id,email:l.email,referralId:l.referralId,kycStatus:l.kycStatus}},m=async e=>{let r=await n.Gy.findByEmail(e.email);if(!r||!await u(e.password,r.password))throw Error("Invalid email or password");return{token:p({userId:r.id,email:r.email}),user:{id:r.id,email:r.email,referralId:r.referralId,kycStatus:r.kycStatus}}},y=e=>/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(e),g=async e=>{let r=await n.Gy.findById(e);return r?.role==="ADMIN"}},27910:e=>{e.exports=require("stream")},28354:e=>{e.exports=require("util")},29236:(e,r,t)=>{t.r(r),t.d(r,{patchFetch:()=>f,routeModule:()=>p,serverHooks:()=>h,workAsyncStorage:()=>c,workUnitAsyncStorage:()=>w});var a={};t.r(a),t.d(a,{GET:()=>u});var s=t(96559),i=t(48088),n=t(37719),l=t(32190),o=t(12909),d=t(6710);async function u(e){try{let{authenticated:r,user:t}=await (0,o.b9)(e);if(!r||!t)return l.NextResponse.json({success:!1,error:"Not authenticated"},{status:401});let a=parseFloat(await d.rs.get("minWithdrawalAmount")||"10"),s=parseFloat(await d.rs.get("withdrawalFeeFixed")||"3"),i=parseFloat(await d.rs.get("withdrawalFeePercentage")||"1"),n=parseInt(await d.rs.get("withdrawalProcessingDays")||"3");return l.NextResponse.json({success:!0,data:{minWithdrawalAmount:a,fixedFee:s,percentageFee:i,processingDays:n}})}catch(e){return console.error("Withdrawal settings fetch error:",e),l.NextResponse.json({success:!1,error:"Failed to fetch withdrawal settings"},{status:500})}}let p=new s.AppRouteRouteModule({definition:{kind:i.RouteKind.APP_ROUTE,page:"/api/wallet/withdrawal-settings/route",pathname:"/api/wallet/withdrawal-settings",filename:"route",bundlePath:"app/api/wallet/withdrawal-settings/route"},resolvedPagePath:"C:\\Users\\<USER>\\Desktop\\Hash_Minings\\hashcorex\\src\\app\\api\\wallet\\withdrawal-settings\\route.ts",nextConfigOutput:"",userland:a}),{workAsyncStorage:c,workUnitAsyncStorage:w,serverHooks:h}=p;function f(){return(0,n.patchFetch)({workAsyncStorage:c,workUnitAsyncStorage:w})}},29294:e=>{e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},44870:e=>{e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},55511:e=>{e.exports=require("crypto")},63033:e=>{e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},79428:e=>{e.exports=require("buffer")},96330:e=>{e.exports=require("@prisma/client")}};var r=require("../../../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),a=r.X(0,[4447,580,5315,3306],()=>t(29236));module.exports=a})();