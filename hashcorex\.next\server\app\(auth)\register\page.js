(()=>{var e={};e.id=2678,e.ids=[2678],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},12597:(e,s,t)=>{"use strict";t.d(s,{A:()=>r});let r=(0,t(62688).A)("eye-off",[["path",{d:"M10.733 5.076a10.744 10.744 0 0 1 11.205 6.575 1 1 0 0 1 0 .696 10.747 10.747 0 0 1-1.444 2.49",key:"ct8e1f"}],["path",{d:"M14.084 14.158a3 3 0 0 1-4.242-4.242",key:"151rxh"}],["path",{d:"M17.479 17.499a10.75 10.75 0 0 1-15.417-5.151 1 1 0 0 1 0-.696 10.75 10.75 0 0 1 4.446-5.143",key:"13bj9a"}],["path",{d:"m2 2 20 20",key:"1ooewy"}]])},13861:(e,s,t)=>{"use strict";t.d(s,{A:()=>r});let r=(0,t(62688).A)("eye",[["path",{d:"M2.062 12.348a1 1 0 0 1 0-.696 10.75 10.75 0 0 1 19.876 0 1 1 0 0 1 0 .696 10.75 10.75 0 0 1-19.876 0",key:"1nclc0"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]])},13964:(e,s,t)=>{"use strict";t.d(s,{A:()=>r});let r=(0,t(62688).A)("check",[["path",{d:"M20 6 9 17l-5-5",key:"1gmf2c"}]])},16189:(e,s,t)=>{"use strict";var r=t(65773);t.o(r,"usePathname")&&t.d(s,{usePathname:function(){return r.usePathname}}),t.o(r,"useRouter")&&t.d(s,{useRouter:function(){return r.useRouter}}),t.o(r,"useSearchParams")&&t.d(s,{useSearchParams:function(){return r.useSearchParams}})},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},20666:(e,s,t)=>{"use strict";t.d(s,{CU:()=>o});var r=t(60687),a=t(43210),l=t(16189),i=t(57445),n=t(77849);let d=({children:e,requireAuth:s=!1,requireGuest:t=!1,redirectTo:d})=>{let{user:o,loading:c}=(0,i.A)(),m=(0,l.useRouter)(),x=(0,l.usePathname)();return((0,a.useEffect)(()=>{if(!c){if(s&&!o){let e=`/login?redirect=${encodeURIComponent(x)}`;m.replace(e);return}if(t&&o){let e=d||"/dashboard";m.replace(e);return}}},[o,c,s,t,m,x,d]),c)?(0,r.jsx)("div",{className:"min-h-screen flex items-center justify-center bg-gray-50",children:(0,r.jsxs)("div",{className:"text-center",children:[(0,r.jsx)(n.Rh,{}),(0,r.jsx)("p",{className:"mt-4 text-gray-600",children:"Checking authentication..."})]})}):s&&!o||t&&o?null:(0,r.jsx)(r.Fragment,{children:e})},o=({children:e,redirectTo:s})=>(0,r.jsx)(d,{requireGuest:!0,redirectTo:s,children:e})},28559:(e,s,t)=>{"use strict";t.d(s,{A:()=>r});let r=(0,t(62688).A)("arrow-left",[["path",{d:"m12 19-7-7 7-7",key:"1l729n"}],["path",{d:"M19 12H5",key:"x3x0zl"}]])},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},38134:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>y});var r=t(60687),a=t(43210),l=t(85814),i=t.n(l),n=t(16189),d=t(77849),o=t(71180),c=t(12597),m=t(13861),x=t(13964),h=t(28559),u=t(57445),p=t(4780),f=t(20666),g=t(41550),j=t(5336),b=t(78122);let v=({email:e,firstName:s,onVerified:t,onResend:l,loading:i=!1,error:n})=>{let[o,c]=(0,a.useState)(["","","","","",""]),[m,x]=(0,a.useState)(600),[h,u]=(0,a.useState)(!1),[p,f]=(0,a.useState)(!1),v=(0,a.useRef)([]);(0,a.useEffect)(()=>{let e=setInterval(()=>{x(e=>e<=1?(u(!0),0):e-1)},1e3);return()=>clearInterval(e)},[]);let N=(e,s)=>{if(s.length>1)return;let r=[...o];r[e]=s,c(r),s&&e<5&&v.current[e+1]?.focus(),r.every(e=>""!==e)&&6===r.join("").length&&t(r.join(""))},y=(e,s)=>{"Backspace"===s.key&&!o[e]&&e>0&&v.current[e-1]?.focus()},w=e=>{e.preventDefault();let s=e.clipboardData.getData("text").replace(/\D/g,"").slice(0,6);6===s.length&&(c(s.split("")),t(s))},k=async()=>{f(!0),u(!1),x(600),await l(),f(!1)};return(0,r.jsxs)("div",{className:"max-w-md mx-auto",children:[(0,r.jsxs)("div",{className:"text-center mb-8",children:[(0,r.jsx)("div",{className:"w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-4",children:(0,r.jsx)(g.A,{className:"w-8 h-8 text-green-600"})}),(0,r.jsx)("h2",{className:"text-2xl font-bold text-gray-900 mb-2",children:"Verify Your Email"}),(0,r.jsx)("p",{className:"text-gray-600",children:"We've sent a 6-digit verification code to"}),(0,r.jsx)("p",{className:"font-semibold text-gray-900",children:e})]}),(0,r.jsxs)("div",{className:"space-y-6",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-3 text-center",children:"Enter verification code"}),(0,r.jsx)("div",{className:"flex justify-center space-x-3",children:o.map((e,s)=>(0,r.jsx)(d.pd,{ref:e=>v.current[s]=e,type:"text",inputMode:"numeric",maxLength:1,value:e,onChange:e=>N(s,e.target.value),onKeyDown:e=>y(s,e),onPaste:w,className:"w-12 h-12 text-center text-lg font-semibold border-2 focus:border-green-500",disabled:i},s))})]}),n&&(0,r.jsx)("div",{className:"bg-red-50 border border-red-200 rounded-lg p-3",children:(0,r.jsx)("p",{className:"text-sm text-red-600 text-center",children:n})}),(0,r.jsx)("div",{className:"text-center",children:m>0?(0,r.jsxs)("p",{className:"text-sm text-gray-600",children:["Code expires in ",(0,r.jsx)("span",{className:"font-semibold text-green-600",children:(e=>{let s=Math.floor(e/60);return`${s}:${(e%60).toString().padStart(2,"0")}`})(m)})]}):(0,r.jsx)("p",{className:"text-sm text-red-600",children:"Code has expired"})}),(0,r.jsx)(d.$n,{onClick:()=>{let e=o.join("");6===e.length&&t(e)},disabled:i||o.some(e=>""===e),className:"w-full bg-green-600 hover:bg-green-700 text-white",children:i?(0,r.jsxs)("div",{className:"flex items-center justify-center",children:[(0,r.jsx)("div",{className:"animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"}),"Verifying..."]}):(0,r.jsxs)("div",{className:"flex items-center justify-center",children:[(0,r.jsx)(j.A,{className:"w-4 h-4 mr-2"}),"Verify Code"]})}),(0,r.jsxs)("div",{className:"text-center",children:[(0,r.jsx)("p",{className:"text-sm text-gray-600 mb-2",children:"Didn't receive the code?"}),(0,r.jsx)(d.$n,{onClick:k,disabled:!h||p,variant:"ghost",className:"text-green-600 hover:text-green-700",children:p?(0,r.jsxs)("div",{className:"flex items-center",children:[(0,r.jsx)(b.A,{className:"w-4 h-4 mr-2 animate-spin"}),"Sending..."]}):(0,r.jsxs)("div",{className:"flex items-center",children:[(0,r.jsx)(b.A,{className:"w-4 h-4 mr-2"}),"Resend Code"]})})]}),(0,r.jsxs)("div",{className:"bg-blue-50 border border-blue-200 rounded-lg p-4",children:[(0,r.jsx)("p",{className:"text-sm text-blue-700",children:(0,r.jsx)("strong",{children:"Tips:"})}),(0,r.jsxs)("ul",{className:"text-sm text-blue-600 mt-1 space-y-1",children:[(0,r.jsx)("li",{children:"• Check your spam/junk folder"}),(0,r.jsxs)("li",{children:["• Make sure ",e," is correct"]}),(0,r.jsx)("li",{children:"• You can paste the code from your email"})]})]})]})]})};function N(){let e=(0,n.useRouter)(),s=(0,n.useSearchParams)(),{register:t,loading:l}=(0,u.A)(),[f,g]=(0,a.useState)({firstName:"",lastName:"",email:"",password:"",confirmPassword:"",referralCode:s.get("ref")||""}),[j,b]=(0,a.useState)(!1),[N,y]=(0,a.useState)(!1),[w,k]=(0,a.useState)(""),[P,C]=(0,a.useState)({isValid:!1,errors:[]}),[A,S]=(0,a.useState)("form"),[M,_]=(0,a.useState)(!1),[q,E]=(0,a.useState)(!1),R=async e=>(e.preventDefault(),k(""),f.password!==f.confirmPassword)?void k("Passwords do not match"):P.isValid?void await D():void k("Please ensure your password meets all requirements"),D=async()=>{try{E(!0),k("");let e=await fetch("/api/auth/send-otp",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({email:f.email,firstName:f.firstName})}),s=await e.json();s.success?(_(!0),S("otp")):k(s.error||"Failed to send OTP")}catch(e){k("Failed to send OTP. Please try again.")}finally{E(!1)}},F=async r=>{try{k("");let a=s.get("side");await t(f.email,f.firstName,f.lastName,f.password,f.confirmPassword,f.referralCode,a||void 0,r);let l=s.get("redirect");l&&l.startsWith("/")?e.push(l):e.push("/dashboard")}catch(e){k(e instanceof Error?e.message:"Registration failed")}},H=e=>{let{name:s,value:t}=e.target;g(e=>({...e,[s]:t})),"password"===s&&C((0,p.Oj)(t))};return(0,r.jsxs)("div",{className:"min-h-screen relative overflow-hidden",children:[(0,r.jsx)("div",{className:"absolute inset-0 animated-gradient opacity-30"}),(0,r.jsx)("div",{className:"absolute inset-0 bg-gradient-to-br from-white/95 via-solar-50/90 to-eco-50/95"}),(0,r.jsx)("div",{className:"absolute top-20 left-10 w-16 h-16 bg-solar-400/20 rounded-full animate-float"}),(0,r.jsx)("div",{className:"absolute top-40 right-20 w-12 h-12 bg-eco-400/20 rounded-full animate-float",style:{animationDelay:"2s"}}),(0,r.jsx)("div",{className:"absolute bottom-40 left-20 w-10 h-10 bg-purple-400/20 rounded-full animate-float",style:{animationDelay:"4s"}}),(0,r.jsxs)("div",{className:"relative z-10 min-h-screen flex",children:[(0,r.jsx)("div",{className:"hidden lg:flex lg:w-1/2 xl:w-3/5 bg-gradient-to-br from-solar-500/10 to-eco-500/10 items-center justify-center p-12",children:(0,r.jsxs)("div",{className:"max-w-lg text-center",children:[(0,r.jsx)("div",{className:"mb-8",children:(0,r.jsxs)("div",{className:"relative inline-block",children:[(0,r.jsx)("div",{className:"absolute inset-0 bg-solar-500/20 rounded-full animate-ping"}),(0,r.jsx)(o.MX,{className:"relative h-20 w-20 text-solar-500"})]})}),(0,r.jsxs)("h1",{className:"text-4xl xl:text-5xl font-black text-dark-900 mb-6",children:["Join the Future of"," ",(0,r.jsx)("span",{className:"bg-gradient-to-r from-solar-600 to-eco-600 bg-clip-text text-transparent",children:"Sustainable Mining"})]}),(0,r.jsx)("p",{className:"text-lg text-gray-600 leading-relaxed mb-8",children:"Start your journey with HashCoreX and be part of the green revolution in cryptocurrency mining."}),(0,r.jsxs)("div",{className:"grid grid-cols-2 gap-6 text-left",children:[(0,r.jsxs)("div",{className:"bg-white/50 rounded-lg p-4",children:[(0,r.jsx)("div",{className:"text-2xl font-bold text-solar-600 mb-1",children:"100%"}),(0,r.jsx)("div",{className:"text-sm text-gray-600",children:"Renewable Energy"})]}),(0,r.jsxs)("div",{className:"bg-white/50 rounded-lg p-4",children:[(0,r.jsx)("div",{className:"text-2xl font-bold text-eco-600 mb-1",children:"24/7"}),(0,r.jsx)("div",{className:"text-sm text-gray-600",children:"Mining Operations"})]})]})]})}),(0,r.jsx)("div",{className:"w-full lg:w-1/2 xl:w-2/5 flex items-center justify-center p-6 lg:p-12",children:(0,r.jsxs)("div",{className:"w-full max-w-md",children:[(0,r.jsxs)("div",{className:"text-center mb-8",children:[(0,r.jsxs)(i(),{href:"/",className:"inline-flex items-center space-x-3 group mb-8",children:[(0,r.jsxs)("div",{className:"relative",children:[(0,r.jsx)(o.MX,{className:"h-10 w-10 lg:h-12 lg:w-12 text-solar-500 group-hover:scale-110 transition-transform"}),(0,r.jsx)("div",{className:"absolute inset-0 bg-solar-500/20 rounded-full animate-ping"})]}),(0,r.jsx)("span",{className:"text-2xl lg:text-4xl font-black bg-gradient-to-r from-solar-600 to-eco-600 bg-clip-text text-transparent",children:"HashCoreX"})]}),(0,r.jsx)("h1",{className:"text-2xl lg:text-3xl xl:text-4xl font-black text-dark-900 mb-3 lg:mb-4",children:"Create Account"}),(0,r.jsx)("p",{className:"text-base lg:text-lg text-gray-600 font-medium",children:"Start your sustainable mining journey"})]}),"form"===A?(0,r.jsxs)("form",{onSubmit:R,className:"space-y-6",children:[w&&(0,r.jsx)("div",{className:"bg-red-50 border border-red-200 text-red-600 px-4 py-3 rounded-lg text-sm",children:w}),(0,r.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{htmlFor:"firstName",className:"block text-sm font-semibold text-gray-700 mb-2",children:"First Name"}),(0,r.jsx)(d.pd,{id:"firstName",name:"firstName",type:"text",required:!0,value:f.firstName,onChange:H,className:"w-full",placeholder:"Enter your first name"})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{htmlFor:"lastName",className:"block text-sm font-semibold text-gray-700 mb-2",children:"Last Name"}),(0,r.jsx)(d.pd,{id:"lastName",name:"lastName",type:"text",required:!0,value:f.lastName,onChange:H,className:"w-full",placeholder:"Enter your last name"})]})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{htmlFor:"email",className:"block text-sm font-semibold text-gray-700 mb-2",children:"Email Address"}),(0,r.jsx)(d.pd,{id:"email",name:"email",type:"email",required:!0,value:f.email,onChange:H,className:"w-full",placeholder:"Enter your email address"})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{htmlFor:"password",className:"block text-sm font-semibold text-gray-700 mb-2",children:"Password"}),(0,r.jsxs)("div",{className:"relative",children:[(0,r.jsx)(d.pd,{id:"password",name:"password",type:j?"text":"password",required:!0,value:f.password,onChange:H,className:"w-full pr-12",placeholder:"Create a strong password"}),(0,r.jsx)("button",{type:"button",onClick:()=>b(!j),className:"absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-500 hover:text-gray-700",children:j?(0,r.jsx)(c.A,{className:"h-5 w-5"}):(0,r.jsx)(m.A,{className:"h-5 w-5"})})]}),f.password&&(0,r.jsx)("div",{className:"mt-2 space-y-1",children:P.errors.map((e,s)=>(0,r.jsxs)("div",{className:"flex items-center text-sm",children:[(0,r.jsx)(x.A,{className:`h-4 w-4 mr-2 ${P.isValid?"text-green-500":"text-red-500"}`}),(0,r.jsx)("span",{className:P.isValid?"text-green-600":"text-red-600",children:e})]},s))})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{htmlFor:"confirmPassword",className:"block text-sm font-semibold text-gray-700 mb-2",children:"Confirm Password"}),(0,r.jsxs)("div",{className:"relative",children:[(0,r.jsx)(d.pd,{id:"confirmPassword",name:"confirmPassword",type:N?"text":"password",required:!0,value:f.confirmPassword,onChange:H,className:"w-full pr-12",placeholder:"Confirm your password"}),(0,r.jsx)("button",{type:"button",onClick:()=>y(!N),className:"absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-500 hover:text-gray-700",children:N?(0,r.jsx)(c.A,{className:"h-5 w-5"}):(0,r.jsx)(m.A,{className:"h-5 w-5"})})]}),f.confirmPassword&&(0,r.jsx)("div",{className:"mt-2",children:(0,r.jsxs)("div",{className:"flex items-center text-sm",children:[(0,r.jsx)(x.A,{className:`h-4 w-4 mr-2 ${f.password===f.confirmPassword?"text-green-500":"text-red-500"}`}),(0,r.jsx)("span",{className:f.password===f.confirmPassword?"text-green-600":"text-red-600",children:f.password===f.confirmPassword?"Passwords match":"Passwords do not match"})]})})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{htmlFor:"referralCode",className:"block text-sm font-semibold text-gray-700 mb-2",children:"Referral Code (Optional)"}),(0,r.jsx)(d.pd,{id:"referralCode",name:"referralCode",type:"text",value:f.referralCode,onChange:H,className:"w-full",placeholder:"Enter referral code if you have one"})]}),(0,r.jsx)(d.$n,{type:"submit",variant:"primary",size:"xl",className:"w-full font-bold",loading:q,disabled:!P.isValid||f.password!==f.confirmPassword||q,children:q?"Sending OTP...":"Send Verification Code"})]}):(0,r.jsx)("div",{className:"space-y-6",children:(0,r.jsx)(v,{email:f.email,firstName:f.firstName,onVerified:F,onResend:D,loading:l,error:w})}),(0,r.jsx)("div",{className:"mt-8 text-center",children:(0,r.jsxs)("p",{className:"text-gray-600",children:["Already have an account?"," ",(0,r.jsx)(i(),{href:"/login",className:"text-solar-500 hover:text-solar-600 font-medium",children:"Sign in"})]})}),(0,r.jsx)("div",{className:"mt-6 text-center lg:hidden",children:(0,r.jsxs)(i(),{href:"/",className:"inline-flex items-center text-gray-500 hover:text-gray-700 text-sm",children:[(0,r.jsx)(h.A,{className:"h-4 w-4 mr-1"}),"Back to Home"]})})]})})]})]})}function y(){return(0,r.jsx)(f.CU,{redirectTo:"/dashboard",children:(0,r.jsx)(a.Suspense,{fallback:(0,r.jsx)("div",{className:"min-h-screen bg-gradient-to-br from-green-50 to-blue-50 flex items-center justify-center",children:(0,r.jsxs)("div",{className:"text-center",children:[(0,r.jsx)("div",{className:"animate-spin rounded-full h-8 w-8 border-b-2 border-green-600 mx-auto mb-4"}),(0,r.jsx)("p",{className:"text-gray-600",children:"Loading..."})]})}),children:(0,r.jsx)(N,{})})})}},41550:(e,s,t)=>{"use strict";t.d(s,{A:()=>r});let r=(0,t(62688).A)("mail",[["path",{d:"m22 7-8.991 5.727a2 2 0 0 1-2.009 0L2 7",key:"132q7q"}],["rect",{x:"2",y:"4",width:"20",height:"16",rx:"2",key:"izxlao"}]])},49084:(e,s,t)=>{"use strict";t.r(s),t.d(s,{GlobalError:()=>i.a,__next_app__:()=>m,pages:()=>c,routeModule:()=>x,tree:()=>o});var r=t(65239),a=t(48088),l=t(88170),i=t.n(l),n=t(30893),d={};for(let e in n)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(d[e]=()=>n[e]);t.d(s,d);let o={children:["",{children:["(auth)",{children:["register",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(t.bind(t,99308)),"C:\\Users\\<USER>\\Desktop\\Hash_Minings\\hashcorex\\src\\app\\(auth)\\register\\page.tsx"]}]},{}]},{"not-found":[()=>Promise.resolve().then(t.t.bind(t,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(t.t.bind(t,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(t.t.bind(t,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(t.bind(t,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(t.bind(t,94431)),"C:\\Users\\<USER>\\Desktop\\Hash_Minings\\hashcorex\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(t.t.bind(t,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(t.t.bind(t,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(t.t.bind(t,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(t.bind(t,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,c=["C:\\Users\\<USER>\\Desktop\\Hash_Minings\\hashcorex\\src\\app\\(auth)\\register\\page.tsx"],m={require:t,loadChunk:()=>Promise.resolve()},x=new r.AppPageRouteModule({definition:{kind:a.RouteKind.APP_PAGE,page:"/(auth)/register/page",pathname:"/register",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:o}})},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},73187:(e,s,t)=>{Promise.resolve().then(t.bind(t,38134))},78122:(e,s,t)=>{"use strict";t.d(s,{A:()=>r});let r=(0,t(62688).A)("refresh-cw",[["path",{d:"M3 12a9 9 0 0 1 9-9 9.75 9.75 0 0 1 6.74 2.74L21 8",key:"v9h5vc"}],["path",{d:"M21 3v5h-5",key:"1q7to0"}],["path",{d:"M21 12a9 9 0 0 1-9 9 9.75 9.75 0 0 1-6.74-2.74L3 16",key:"3uifl3"}],["path",{d:"M8 16H3v5",key:"1cv678"}]])},79551:e=>{"use strict";e.exports=require("url")},97163:(e,s,t)=>{Promise.resolve().then(t.bind(t,99308))},99308:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>r});let r=(0,t(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\app\\\\(auth)\\\\register\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Desktop\\Hash_Minings\\hashcorex\\src\\app\\(auth)\\register\\page.tsx","default")}};var s=require("../../../webpack-runtime.js");s.C(e);var t=e=>s(s.s=e),r=s.X(0,[4447,1771,1658,1752,1601],()=>t(49084));module.exports=r})();