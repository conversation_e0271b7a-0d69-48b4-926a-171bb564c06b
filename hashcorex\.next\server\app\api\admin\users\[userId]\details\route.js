"use strict";(()=>{var e={};e.id=5692,e.ids=[5692],e.modules={3295:e=>{e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},12909:(e,t,a)=>{a.d(t,{DT:()=>w,DY:()=>h,HU:()=>c,Lx:()=>y,b9:()=>f,qc:()=>I});var r=a(85663),s=a(43205),i=a.n(s),n=a(6710);let o=process.env.JWT_SECRET||"fallback-secret-key",l=process.env.JWT_EXPIRES_IN||"30d",u=async e=>await r.Ay.hash(e,12),d=async(e,t)=>await r.Ay.compare(e,t),c=e=>i().sign(e,o,{expiresIn:l}),m=e=>{try{return i().verify(e,o)}catch(e){return null}},p=()=>{let e="ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789",t="HC";for(let a=0;a<8;a++)t+=e.charAt(Math.floor(Math.random()*e.length));return t},f=async e=>{let t=e.headers.get("authorization")?.replace("Bearer ","")||e.cookies.get("auth-token")?.value;if(!t)return{authenticated:!1,user:null};let a=m(t);if(!a)return{authenticated:!1,user:null};let r=await n.Gy.findByEmail(a.email);return r?{authenticated:!0,user:r}:{authenticated:!1,user:null}},h=async e=>{let t,r;if(await n.Gy.findByEmail(e.email))throw Error("User already exists with this email");if(e.referralCode){let a=await n.Gy.findByReferralId(e.referralCode);if(!a)throw Error("Invalid referral code");t=a.id}let s=await u(e.password),i=!1;do r=p(),i=!await n.Gy.findByReferralId(r);while(!i);let o=await n.Gy.create({email:e.email,firstName:e.firstName,lastName:e.lastName,password:s,referralId:r});if(t){let{placeUserByReferralType:r}=await a.e(2746).then(a.bind(a,2746)),s="general";"left"===e.placementSide?s="left":"right"===e.placementSide&&(s="right"),await r(t,o.id,s)}return{id:o.id,email:o.email,referralId:o.referralId,kycStatus:o.kycStatus}},y=async e=>{let t=await n.Gy.findByEmail(e.email);if(!t||!await d(e.password,t.password))throw Error("Invalid email or password");return{token:c({userId:t.id,email:t.email}),user:{id:t.id,email:t.email,referralId:t.referralId,kycStatus:t.kycStatus}}},w=e=>/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(e),I=async e=>{let t=await n.Gy.findById(e);return t?.role==="ADMIN"}},27910:e=>{e.exports=require("stream")},28354:e=>{e.exports=require("util")},29294:e=>{e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},44823:(e,t,a)=>{a.r(t),a.d(t,{patchFetch:()=>w,routeModule:()=>p,serverHooks:()=>y,workAsyncStorage:()=>f,workUnitAsyncStorage:()=>h});var r={};a.r(r),a.d(r,{GET:()=>m});var s=a(96559),i=a(48088),n=a(37719),o=a(32190),l=a(12909),u=a(31183),d=a(6710),c=a(2746);async function m(e,{params:t}){try{let{authenticated:a,user:r}=await (0,l.b9)(e);if(!a||!r)return o.NextResponse.json({success:!1,error:"Authentication required"},{status:401});if("ADMIN"!==r.role)return o.NextResponse.json({success:!1,error:"Admin access required"},{status:403});let{userId:s}=await t,i=await u.prisma.user.findUnique({where:{id:s},select:{id:!0,email:!0,firstName:!0,lastName:!0,role:!0,kycStatus:!0,isActive:!0,createdAt:!0,referralId:!0,referrerId:!0}});if(!i)return o.NextResponse.json({success:!1,error:"User not found"},{status:404});let n=await d.k_.getOrCreate(s),m=(await u.prisma.miningUnit.findMany({where:{userId:s},select:{id:!0,thsAmount:!0,investmentAmount:!0,totalEarned:!0,status:!0,expiryDate:!0,createdAt:!0}})).reduce((e,t)=>{let a="ACTIVE"===t.status&&t.expiryDate>new Date;return{totalUnits:e.totalUnits+1,totalInvestment:e.totalInvestment+t.investmentAmount,totalTHS:e.totalTHS+t.thsAmount,activeTHS:e.activeTHS+(a?t.thsAmount:0)}},{totalUnits:0,totalInvestment:0,totalTHS:0,activeTHS:0}),p=await d.FW.findByUserId(s),f=await (0,c.QS)(s),h=await (0,c.l6)(s),y=(await u.prisma.transaction.findMany({where:{userId:s},orderBy:{createdAt:"desc"},take:10,select:{id:!0,type:!0,amount:!0,description:!0,status:!0,createdAt:!0}})).map(e=>({type:e.type,description:e.description||`${e.type.replace("_"," ")} - ${e.status}`,amount:e.amount,date:e.createdAt.toISOString()})),w=await u.prisma.transaction.aggregate({where:{userId:s,type:{in:["MINING_EARNINGS","DIRECT_REFERRAL","BINARY_BONUS"]},status:"COMPLETED"},_sum:{amount:!0}}),I=await u.prisma.transaction.aggregate({where:{userId:s,type:"DEPOSIT",status:"COMPLETED"},_sum:{amount:!0}}),g=await u.prisma.transaction.aggregate({where:{userId:s,type:"WITHDRAWAL",status:"COMPLETED"},_sum:{amount:!0}}),x={id:i.id,email:i.email,firstName:i.firstName,lastName:i.lastName,role:i.role,kycStatus:i.kycStatus,isActive:i.isActive,createdAt:i.createdAt.toISOString(),referralId:i.referralId,referrerId:i.referrerId,walletBalance:{availableBalance:n.availableBalance,totalEarnings:w._sum.amount||0,totalDeposits:I._sum.amount||0,totalWithdrawals:g._sum.amount||0},miningUnits:m,binaryPoints:{leftPoints:p?.leftPoints||0,rightPoints:p?.rightPoints||0,totalMatched:p?.totalMatched||0,lastMatchDate:p?.lastMatchDate?.toISOString()},referralStats:{directReferrals:f,leftTeam:h.left,rightTeam:h.right,totalTeam:h.total},recentActivity:y};return o.NextResponse.json({success:!0,data:x})}catch(e){return console.error("User details fetch error:",e),o.NextResponse.json({success:!1,error:"Failed to fetch user details"},{status:500})}}let p=new s.AppRouteRouteModule({definition:{kind:i.RouteKind.APP_ROUTE,page:"/api/admin/users/[userId]/details/route",pathname:"/api/admin/users/[userId]/details",filename:"route",bundlePath:"app/api/admin/users/[userId]/details/route"},resolvedPagePath:"C:\\Users\\<USER>\\Desktop\\Hash_Minings\\hashcorex\\src\\app\\api\\admin\\users\\[userId]\\details\\route.ts",nextConfigOutput:"",userland:r}),{workAsyncStorage:f,workUnitAsyncStorage:h,serverHooks:y}=p;function w(){return(0,n.patchFetch)({workAsyncStorage:f,workUnitAsyncStorage:h})}},44870:e=>{e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},55511:e=>{e.exports=require("crypto")},63033:e=>{e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},79428:e=>{e.exports=require("buffer")},96330:e=>{e.exports=require("@prisma/client")}};var t=require("../../../../../../webpack-runtime.js");t.C(e);var a=e=>t(t.s=e),r=t.X(0,[4447,580,5315,3306,2746],()=>a(44823));module.exports=r})();