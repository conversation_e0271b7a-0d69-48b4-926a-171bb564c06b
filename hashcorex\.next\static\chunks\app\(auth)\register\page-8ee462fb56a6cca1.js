(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[2678],{1264:(e,t,s)=>{"use strict";s.d(t,{A:()=>r});let r=(0,s(9946).A)("mail",[["path",{d:"m22 7-8.991 5.727a2 2 0 0 1-2.009 0L2 7",key:"132q7q"}],["rect",{x:"2",y:"4",width:"20",height:"16",rx:"2",key:"izxlao"}]])},1470:(e,t,s)=>{"use strict";s.d(t,{CU:()=>c});var r=s(5155),a=s(2115),l=s(5695),i=s(4105),n=s(6469);let o=e=>{let{children:t,requireAuth:s=!1,requireGuest:o=!1,redirectTo:c}=e,{user:d,loading:x}=(0,i.A)(),h=(0,l.useRouter)(),m=(0,l.usePathname)();return((0,a.useEffect)(()=>{if(!x){if(s&&!d){let e="/login?redirect=".concat(encodeURIComponent(m));h.replace(e);return}if(o&&d){let e=c||"/dashboard";h.replace(e);return}}},[d,x,s,o,h,m,c]),x)?(0,r.jsx)("div",{className:"min-h-screen flex items-center justify-center bg-gray-50",children:(0,r.jsxs)("div",{className:"text-center",children:[(0,r.jsx)(n.Rh,{}),(0,r.jsx)("p",{className:"mt-4 text-gray-600",children:"Checking authentication..."})]})}):s&&!d||o&&d?null:(0,r.jsx)(r.Fragment,{children:t})},c=e=>{let{children:t,redirectTo:s}=e;return(0,r.jsx)(o,{requireGuest:!0,redirectTo:s,children:t})}},2657:(e,t,s)=>{"use strict";s.d(t,{A:()=>r});let r=(0,s(9946).A)("eye",[["path",{d:"M2.062 12.348a1 1 0 0 1 0-.696 10.75 10.75 0 0 1 19.876 0 1 1 0 0 1 0 .696 10.75 10.75 0 0 1-19.876 0",key:"1nclc0"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]])},3904:(e,t,s)=>{"use strict";s.d(t,{A:()=>r});let r=(0,s(9946).A)("refresh-cw",[["path",{d:"M3 12a9 9 0 0 1 9-9 9.75 9.75 0 0 1 6.74 2.74L21 8",key:"v9h5vc"}],["path",{d:"M21 3v5h-5",key:"1q7to0"}],["path",{d:"M21 12a9 9 0 0 1-9 9 9.75 9.75 0 0 1-6.74-2.74L3 16",key:"3uifl3"}],["path",{d:"M8 16H3v5",key:"1cv678"}]])},4105:(e,t,s)=>{"use strict";s.d(t,{A:()=>n,AuthProvider:()=>i});var r=s(5155),a=s(2115);let l=(0,a.createContext)(void 0),i=e=>{let{children:t}=e,[s,i]=(0,a.useState)(null),[n,o]=(0,a.useState)(!0);(0,a.useEffect)(()=>{c()},[]);let c=async()=>{try{let e=await fetch("/api/auth/me",{credentials:"include"});if(e.ok){let t=await e.json();t.success&&i(t.data)}}catch(e){console.error("Auth check failed:",e)}finally{o(!1)}},d=async(e,t)=>{o(!0);try{let s=await fetch("/api/auth/login",{method:"POST",headers:{"Content-Type":"application/json"},credentials:"include",body:JSON.stringify({email:e,password:t})}),r=await s.json();if(!r.success)throw Error(r.error||"Login failed");i(r.data.user),setTimeout(()=>{c()},100)}catch(e){throw e}finally{o(!1)}},x=async(e,t,s,r,a,l,n,c)=>{o(!0);try{let o=await fetch(n?"/api/auth/register?side=".concat(n):"/api/auth/register",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({email:e,firstName:t,lastName:s,password:r,confirmPassword:a,referralCode:l,otp:c})}),d=await o.json();if(!d.success)throw Error(d.error||"Registration failed");i({id:d.data.user.id,email:d.data.user.email,firstName:d.data.user.firstName||"",lastName:d.data.user.lastName||"",referralId:d.data.user.referralId,role:d.data.user.role||"USER",kycStatus:d.data.user.kycStatus,isActive:d.data.user.isActive||!0,profilePicture:d.data.user.profilePicture||null,createdAt:d.data.user.createdAt,updatedAt:d.data.user.updatedAt})}catch(e){throw e}finally{o(!1)}},h=async()=>{try{await fetch("/api/auth/logout",{method:"POST",credentials:"include"})}catch(e){console.error("Logout error:",e)}finally{i(null)}},m=async()=>{await c()};return(0,r.jsx)(l.Provider,{value:{user:s,loading:n,login:d,register:x,logout:h,refreshUser:m},children:t})},n=()=>{let e=(0,a.useContext)(l);if(void 0===e)throw Error("useAuth must be used within an AuthProvider");return e}},5174:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>v});var r=s(5155),a=s(2115),l=s(6874),i=s.n(l),n=s(5695),o=s(6469),c=s(7508),d=s(8749),x=s(2657),h=s(5196),m=s(7550),u=s(4105),f=s(9434),j=s(1470),g=s(1264),y=s(646),p=s(3904);let w=e=>{let{email:t,firstName:s,onVerified:l,onResend:i,loading:n=!1,error:c}=e,[d,x]=(0,a.useState)(["","","","","",""]),[h,m]=(0,a.useState)(600),[u,f]=(0,a.useState)(!1),[j,w]=(0,a.useState)(!1),N=(0,a.useRef)([]);(0,a.useEffect)(()=>{let e=setInterval(()=>{m(e=>e<=1?(f(!0),0):e-1)},1e3);return()=>clearInterval(e)},[]);let v=(e,t)=>{if(t.length>1)return;let s=[...d];if(s[e]=t,x(s),t&&e<5){var r;null==(r=N.current[e+1])||r.focus()}s.every(e=>""!==e)&&6===s.join("").length&&l(s.join(""))},b=(e,t)=>{if("Backspace"===t.key&&!d[e]&&e>0){var s;null==(s=N.current[e-1])||s.focus()}},k=e=>{e.preventDefault();let t=e.clipboardData.getData("text").replace(/\D/g,"").slice(0,6);6===t.length&&(x(t.split("")),l(t))},C=async()=>{w(!0),f(!1),m(600),await i(),w(!1)};return(0,r.jsxs)("div",{className:"max-w-md mx-auto",children:[(0,r.jsxs)("div",{className:"text-center mb-8",children:[(0,r.jsx)("div",{className:"w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-4",children:(0,r.jsx)(g.A,{className:"w-8 h-8 text-green-600"})}),(0,r.jsx)("h2",{className:"text-2xl font-bold text-gray-900 mb-2",children:"Verify Your Email"}),(0,r.jsx)("p",{className:"text-gray-600",children:"We've sent a 6-digit verification code to"}),(0,r.jsx)("p",{className:"font-semibold text-gray-900",children:t})]}),(0,r.jsxs)("div",{className:"space-y-6",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-3 text-center",children:"Enter verification code"}),(0,r.jsx)("div",{className:"flex justify-center space-x-3",children:d.map((e,t)=>(0,r.jsx)(o.pd,{ref:e=>N.current[t]=e,type:"text",inputMode:"numeric",maxLength:1,value:e,onChange:e=>v(t,e.target.value),onKeyDown:e=>b(t,e),onPaste:k,className:"w-12 h-12 text-center text-lg font-semibold border-2 focus:border-green-500",disabled:n},t))})]}),c&&(0,r.jsx)("div",{className:"bg-red-50 border border-red-200 rounded-lg p-3",children:(0,r.jsx)("p",{className:"text-sm text-red-600 text-center",children:c})}),(0,r.jsx)("div",{className:"text-center",children:h>0?(0,r.jsxs)("p",{className:"text-sm text-gray-600",children:["Code expires in ",(0,r.jsx)("span",{className:"font-semibold text-green-600",children:(e=>{let t=Math.floor(e/60);return"".concat(t,":").concat((e%60).toString().padStart(2,"0"))})(h)})]}):(0,r.jsx)("p",{className:"text-sm text-red-600",children:"Code has expired"})}),(0,r.jsx)(o.$n,{onClick:()=>{let e=d.join("");6===e.length&&l(e)},disabled:n||d.some(e=>""===e),className:"w-full bg-green-600 hover:bg-green-700 text-white",children:n?(0,r.jsxs)("div",{className:"flex items-center justify-center",children:[(0,r.jsx)("div",{className:"animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"}),"Verifying..."]}):(0,r.jsxs)("div",{className:"flex items-center justify-center",children:[(0,r.jsx)(y.A,{className:"w-4 h-4 mr-2"}),"Verify Code"]})}),(0,r.jsxs)("div",{className:"text-center",children:[(0,r.jsx)("p",{className:"text-sm text-gray-600 mb-2",children:"Didn't receive the code?"}),(0,r.jsx)(o.$n,{onClick:C,disabled:!u||j,variant:"ghost",className:"text-green-600 hover:text-green-700",children:j?(0,r.jsxs)("div",{className:"flex items-center",children:[(0,r.jsx)(p.A,{className:"w-4 h-4 mr-2 animate-spin"}),"Sending..."]}):(0,r.jsxs)("div",{className:"flex items-center",children:[(0,r.jsx)(p.A,{className:"w-4 h-4 mr-2"}),"Resend Code"]})})]}),(0,r.jsxs)("div",{className:"bg-blue-50 border border-blue-200 rounded-lg p-4",children:[(0,r.jsx)("p",{className:"text-sm text-blue-700",children:(0,r.jsx)("strong",{children:"Tips:"})}),(0,r.jsxs)("ul",{className:"text-sm text-blue-600 mt-1 space-y-1",children:[(0,r.jsx)("li",{children:"• Check your spam/junk folder"}),(0,r.jsxs)("li",{children:["• Make sure ",t," is correct"]}),(0,r.jsx)("li",{children:"• You can paste the code from your email"})]})]})]})]})};function N(){let e=(0,n.useRouter)(),t=(0,n.useSearchParams)(),{register:s,loading:l}=(0,u.A)(),[j,g]=(0,a.useState)({firstName:"",lastName:"",email:"",password:"",confirmPassword:"",referralCode:t.get("ref")||""}),[y,p]=(0,a.useState)(!1),[N,v]=(0,a.useState)(!1),[b,k]=(0,a.useState)(""),[C,P]=(0,a.useState)({isValid:!1,errors:[]}),[A,S]=(0,a.useState)("form"),[M,W]=(0,a.useState)(!1),[E,O]=(0,a.useState)(!1),R=async e=>(e.preventDefault(),k(""),j.password!==j.confirmPassword)?void k("Passwords do not match"):C.isValid?void await T():void k("Please ensure your password meets all requirements"),T=async()=>{try{O(!0),k("");let e=await fetch("/api/auth/send-otp",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({email:j.email,firstName:j.firstName})}),t=await e.json();t.success?(W(!0),S("otp")):k(t.error||"Failed to send OTP")}catch(e){k("Failed to send OTP. Please try again.")}finally{O(!1)}},z=async r=>{try{k("");let a=t.get("side");await s(j.email,j.firstName,j.lastName,j.password,j.confirmPassword,j.referralCode,a||void 0,r);let l=t.get("redirect");l&&l.startsWith("/")?e.push(l):e.push("/dashboard")}catch(e){k(e instanceof Error?e.message:"Registration failed")}},F=e=>{let{name:t,value:s}=e.target;g(e=>({...e,[t]:s})),"password"===t&&P((0,f.Oj)(s))};return(0,r.jsxs)("div",{className:"min-h-screen relative overflow-hidden",children:[(0,r.jsx)("div",{className:"absolute inset-0 animated-gradient opacity-30"}),(0,r.jsx)("div",{className:"absolute inset-0 bg-gradient-to-br from-white/95 via-solar-50/90 to-eco-50/95"}),(0,r.jsx)("div",{className:"absolute top-20 left-10 w-16 h-16 bg-solar-400/20 rounded-full animate-float"}),(0,r.jsx)("div",{className:"absolute top-40 right-20 w-12 h-12 bg-eco-400/20 rounded-full animate-float",style:{animationDelay:"2s"}}),(0,r.jsx)("div",{className:"absolute bottom-40 left-20 w-10 h-10 bg-purple-400/20 rounded-full animate-float",style:{animationDelay:"4s"}}),(0,r.jsxs)("div",{className:"relative z-10 min-h-screen flex",children:[(0,r.jsx)("div",{className:"hidden lg:flex lg:w-1/2 xl:w-3/5 bg-gradient-to-br from-solar-500/10 to-eco-500/10 items-center justify-center p-12",children:(0,r.jsxs)("div",{className:"max-w-lg text-center",children:[(0,r.jsx)("div",{className:"mb-8",children:(0,r.jsxs)("div",{className:"relative inline-block",children:[(0,r.jsx)("div",{className:"absolute inset-0 bg-solar-500/20 rounded-full animate-ping"}),(0,r.jsx)(c.MX,{className:"relative h-20 w-20 text-solar-500"})]})}),(0,r.jsxs)("h1",{className:"text-4xl xl:text-5xl font-black text-dark-900 mb-6",children:["Join the Future of"," ",(0,r.jsx)("span",{className:"bg-gradient-to-r from-solar-600 to-eco-600 bg-clip-text text-transparent",children:"Sustainable Mining"})]}),(0,r.jsx)("p",{className:"text-lg text-gray-600 leading-relaxed mb-8",children:"Start your journey with HashCoreX and be part of the green revolution in cryptocurrency mining."}),(0,r.jsxs)("div",{className:"grid grid-cols-2 gap-6 text-left",children:[(0,r.jsxs)("div",{className:"bg-white/50 rounded-lg p-4",children:[(0,r.jsx)("div",{className:"text-2xl font-bold text-solar-600 mb-1",children:"100%"}),(0,r.jsx)("div",{className:"text-sm text-gray-600",children:"Renewable Energy"})]}),(0,r.jsxs)("div",{className:"bg-white/50 rounded-lg p-4",children:[(0,r.jsx)("div",{className:"text-2xl font-bold text-eco-600 mb-1",children:"24/7"}),(0,r.jsx)("div",{className:"text-sm text-gray-600",children:"Mining Operations"})]})]})]})}),(0,r.jsx)("div",{className:"w-full lg:w-1/2 xl:w-2/5 flex items-center justify-center p-6 lg:p-12",children:(0,r.jsxs)("div",{className:"w-full max-w-md",children:[(0,r.jsxs)("div",{className:"text-center mb-8",children:[(0,r.jsxs)(i(),{href:"/",className:"inline-flex items-center space-x-3 group mb-8",children:[(0,r.jsxs)("div",{className:"relative",children:[(0,r.jsx)(c.MX,{className:"h-10 w-10 lg:h-12 lg:w-12 text-solar-500 group-hover:scale-110 transition-transform"}),(0,r.jsx)("div",{className:"absolute inset-0 bg-solar-500/20 rounded-full animate-ping"})]}),(0,r.jsx)("span",{className:"text-2xl lg:text-4xl font-black bg-gradient-to-r from-solar-600 to-eco-600 bg-clip-text text-transparent",children:"HashCoreX"})]}),(0,r.jsx)("h1",{className:"text-2xl lg:text-3xl xl:text-4xl font-black text-dark-900 mb-3 lg:mb-4",children:"Create Account"}),(0,r.jsx)("p",{className:"text-base lg:text-lg text-gray-600 font-medium",children:"Start your sustainable mining journey"})]}),"form"===A?(0,r.jsxs)("form",{onSubmit:R,className:"space-y-6",children:[b&&(0,r.jsx)("div",{className:"bg-red-50 border border-red-200 text-red-600 px-4 py-3 rounded-lg text-sm",children:b}),(0,r.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{htmlFor:"firstName",className:"block text-sm font-semibold text-gray-700 mb-2",children:"First Name"}),(0,r.jsx)(o.pd,{id:"firstName",name:"firstName",type:"text",required:!0,value:j.firstName,onChange:F,className:"w-full",placeholder:"Enter your first name"})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{htmlFor:"lastName",className:"block text-sm font-semibold text-gray-700 mb-2",children:"Last Name"}),(0,r.jsx)(o.pd,{id:"lastName",name:"lastName",type:"text",required:!0,value:j.lastName,onChange:F,className:"w-full",placeholder:"Enter your last name"})]})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{htmlFor:"email",className:"block text-sm font-semibold text-gray-700 mb-2",children:"Email Address"}),(0,r.jsx)(o.pd,{id:"email",name:"email",type:"email",required:!0,value:j.email,onChange:F,className:"w-full",placeholder:"Enter your email address"})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{htmlFor:"password",className:"block text-sm font-semibold text-gray-700 mb-2",children:"Password"}),(0,r.jsxs)("div",{className:"relative",children:[(0,r.jsx)(o.pd,{id:"password",name:"password",type:y?"text":"password",required:!0,value:j.password,onChange:F,className:"w-full pr-12",placeholder:"Create a strong password"}),(0,r.jsx)("button",{type:"button",onClick:()=>p(!y),className:"absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-500 hover:text-gray-700",children:y?(0,r.jsx)(d.A,{className:"h-5 w-5"}):(0,r.jsx)(x.A,{className:"h-5 w-5"})})]}),j.password&&(0,r.jsx)("div",{className:"mt-2 space-y-1",children:C.errors.map((e,t)=>(0,r.jsxs)("div",{className:"flex items-center text-sm",children:[(0,r.jsx)(h.A,{className:"h-4 w-4 mr-2 ".concat(C.isValid?"text-green-500":"text-red-500")}),(0,r.jsx)("span",{className:C.isValid?"text-green-600":"text-red-600",children:e})]},t))})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{htmlFor:"confirmPassword",className:"block text-sm font-semibold text-gray-700 mb-2",children:"Confirm Password"}),(0,r.jsxs)("div",{className:"relative",children:[(0,r.jsx)(o.pd,{id:"confirmPassword",name:"confirmPassword",type:N?"text":"password",required:!0,value:j.confirmPassword,onChange:F,className:"w-full pr-12",placeholder:"Confirm your password"}),(0,r.jsx)("button",{type:"button",onClick:()=>v(!N),className:"absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-500 hover:text-gray-700",children:N?(0,r.jsx)(d.A,{className:"h-5 w-5"}):(0,r.jsx)(x.A,{className:"h-5 w-5"})})]}),j.confirmPassword&&(0,r.jsx)("div",{className:"mt-2",children:(0,r.jsxs)("div",{className:"flex items-center text-sm",children:[(0,r.jsx)(h.A,{className:"h-4 w-4 mr-2 ".concat(j.password===j.confirmPassword?"text-green-500":"text-red-500")}),(0,r.jsx)("span",{className:j.password===j.confirmPassword?"text-green-600":"text-red-600",children:j.password===j.confirmPassword?"Passwords match":"Passwords do not match"})]})})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{htmlFor:"referralCode",className:"block text-sm font-semibold text-gray-700 mb-2",children:"Referral Code (Optional)"}),(0,r.jsx)(o.pd,{id:"referralCode",name:"referralCode",type:"text",value:j.referralCode,onChange:F,className:"w-full",placeholder:"Enter referral code if you have one"})]}),(0,r.jsx)(o.$n,{type:"submit",variant:"primary",size:"xl",className:"w-full font-bold",loading:E,disabled:!C.isValid||j.password!==j.confirmPassword||E,children:E?"Sending OTP...":"Send Verification Code"})]}):(0,r.jsx)("div",{className:"space-y-6",children:(0,r.jsx)(w,{email:j.email,firstName:j.firstName,onVerified:z,onResend:T,loading:l,error:b})}),(0,r.jsx)("div",{className:"mt-8 text-center",children:(0,r.jsxs)("p",{className:"text-gray-600",children:["Already have an account?"," ",(0,r.jsx)(i(),{href:"/login",className:"text-solar-500 hover:text-solar-600 font-medium",children:"Sign in"})]})}),(0,r.jsx)("div",{className:"mt-6 text-center lg:hidden",children:(0,r.jsxs)(i(),{href:"/",className:"inline-flex items-center text-gray-500 hover:text-gray-700 text-sm",children:[(0,r.jsx)(m.A,{className:"h-4 w-4 mr-1"}),"Back to Home"]})})]})})]})]})}function v(){return(0,r.jsx)(j.CU,{redirectTo:"/dashboard",children:(0,r.jsx)(a.Suspense,{fallback:(0,r.jsx)("div",{className:"min-h-screen bg-gradient-to-br from-green-50 to-blue-50 flex items-center justify-center",children:(0,r.jsxs)("div",{className:"text-center",children:[(0,r.jsx)("div",{className:"animate-spin rounded-full h-8 w-8 border-b-2 border-green-600 mx-auto mb-4"}),(0,r.jsx)("p",{className:"text-gray-600",children:"Loading..."})]})}),children:(0,r.jsx)(N,{})})})}},5196:(e,t,s)=>{"use strict";s.d(t,{A:()=>r});let r=(0,s(9946).A)("check",[["path",{d:"M20 6 9 17l-5-5",key:"1gmf2c"}]])},5373:(e,t,s)=>{Promise.resolve().then(s.bind(s,5174))},5695:(e,t,s)=>{"use strict";var r=s(8999);s.o(r,"usePathname")&&s.d(t,{usePathname:function(){return r.usePathname}}),s.o(r,"useRouter")&&s.d(t,{useRouter:function(){return r.useRouter}}),s.o(r,"useSearchParams")&&s.d(t,{useSearchParams:function(){return r.useSearchParams}})},7508:(e,t,s)=>{"use strict";s.d(t,{Lc:()=>i,hK:()=>n,NC:()=>l,MX:()=>a,Kj:()=>o});var r=s(5155);s(2115);let a=e=>{let{className:t,size:s=24}=e;return(0,r.jsxs)("svg",{width:s,height:s,viewBox:"0 0 24 24",fill:"none",xmlns:"http://www.w3.org/2000/svg",className:t,children:[(0,r.jsx)("rect",{x:"2",y:"4",width:"20",height:"16",rx:"2",stroke:"currentColor",strokeWidth:"2",fill:"none"}),(0,r.jsx)("line",{x1:"2",y1:"8",x2:"22",y2:"8",stroke:"currentColor",strokeWidth:"2"}),(0,r.jsx)("line",{x1:"2",y1:"12",x2:"22",y2:"12",stroke:"currentColor",strokeWidth:"2"}),(0,r.jsx)("line",{x1:"2",y1:"16",x2:"22",y2:"16",stroke:"currentColor",strokeWidth:"2"}),(0,r.jsx)("line",{x1:"6",y1:"4",x2:"6",y2:"20",stroke:"currentColor",strokeWidth:"2"}),(0,r.jsx)("line",{x1:"10",y1:"4",x2:"10",y2:"20",stroke:"currentColor",strokeWidth:"2"}),(0,r.jsx)("line",{x1:"14",y1:"4",x2:"14",y2:"20",stroke:"currentColor",strokeWidth:"2"}),(0,r.jsx)("line",{x1:"18",y1:"4",x2:"18",y2:"20",stroke:"currentColor",strokeWidth:"2"}),(0,r.jsx)("circle",{cx:"20",cy:"2",r:"1",fill:"currentColor"}),(0,r.jsx)("path",{d:"M19 1l1 1-1 1-1-1z",fill:"currentColor"})]})},l=e=>{let{className:t,size:s=24}=e;return(0,r.jsxs)("svg",{width:s,height:s,viewBox:"0 0 24 24",fill:"none",xmlns:"http://www.w3.org/2000/svg",className:t,children:[(0,r.jsx)("rect",{x:"2",y:"6",width:"20",height:"12",rx:"2",stroke:"currentColor",strokeWidth:"2",fill:"none"}),(0,r.jsx)("rect",{x:"4",y:"8",width:"3",height:"3",rx:"0.5",fill:"currentColor"}),(0,r.jsx)("rect",{x:"8",y:"8",width:"3",height:"3",rx:"0.5",fill:"currentColor"}),(0,r.jsx)("rect",{x:"12",y:"8",width:"3",height:"3",rx:"0.5",fill:"currentColor"}),(0,r.jsx)("rect",{x:"16",y:"8",width:"3",height:"3",rx:"0.5",fill:"currentColor"}),(0,r.jsx)("rect",{x:"4",y:"12",width:"3",height:"3",rx:"0.5",fill:"currentColor"}),(0,r.jsx)("rect",{x:"8",y:"12",width:"3",height:"3",rx:"0.5",fill:"currentColor"}),(0,r.jsx)("rect",{x:"12",y:"12",width:"3",height:"3",rx:"0.5",fill:"currentColor"}),(0,r.jsx)("rect",{x:"16",y:"12",width:"3",height:"3",rx:"0.5",fill:"currentColor"}),(0,r.jsx)("circle",{cx:"20",cy:"4",r:"1",fill:"currentColor"}),(0,r.jsx)("line",{x1:"20",y1:"4",x2:"20",y2:"6",stroke:"currentColor",strokeWidth:"2"}),(0,r.jsx)("line",{x1:"18",y1:"2",x2:"22",y2:"2",stroke:"currentColor",strokeWidth:"2"}),(0,r.jsx)("line",{x1:"19",y1:"1",x2:"21",y2:"3",stroke:"currentColor",strokeWidth:"1"}),(0,r.jsx)("line",{x1:"21",y1:"1",x2:"19",y2:"3",stroke:"currentColor",strokeWidth:"1"})]})},i=e=>{let{className:t,size:s=24}=e;return(0,r.jsxs)("svg",{width:s,height:s,viewBox:"0 0 24 24",fill:"none",xmlns:"http://www.w3.org/2000/svg",className:t,children:[(0,r.jsx)("circle",{cx:"12",cy:"12",r:"10",stroke:"currentColor",strokeWidth:"2",fill:"none"}),(0,r.jsx)("path",{d:"M8 12h8M12 8v8",stroke:"currentColor",strokeWidth:"2"}),(0,r.jsx)("path",{d:"M10 8h4c1.1 0 2 .9 2 2s-.9 2-2 2h-4",stroke:"currentColor",strokeWidth:"2",fill:"none"}),(0,r.jsx)("path",{d:"M10 14h4c1.1 0 2 .9 2 2s-.9 2-2 2h-4",stroke:"currentColor",strokeWidth:"2",fill:"none"}),(0,r.jsx)("line",{x1:"12",y1:"6",x2:"12",y2:"8",stroke:"currentColor",strokeWidth:"2"}),(0,r.jsx)("line",{x1:"12",y1:"16",x2:"12",y2:"18",stroke:"currentColor",strokeWidth:"2"})]})},n=e=>{let{className:t,size:s=24}=e;return(0,r.jsxs)("svg",{width:s,height:s,viewBox:"0 0 24 24",fill:"none",xmlns:"http://www.w3.org/2000/svg",className:t,children:[(0,r.jsx)("path",{d:"M12 2C8 2 5 5 5 9c0 5 7 13 7 13s7-8 7-13c0-4-3-7-7-7z",stroke:"currentColor",strokeWidth:"2",fill:"none"}),(0,r.jsx)("path",{d:"M12 9c0-2-1-3-3-3s-3 1-3 3 1 3 3 3 3-1 3-3z",stroke:"currentColor",strokeWidth:"2",fill:"none"}),(0,r.jsx)("path",{d:"M8 9c0 1 1 2 2 2s2-1 2-2-1-2-2-2-2 1-2 2z",fill:"currentColor"})]})},o=e=>{let{className:t,size:s=24}=e;return(0,r.jsxs)("svg",{width:s,height:s,viewBox:"0 0 24 24",fill:"none",xmlns:"http://www.w3.org/2000/svg",className:t,children:[(0,r.jsx)("line",{x1:"12",y1:"12",x2:"12",y2:"22",stroke:"currentColor",strokeWidth:"2"}),(0,r.jsx)("path",{d:"M12 12L8 4c-1-2 0-4 2-4s3 2 2 4l-2 8z",stroke:"currentColor",strokeWidth:"2",fill:"none"}),(0,r.jsx)("path",{d:"M12 12l8-4c2-1 4 0 4 2s-2 3-4 2l-8-2z",stroke:"currentColor",strokeWidth:"2",fill:"none"}),(0,r.jsx)("path",{d:"M12 12l-4 8c-1 2-3 2-4 0s0-3 2-4l8-2z",stroke:"currentColor",strokeWidth:"2",fill:"none"}),(0,r.jsx)("circle",{cx:"12",cy:"12",r:"1",fill:"currentColor"}),(0,r.jsx)("line",{x1:"10",y1:"22",x2:"14",y2:"22",stroke:"currentColor",strokeWidth:"2"})]})}},7550:(e,t,s)=>{"use strict";s.d(t,{A:()=>r});let r=(0,s(9946).A)("arrow-left",[["path",{d:"m12 19-7-7 7-7",key:"1l729n"}],["path",{d:"M19 12H5",key:"x3x0zl"}]])},8749:(e,t,s)=>{"use strict";s.d(t,{A:()=>r});let r=(0,s(9946).A)("eye-off",[["path",{d:"M10.733 5.076a10.744 10.744 0 0 1 11.205 6.575 1 1 0 0 1 0 .696 10.747 10.747 0 0 1-1.444 2.49",key:"ct8e1f"}],["path",{d:"M14.084 14.158a3 3 0 0 1-4.242-4.242",key:"151rxh"}],["path",{d:"M17.479 17.499a10.75 10.75 0 0 1-15.417-5.151 1 1 0 0 1 0-.696 10.75 10.75 0 0 1 4.446-5.143",key:"13bj9a"}],["path",{d:"m2 2 20 20",key:"1ooewy"}]])}},e=>{var t=t=>e(e.s=t);e.O(0,[3473,6874,6469,8441,1684,7358],()=>t(5373)),_N_E=e.O()}]);