"use strict";exports.id=1827,exports.ids=[1827,3161],exports.modules={1827:(t,e,a)=>{a.d(e,{j:()=>d});var i=a(6710),r=a(31183),s=a(59480),o=a(13161);let n=new Map,c=new Map;class l{static getInstance(){return l.instance||(l.instance=new l),l.instance}async start(){if(this.isRunning)return void console.log("Deposit verification service is already running");this.isRunning=!0,console.log("Starting deposit verification service..."),await this.processPendingVerifications(),await this.processWaitingForConfirmations(),console.log("Deposit verification service started successfully")}stop(){this.isRunning=!1,c.forEach(t=>{clearTimeout(t)}),c.clear(),n.clear(),console.log("Deposit verification service stopped")}async processPendingVerifications(){try{let t=await i.J6.getPendingVerificationDeposits();for(let e of(console.log(`Found ${t.length} deposits pending verification`),t))n.has(e.transactionId)||this.scheduleVerification(e.transactionId,e.tronAddress)}catch(t){console.error("Error processing pending verifications:",t),await i.AJ.create({action:"DEPOSIT_VERIFICATION_ERROR",details:`Error processing pending verifications: ${t instanceof Error?t.message:"Unknown error"}`})}}async processWaitingForConfirmations(){try{let t=await i.J6.getWaitingForConfirmationsDeposits();for(let e of(console.log(`Found ${t.length} deposits waiting for confirmations`),t))c.has(e.transactionId)||this.scheduleConfirmationCheck(e.transactionId,e.tronAddress)}catch(t){console.error("Error processing waiting for confirmations:",t),await i.AJ.create({action:"CONFIRMATION_CHECK_ERROR",details:`Error processing waiting for confirmations: ${t instanceof Error?t.message:"Unknown error"}`})}}scheduleVerification(t,e){n.has(t)||(n.set(t,!0),console.log(`Scheduling verification for transaction: ${t}`),this.verifyTransaction(t,e,!1),setTimeout(()=>{this.verifyTransaction(t,e,!0)},6e4))}async verifyTransaction(t,e,a){try{console.log(`${a?"Retrying":"Attempting"} verification for transaction: ${t}`);let o=await i.rs.get("usdtDepositAddress");o||(o=await i.rs.get("USDT_DEPOSIT_ADDRESS")),o&&(o=o.replace(/['"]/g,"").trim());let c=o||e;console.log(`Using deposit address for verification: ${c} (from admin settings: ${!!o})`);let l=parseInt(await i.rs.get("minConfirmations")||"10"),d=parseFloat(await i.rs.get("minDepositAmount")||"10"),u=parseFloat(await i.rs.get("maxDepositAmount")||"10000"),p=(0,s.gp)(t,c,1),m=new Promise((t,e)=>setTimeout(()=>e(Error("Verification timeout")),3e4)),f=await Promise.race([p,m]);if(!f.isValid&&0===f.confirmations){a&&(await i.J6.updateStatus(t,"FAILED",{failureReason:"Transaction not found or invalid after verification attempts",processedAt:new Date}),await i.AJ.create({action:"DEPOSIT_VERIFICATION_FAILED",details:`Transaction ${t} failed verification after retry`}),n.delete(t));return}if(!(f.toAddress.toLowerCase().includes(c.toLowerCase().slice(1,10))||c.toLowerCase().includes(f.toAddress.toLowerCase().slice(1,10)))){await i.J6.updateStatus(t,"FAILED",{failureReason:"Invalid recipient address",processedAt:new Date}),n.delete(t);return}if(f.amount<d){await i.J6.updateStatus(t,"FAILED",{failureReason:`Deposit amount ${f.amount} USDT is below minimum ${d} USDT`,processedAt:new Date}),n.delete(t);return}if(f.amount>u){await i.J6.updateStatus(t,"FAILED",{failureReason:`Deposit amount ${f.amount} USDT exceeds maximum ${u} USDT`,processedAt:new Date}),n.delete(t);return}await r.prisma.depositTransaction.update({where:{transactionId:t},data:{amount:f.amount,usdtAmount:f.amount,senderAddress:f.fromAddress,blockNumber:f.blockNumber.toString(),blockTimestamp:new Date(f.blockTimestamp),confirmations:f.confirmations,tronAddress:c}}),await i.J6.updateStatus(t,"PENDING",{confirmations:f.confirmations}),console.log(`Transaction ${t} verified with ${f.confirmations} confirmations (required: ${l})`),f.confirmations>=l?await this.completeDeposit(t,f.amount):(await i.J6.updateStatus(t,"WAITING_FOR_CONFIRMATIONS"),this.scheduleConfirmationCheck(t,c)),n.delete(t)}catch(s){console.error(`Error verifying transaction ${t}:`,s);let e=s instanceof Error?s.message:"Unknown error",r=e.includes("timeout")||e.includes("network")||e.includes("ECONNRESET");a||!r?(await i.J6.updateStatus(t,"FAILED",{failureReason:`Verification error: ${e}`,processedAt:new Date}),n.delete(t),await i.AJ.create({action:"DEPOSIT_VERIFICATION_FAILED",details:`Transaction ${t} failed verification: ${e}`})):await i.AJ.create({action:"DEPOSIT_VERIFICATION_NETWORK_ERROR",details:`Network error verifying transaction ${t}: ${e}. Will retry.`})}}scheduleConfirmationCheck(t,e){if(c.has(t))return;console.log(`Starting confirmation checking for transaction: ${t}`);let a=setInterval(async()=>{await this.checkConfirmations(t,e)},6e4);c.set(t,a),this.checkConfirmations(t,e)}async checkConfirmations(t,e){try{console.log(`Checking confirmations for transaction: ${t}`);let a=await i.rs.get("usdtDepositAddress");a||(a=await i.rs.get("USDT_DEPOSIT_ADDRESS")),a&&(a=a.replace(/['"]/g,"").trim());let r=a||e,o=parseInt(await i.rs.get("minConfirmations")||"10"),n=await (0,s.gp)(t,r,1);if(!n.isValid)return void console.log(`Transaction ${t} is no longer valid during confirmation check`);if(await i.J6.updateConfirmations(t,n.confirmations),console.log(`Transaction ${t} has ${n.confirmations} confirmations (required: ${o})`),n.confirmations>=o){await this.completeDeposit(t,n.amount);let e=c.get(t);e&&(clearInterval(e),c.delete(t))}}catch(e){console.error(`Error checking confirmations for transaction ${t}:`,e),await i.AJ.create({action:"CONFIRMATION_CHECK_ERROR",details:`Error checking confirmations for ${t}: ${e instanceof Error?e.message:"Unknown error"}`})}}async completeDeposit(t,e){try{console.log(`Completing deposit for transaction: ${t} with amount: ${e}`);let a=await i.J6.findByTransactionId(t);if(!a)throw Error("Deposit record not found");if("CONFIRMED"===a.status||"COMPLETED"===a.status)return void console.log(`Deposit ${t} already completed, skipping...`);await r.prisma.$transaction(async i=>{await i.depositTransaction.update({where:{transactionId:t},data:{status:"CONFIRMED",verifiedAt:new Date,processedAt:new Date}});let r=await i.walletBalance.findUnique({where:{userId:a.userId}});r?await i.walletBalance.update({where:{userId:a.userId},data:{availableBalance:r.availableBalance+e,totalDeposits:r.totalDeposits+e,lastUpdated:new Date}}):await i.walletBalance.create({data:{userId:a.userId,availableBalance:e,pendingBalance:0,totalDeposits:e,totalWithdrawals:0,totalEarnings:0}});let s=await i.transaction.findFirst({where:{userId:a.userId,type:"DEPOSIT",description:`USDT TRC20 Deposit - TX: ${t}`,status:"PENDING"}});s?await i.transaction.update({where:{id:s.id},data:{status:"COMPLETED",amount:e}}):await i.transaction.create({data:{userId:a.userId,type:"DEPOSIT",amount:e,description:`USDT TRC20 Deposit - TX: ${t}`,status:"COMPLETED"}})}),await i.AJ.create({action:"DEPOSIT_COMPLETED",userId:a.userId,details:`Deposit completed: ${e} USDT from transaction ${t}`});try{let r=await i.Gy.findById(a.userId);r&&await o.emailNotificationService.sendDepositSuccessNotification({userId:r.id,email:r.email,firstName:r.firstName,lastName:r.lastName,amount:e,transactionId:t,currency:"USDT"})}catch(t){console.error("Error sending deposit success email:",t)}console.log(`Deposit completed successfully for transaction: ${t}`)}catch(e){console.error(`Error completing deposit for transaction ${t}:`,e),await i.J6.updateStatus(t,"FAILED",{failureReason:`Completion error: ${e instanceof Error?e.message:"Unknown error"}`,processedAt:new Date}),await i.AJ.create({action:"DEPOSIT_COMPLETION_ERROR",details:`Error completing deposit ${t}: ${e instanceof Error?e.message:"Unknown error"}`})}}async addTransactionForVerification(t,e){this.isRunning||console.log("Deposit verification service is not running, starting verification manually"),this.scheduleVerification(t,e)}getStatus(){return{isRunning:this.isRunning,activeVerifications:n.size,confirmationChecks:c.size}}constructor(){this.isRunning=!1}}let d=l.getInstance()},13161:(t,e,a)=>{a.d(e,{emailNotificationService:()=>o});var i=a(21111),r=a(6710);class s{async sendDepositSuccessNotification(t){try{let e=await i.gm.getEmailTemplate("deposit_success");e||console.warn('Email template "deposit_success" not found. Using default template.');let a=e?.subject||"Deposit Confirmed - HashCoreX",s=e?.htmlContent||this.getDefaultDepositSuccessTemplate(),o=e?.textContent||"";s=this.replaceVariables(s,{firstName:t.firstName,lastName:t.lastName,email:t.email,amount:t.amount.toString(),transactionId:t.transactionId,currency:t.currency}),o=this.replaceVariables(o,{firstName:t.firstName,lastName:t.lastName,email:t.email,amount:t.amount.toString(),transactionId:t.transactionId,currency:t.currency});let n=await r.XB.create({to:t.email,subject:a,template:"deposit_success",status:"PENDING"});if(await i.gm.sendEmail({to:t.email,subject:a,html:s,text:o||`Deposit Confirmed

Amount: ${t.amount} ${t.currency}
Transaction ID: ${t.transactionId}`}))return await r.XB.updateStatus(n.id,"SENT"),!0;return await r.XB.updateStatus(n.id,"FAILED","Email service error"),!1}catch(t){return console.error("Error sending deposit success notification:",t),!1}}async sendKYCStatusNotification(t){try{let e="APPROVED"===t.status?"kyc_approved":"kyc_rejected",a=await i.gm.getEmailTemplate(e);a||console.warn(`Email template "${e}" not found. Using default template.`);let s=a?.subject||`KYC ${"APPROVED"===t.status?"Approved":"Rejected"} - HashCoreX`,o=a?.htmlContent||this.getDefaultKYCTemplate(t.status),n=a?.textContent||"";o=this.replaceVariables(o,{firstName:t.firstName,lastName:t.lastName,email:t.email,status:t.status,rejectionReason:t.rejectionReason||""}),n=this.replaceVariables(n,{firstName:t.firstName,lastName:t.lastName,email:t.email,status:t.status,rejectionReason:t.rejectionReason||""});let c=await r.XB.create({to:t.email,subject:s,template:e,status:"PENDING"});if(await i.gm.sendEmail({to:t.email,subject:s,html:o,text:n||`KYC ${t.status}

${t.rejectionReason?`Reason: ${t.rejectionReason}`:"Your KYC has been approved."}`}))return await r.XB.updateStatus(c.id,"SENT"),!0;return await r.XB.updateStatus(c.id,"FAILED","Email service error"),!1}catch(t){return console.error("Error sending KYC status notification:",t),!1}}async sendWithdrawalStatusNotification(t){try{let e=this.getWithdrawalTemplateName(t.status),a=await i.gm.getEmailTemplate(e);a||console.warn(`Email template "${e}" not found. Using default template.`);let s=a?.subject||`Withdrawal ${this.getWithdrawalStatusText(t.status)} - HashCoreX`,o=a?.htmlContent||this.getDefaultWithdrawalTemplate(t.status),n=a?.textContent||"";o=this.replaceVariables(o,{firstName:t.firstName,lastName:t.lastName,email:t.email,amount:t.amount.toString(),status:t.status,transactionHash:t.transactionHash||"",rejectionReason:t.rejectionReason||"",usdtAddress:t.usdtAddress||""}),n=this.replaceVariables(n,{firstName:t.firstName,lastName:t.lastName,email:t.email,amount:t.amount.toString(),status:t.status,transactionHash:t.transactionHash||"",rejectionReason:t.rejectionReason||"",usdtAddress:t.usdtAddress||""});let c=await r.XB.create({to:t.email,subject:s,template:e,status:"PENDING"});if(await i.gm.sendEmail({to:t.email,subject:s,html:o,text:n||this.getDefaultWithdrawalText(t)}))return await r.XB.updateStatus(c.id,"SENT"),!0;return await r.XB.updateStatus(c.id,"FAILED","Email service error"),!1}catch(t){return console.error("Error sending withdrawal status notification:",t),!1}}replaceVariables(t,e){let a=t;for(let[t,i]of Object.entries(e)){let e=RegExp(`{{${t}}}`,"g");a=a.replace(e,i)}return a}getWithdrawalTemplateName(t){switch(t){case"APPROVED":return"withdrawal_approved";case"REJECTED":return"withdrawal_rejected";case"COMPLETED":return"withdrawal_completed";case"FAILED":return"withdrawal_failed";default:return"withdrawal_status"}}getWithdrawalStatusText(t){switch(t){case"APPROVED":return"Approved";case"REJECTED":return"Rejected";case"COMPLETED":return"Completed";case"FAILED":return"Failed";default:return"Updated"}}getDefaultDepositSuccessTemplate(){return`
      <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
        <div style="background: linear-gradient(135deg, #ffd60a 0%, #10b981 100%); padding: 20px; text-align: center;">
          <h1 style="color: white; margin: 0;">HashCoreX</h1>
        </div>
        <div style="padding: 30px; background: #f9f9f9;">
          <h2 style="color: #333;">Deposit Confirmed!</h2>
          <p>Hello {{firstName}},</p>
          <p>Great news! Your deposit has been successfully confirmed and credited to your account.</p>
          <div style="background: white; padding: 20px; margin: 20px 0; border-radius: 8px; border-left: 4px solid #10b981;">
            <h3 style="margin: 0 0 10px 0; color: #10b981;">Deposit Details</h3>
            <p><strong>Amount:</strong> {{amount}} {{currency}}</p>
            <p><strong>Transaction ID:</strong> {{transactionId}}</p>
            <p><strong>Status:</strong> Confirmed</p>
          </div>
          <p>Your funds are now available in your wallet and you can start using them immediately.</p>
          <p>Best regards,<br>The HashCoreX Team</p>
        </div>
        <div style="background: #333; color: white; padding: 20px; text-align: center; font-size: 12px;">
          <p>&copy; 2024 HashCoreX. All rights reserved.</p>
        </div>
      </div>
    `}getDefaultKYCTemplate(t){let e="APPROVED"===t;return`
      <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
        <div style="background: linear-gradient(135deg, #ffd60a 0%, ${e?"#10b981":"#ef4444"} 100%); padding: 20px; text-align: center;">
          <h1 style="color: white; margin: 0;">HashCoreX</h1>
        </div>
        <div style="padding: 30px; background: #f9f9f9;">
          <h2 style="color: #333;">${e?"KYC Approved!":"KYC Rejected"}</h2>
          <p>Hello {{firstName}},</p>
          <p>${e?"Congratulations! Your KYC verification has been approved. You now have full access to all platform features.":"Unfortunately, your KYC verification has been rejected. Please review the reason below and resubmit with correct documents."}</p>
          ${!e?'<div style="background: white; padding: 20px; margin: 20px 0; border-radius: 8px; border-left: 4px solid #ef4444;"><p><strong>Rejection Reason:</strong> {{rejectionReason}}</p></div>':""}
          <p>Best regards,<br>The HashCoreX Team</p>
        </div>
        <div style="background: #333; color: white; padding: 20px; text-align: center; font-size: 12px;">
          <p>&copy; 2024 HashCoreX. All rights reserved.</p>
        </div>
      </div>
    `}getDefaultWithdrawalTemplate(t){let e=t=>{switch(t){case"APPROVED":case"COMPLETED":return"#10b981";case"REJECTED":case"FAILED":return"#ef4444";default:return"#6b7280"}};return`
      <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
        <div style="background: linear-gradient(135deg, #ffd60a 0%, ${e(t)} 100%); padding: 20px; text-align: center;">
          <h1 style="color: white; margin: 0;">HashCoreX</h1>
        </div>
        <div style="padding: 30px; background: #f9f9f9;">
          <h2 style="color: #333;">Withdrawal {{status}}</h2>
          <p>Hello {{firstName}},</p>
          <p>Your withdrawal request has been {{status}}.</p>
          <div style="background: white; padding: 20px; margin: 20px 0; border-radius: 8px; border-left: 4px solid ${e(t)};">
            <h3 style="margin: 0 0 10px 0; color: ${e(t)};">Withdrawal Details</h3>
            <p><strong>Amount:</strong> {{amount}} USDT</p>
            <p><strong>Status:</strong> {{status}}</p>
            {{#if usdtAddress}}<p><strong>Address:</strong> {{usdtAddress}}</p>{{/if}}
            {{#if transactionHash}}<p><strong>Transaction Hash:</strong> {{transactionHash}}</p>{{/if}}
            {{#if rejectionReason}}<p><strong>Reason:</strong> {{rejectionReason}}</p>{{/if}}
          </div>
          <p>Best regards,<br>The HashCoreX Team</p>
        </div>
        <div style="background: #333; color: white; padding: 20px; text-align: center; font-size: 12px;">
          <p>&copy; 2024 HashCoreX. All rights reserved.</p>
        </div>
      </div>
    `}getDefaultWithdrawalText(t){let e=`Withdrawal ${t.status}

Amount: ${t.amount} USDT
Status: ${t.status}`;return t.usdtAddress&&(e+=`
Address: ${t.usdtAddress}`),t.transactionHash&&(e+=`
Transaction Hash: ${t.transactionHash}`),t.rejectionReason&&(e+=`
Reason: ${t.rejectionReason}`),e}}let o=new s},21111:(t,e,a)=>{a.d(e,{XG:()=>n,gm:()=>o});var i=a(49526),r=a(6710);class s{async getEmailConfig(){try{let t=await r.T8.getEmailSettings();if(!t||!t.smtpHost||!t.smtpUser||!t.smtpPassword)return console.warn("Email configuration not found or incomplete"),null;return{host:t.smtpHost,port:t.smtpPort||587,secure:t.smtpSecure||!1,user:t.smtpUser,password:t.smtpPassword,fromName:t.fromName||"HashCoreX",fromEmail:t.fromEmail||t.smtpUser}}catch(t){return console.error("Failed to get email configuration:",t),null}}async initializeTransporter(){try{if(this.config=await this.getEmailConfig(),!this.config)return!1;return this.transporter=i.createTransport({host:this.config.host,port:this.config.port,secure:this.config.secure,auth:{user:this.config.user,pass:this.config.password},tls:{rejectUnauthorized:!1}}),await this.transporter.verify(),console.log("Email transporter initialized successfully"),!0}catch(t){return console.error("Failed to initialize email transporter:",t),this.transporter=null,!1}}async sendEmail(t){try{if((!this.transporter||!this.config)&&!await this.initializeTransporter())throw Error("Email service not configured");let e={from:`"${this.config.fromName}" <${this.config.fromEmail}>`,to:t.to,subject:t.subject,html:t.html,text:t.text},a=await this.transporter.sendMail(e);return console.log("Email sent successfully:",a.messageId),!0}catch(t){return console.error("Failed to send email:",t),!1}}async sendOTPEmail(t,e,a,i="email_verification"){let r="password_reset"===i?"password_reset_otp":"otp_verification",s=await this.getEmailTemplate(r);if(!s)return console.error(`Email template '${r}' not found. Please ensure email templates are seeded.`),!1;let o=s.htmlContent,n=s.textContent||"";return o=(o=o.replace(/{{firstName}}/g,a||"User")).replace(/{{otp}}/g,e),n=(n=n.replace(/{{firstName}}/g,a||"User")).replace(/{{otp}}/g,e),await this.sendEmail({to:t,subject:s.subject,html:o,text:n})}async getEmailTemplate(t){try{return await r.T8.getEmailTemplate(t)}catch(t){return console.error("Failed to get email template:",t),null}}async testConnection(){try{if(this.config=await this.getEmailConfig(),!this.config)return console.error("Email configuration not found or incomplete"),!1;return this.transporter=i.createTransport({host:this.config.host,port:this.config.port,secure:this.config.secure,auth:{user:this.config.user,pass:this.config.password},tls:{rejectUnauthorized:!1}}),await this.transporter.verify(),console.log("Email connection test successful"),!0}catch(t){throw console.error("Email connection test failed:",t),this.transporter=null,t}}constructor(){this.transporter=null,this.config=null}}let o=new s,n=()=>Math.floor(1e5+9e5*Math.random()).toString()}};