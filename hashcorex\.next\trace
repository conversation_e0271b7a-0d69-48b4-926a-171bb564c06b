[{"name": "generate-buildid", "duration": 172, "timestamp": 27585647724, "id": 4, "parentId": 1, "tags": {}, "startTime": 1751209889252, "traceId": "aac076691bba3f53"}, {"name": "load-custom-routes", "duration": 245, "timestamp": 27585647965, "id": 5, "parentId": 1, "tags": {}, "startTime": 1751209889252, "traceId": "aac076691bba3f53"}, {"name": "create-dist-dir", "duration": 1694, "timestamp": 27585708684, "id": 6, "parentId": 1, "tags": {}, "startTime": 1751209889313, "traceId": "aac076691bba3f53"}, {"name": "create-pages-mapping", "duration": 160, "timestamp": 27585746998, "id": 7, "parentId": 1, "tags": {}, "startTime": 1751209889351, "traceId": "aac076691bba3f53"}, {"name": "collect-app-paths", "duration": 3776, "timestamp": 27585747192, "id": 8, "parentId": 1, "tags": {}, "startTime": 1751209889351, "traceId": "aac076691bba3f53"}, {"name": "create-app-mapping", "duration": 2913, "timestamp": 27585750995, "id": 9, "parentId": 1, "tags": {}, "startTime": 1751209889355, "traceId": "aac076691bba3f53"}, {"name": "public-dir-conflict-check", "duration": 529, "timestamp": 27585754623, "id": 10, "parentId": 1, "tags": {}, "startTime": 1751209889359, "traceId": "aac076691bba3f53"}, {"name": "generate-routes-manifest", "duration": 2514, "timestamp": 27585755368, "id": 11, "parentId": 1, "tags": {}, "startTime": 1751209889360, "traceId": "aac076691bba3f53"}, {"name": "next-build", "duration": 3621538, "timestamp": 27585548000, "id": 1, "tags": {"buildMode": "default", "isTurboBuild": "false", "version": "15.3.4", "has-custom-webpack-config": "false", "use-build-worker": "true"}, "startTime": 1751209889152, "traceId": "aac076691bba3f53"}]