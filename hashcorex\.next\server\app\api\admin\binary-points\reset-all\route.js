"use strict";(()=>{var e={};e.id=7745,e.ids=[7745],e.modules={3295:e=>{e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},12909:(e,r,t)=>{t.d(r,{DT:()=>w,DY:()=>y,HU:()=>c,Lx:()=>h,b9:()=>f,qc:()=>g});var a=t(85663),s=t(43205),i=t.n(s),n=t(6710);let o=process.env.JWT_SECRET||"fallback-secret-key",l=process.env.JWT_EXPIRES_IN||"30d",u=async e=>await a.Ay.hash(e,12),d=async(e,r)=>await a.Ay.compare(e,r),c=e=>i().sign(e,o,{expiresIn:l}),p=e=>{try{return i().verify(e,o)}catch(e){return null}},m=()=>{let e="ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789",r="HC";for(let t=0;t<8;t++)r+=e.charAt(Math.floor(Math.random()*e.length));return r},f=async e=>{let r=e.headers.get("authorization")?.replace("Bearer ","")||e.cookies.get("auth-token")?.value;if(!r)return{authenticated:!1,user:null};let t=p(r);if(!t)return{authenticated:!1,user:null};let a=await n.Gy.findByEmail(t.email);return a?{authenticated:!0,user:a}:{authenticated:!1,user:null}},y=async e=>{let r,a;if(await n.Gy.findByEmail(e.email))throw Error("User already exists with this email");if(e.referralCode){let t=await n.Gy.findByReferralId(e.referralCode);if(!t)throw Error("Invalid referral code");r=t.id}let s=await u(e.password),i=!1;do a=m(),i=!await n.Gy.findByReferralId(a);while(!i);let o=await n.Gy.create({email:e.email,firstName:e.firstName,lastName:e.lastName,password:s,referralId:a});if(r){let{placeUserByReferralType:a}=await t.e(2746).then(t.bind(t,2746)),s="general";"left"===e.placementSide?s="left":"right"===e.placementSide&&(s="right"),await a(r,o.id,s)}return{id:o.id,email:o.email,referralId:o.referralId,kycStatus:o.kycStatus}},h=async e=>{let r=await n.Gy.findByEmail(e.email);if(!r||!await d(e.password,r.password))throw Error("Invalid email or password");return{token:c({userId:r.id,email:r.email}),user:{id:r.id,email:r.email,referralId:r.referralId,kycStatus:r.kycStatus}}},w=e=>/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(e),g=async e=>{let r=await n.Gy.findById(e);return r?.role==="ADMIN"}},13354:(e,r,t)=>{t.r(r),t.d(r,{patchFetch:()=>y,routeModule:()=>c,serverHooks:()=>f,workAsyncStorage:()=>p,workUnitAsyncStorage:()=>m});var a={};t.r(a),t.d(a,{POST:()=>d});var s=t(96559),i=t(48088),n=t(37719),o=t(32190),l=t(12909),u=t(31183);async function d(e){try{let{authenticated:r,user:t}=await (0,l.b9)(e);if(!r||!t)return o.NextResponse.json({success:!1,error:"Authentication required"},{status:401});if("ADMIN"!==t.role)return o.NextResponse.json({success:!1,error:"Admin access required"},{status:403});console.log("Starting binary points reset for all users...");let a=await u.prisma.binaryPoints.findMany({select:{userId:!0,leftPoints:!0,rightPoints:!0,user:{select:{email:!0}}}});console.log(`Found ${a.length} users with binary points to reset`);let s=await u.prisma.binaryPoints.updateMany({data:{leftPoints:0,rightPoints:0,matchedPoints:0,flushDate:new Date}});return console.log(`Reset binary points for ${s.count} records`),await u.prisma.systemLog.create({data:{action:"ADMIN_BINARY_POINTS_RESET_ALL",details:{adminId:t.id,adminEmail:t.email,resetCount:s.count,usersAffected:a.map(e=>({userId:e.userId,email:e.user.email,previousLeftPoints:e.leftPoints,previousRightPoints:e.rightPoints})),timestamp:new Date().toISOString()}}}),o.NextResponse.json({success:!0,message:`Successfully reset binary points for ${s.count} users`,data:{resetCount:s.count,usersAffected:a.length}})}catch(e){return console.error("Binary points reset error:",e),o.NextResponse.json({success:!1,error:"Failed to reset binary points"},{status:500})}}let c=new s.AppRouteRouteModule({definition:{kind:i.RouteKind.APP_ROUTE,page:"/api/admin/binary-points/reset-all/route",pathname:"/api/admin/binary-points/reset-all",filename:"route",bundlePath:"app/api/admin/binary-points/reset-all/route"},resolvedPagePath:"C:\\Users\\<USER>\\Desktop\\Hash_Minings\\hashcorex\\src\\app\\api\\admin\\binary-points\\reset-all\\route.ts",nextConfigOutput:"",userland:a}),{workAsyncStorage:p,workUnitAsyncStorage:m,serverHooks:f}=c;function y(){return(0,n.patchFetch)({workAsyncStorage:p,workUnitAsyncStorage:m})}},27910:e=>{e.exports=require("stream")},28354:e=>{e.exports=require("util")},29294:e=>{e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},44870:e=>{e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},55511:e=>{e.exports=require("crypto")},63033:e=>{e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},79428:e=>{e.exports=require("buffer")},96330:e=>{e.exports=require("@prisma/client")}};var r=require("../../../../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),a=r.X(0,[4447,580,5315,3306],()=>t(13354));module.exports=a})();