"use strict";(()=>{var e={};e.id=3470,e.ids=[3470,9046],e.modules={803:(e,t,r)=>{r.d(t,{x:()=>a});var n=r(92731),s=r(2746),i=r(6710);class o{constructor(){this.tasks=new Map,this.isInitialized=!1}static getInstance(){return o.instance||(o.instance=new o),o.instance}async initialize(){if(this.isInitialized)return void console.log("Scheduler service already initialized");console.log("Initializing server-side scheduler service...");try{this.scheduleDailyROI(),this.scheduleWeeklyPayout(),this.scheduleBinaryMatching(),this.scheduleDepositProcessing(),this.isInitialized=!0,console.log("Server-side scheduler service initialized successfully"),await i.AJ.create({action:"SCHEDULER_SERVICE_INITIALIZED",details:{tasksScheduled:this.tasks.size,initTime:new Date().toISOString(),tasks:Array.from(this.tasks.keys())}})}catch(e){throw console.error("Error initializing scheduler service:",e),e}}scheduleDailyROI(){let e="daily-roi",t=new Date,r=new Date;r.setUTCHours(0,1,0,0),r<=t&&r.setUTCDate(r.getUTCDate()+1);let n=r.getTime()-t.getTime();console.log(`Scheduling daily ROI calculation for ${r.toISOString()}`),setTimeout(()=>{this.runDailyROI();let t=setInterval(()=>{this.runDailyROI()},864e5);this.tasks.set(e,{name:e,interval:t,isRunning:!1,lastRun:null,nextRun:new Date(Date.now()+864e5),runCount:0})},n),this.tasks.set(e,{name:e,interval:null,isRunning:!1,lastRun:null,nextRun:r,runCount:0})}scheduleWeeklyPayout(){let e="weekly-payout",t=this.getNextWeeklyTime(6,15),r=t.getTime()-Date.now();console.log(`Scheduling weekly payout for ${t.toISOString()}`),setTimeout(()=>{this.runWeeklyPayout();let t=setInterval(()=>{this.runWeeklyPayout()},6048e5);this.tasks.set(e,{name:e,interval:t,isRunning:!1,lastRun:null,nextRun:new Date(Date.now()+6048e5),runCount:0})},r),this.tasks.set(e,{name:e,interval:null,isRunning:!1,lastRun:null,nextRun:t,runCount:0})}scheduleBinaryMatching(){let e="binary-matching",t=this.getNextWeeklyTime(6,15),r=t.getTime()-Date.now();console.log(`Scheduling binary matching for ${t.toISOString()}`),setTimeout(()=>{this.runBinaryMatching();let t=setInterval(()=>{this.runBinaryMatching()},6048e5);this.tasks.set(e,{name:e,interval:t,isRunning:!1,lastRun:null,nextRun:new Date(Date.now()+6048e5),runCount:0})},r),this.tasks.set(e,{name:e,interval:null,isRunning:!1,lastRun:null,nextRun:t,runCount:0})}scheduleDepositProcessing(){let e="deposit-processing";console.log("Scheduling deposit processing every 10 minutes");let t=setInterval(()=>{this.runDepositProcessing()},6e5);this.runDepositProcessing(),this.tasks.set(e,{name:e,interval:t,isRunning:!1,lastRun:null,nextRun:new Date(Date.now()+6e5),runCount:0})}getNextWeeklyTime(e,t){let r=new Date,n=new Date;n.setUTCHours(t,0,0,0);let s=e-r.getUTCDay();return(s<0||0===s&&r.getUTCHours()>=t)&&(s+=7),n.setUTCDate(r.getUTCDate()+s),n}async runDailyROI(){let e=this.tasks.get("daily-roi");if(!e||e.isRunning)return void console.log("Daily ROI task already running or not found");e.isRunning=!0,e.lastRun=new Date,e.runCount++;try{console.log("Starting scheduled daily ROI calculation...");let t=await (0,n.eB)();console.log(`Expired ${t} old mining units`);let r=await (0,n.WL)();console.log(`Processed ${r.length} mining units for daily ROI`);let s=r.reduce((e,t)=>e+t.earnings,0),o=r.filter(e=>e.expired).length;await i.AJ.create({action:"DAILY_ROI_SCHEDULED_EXECUTED",details:{unitsProcessed:r.length,totalEarnings:s,expiredUnits:o,oldUnitsExpired:t,executionTime:new Date().toISOString(),runCount:e.runCount}}),console.log(`Daily ROI calculation completed successfully (run #${e.runCount})`)}catch(t){console.error("Daily ROI scheduled task error:",t),await i.AJ.create({action:"DAILY_ROI_SCHEDULED_ERROR",details:{error:t instanceof Error?t.message:"Unknown error",stack:t instanceof Error?t.stack:void 0,timestamp:new Date().toISOString(),runCount:e.runCount}})}finally{e.isRunning=!1,e.nextRun=new Date(Date.now()+864e5)}}async runWeeklyPayout(){let e=this.tasks.get("weekly-payout");if(!e||e.isRunning)return void console.log("Weekly payout task already running or not found");e.isRunning=!0,e.lastRun=new Date,e.runCount++;try{console.log("Starting scheduled weekly earnings payout...");let t=await (0,n.Oh)();console.log(`Processed earnings for ${t.length} users`);let r=t.reduce((e,t)=>e+t.totalEarnings,0);await i.AJ.create({action:"WEEKLY_PAYOUT_SCHEDULED_EXECUTED",details:{usersProcessed:t.length,totalDistributed:r,executionTime:new Date().toISOString(),payoutDay:"Saturday",payoutTime:"15:00 UTC",runCount:e.runCount}}),console.log(`Weekly payout completed successfully (run #${e.runCount})`)}catch(t){console.error("Weekly payout scheduled task error:",t),await i.AJ.create({action:"WEEKLY_PAYOUT_SCHEDULED_ERROR",details:{error:t instanceof Error?t.message:"Unknown error",stack:t instanceof Error?t.stack:void 0,timestamp:new Date().toISOString(),runCount:e.runCount}})}finally{e.isRunning=!1,e.nextRun=new Date(Date.now()+6048e5)}}async runBinaryMatching(){let e=this.tasks.get("binary-matching");if(!e||e.isRunning)return void console.log("Binary matching task already running or not found");e.isRunning=!0,e.lastRun=new Date,e.runCount++;try{console.log("Starting scheduled binary matching...");let t=await (0,s.E5)(),r=t.reduce((e,t)=>e+t.payout,0),n=t.reduce((e,t)=>e+t.matchedPoints,0);await i.AJ.create({action:"BINARY_MATCHING_SCHEDULED_EXECUTED",details:{usersProcessed:t.length,totalPayouts:r,totalMatchedPoints:n,executionTime:new Date().toISOString(),matchingTime:"15:00 UTC (Weekly)",runCount:e.runCount}}),console.log(`Binary matching completed successfully (run #${e.runCount})`)}catch(t){console.error("Binary matching scheduled task error:",t),await i.AJ.create({action:"BINARY_MATCHING_SCHEDULED_ERROR",details:{error:t instanceof Error?t.message:"Unknown error",stack:t instanceof Error?t.stack:void 0,timestamp:new Date().toISOString(),runCount:e.runCount}})}finally{e.isRunning=!1,e.nextRun=new Date(Date.now()+6048e5)}}async runDepositProcessing(){let e=this.tasks.get("deposit-processing");if(!e||e.isRunning)return void console.log("Deposit processing task already running or not found");e.isRunning=!0,e.lastRun=new Date,e.runCount++;try{console.log("Starting scheduled deposit processing...");let{processDeposits:t}=await Promise.all([r.e(9480),r.e(9046)]).then(r.bind(r,79046)),n=await t();await i.AJ.create({action:"DEPOSIT_PROCESSING_SCHEDULED_EXECUTED",details:{...n,executionTime:new Date().toISOString(),runCount:e.runCount}}),console.log(`Deposit processing completed successfully (run #${e.runCount})`)}catch(t){console.error("Deposit processing scheduled task error:",t),await i.AJ.create({action:"DEPOSIT_PROCESSING_SCHEDULED_ERROR",details:{error:t instanceof Error?t.message:"Unknown error",stack:t instanceof Error?t.stack:void 0,timestamp:new Date().toISOString(),runCount:e.runCount}})}finally{e.isRunning=!1,e.nextRun=new Date(Date.now()+6e5)}}stop(){for(let[e,t]of(console.log("Stopping scheduler service..."),this.tasks))t.interval&&(clearInterval(t.interval),console.log(`Stopped task: ${e}`));this.tasks.clear(),this.isInitialized=!1,console.log("Scheduler service stopped")}getStatus(){return{isInitialized:this.isInitialized,totalTasks:this.tasks.size,tasks:Array.from(this.tasks.entries()).map(([e,t])=>({name:e,isRunning:t.isRunning,lastRun:t.lastRun?.toISOString()||null,nextRun:t.nextRun?.toISOString()||null,runCount:t.runCount}))}}async triggerTask(e){switch(e){case"daily-roi":await this.runDailyROI();break;case"weekly-payout":await this.runWeeklyPayout();break;case"binary-matching":await this.runBinaryMatching();break;case"deposit-processing":await this.runDepositProcessing();break;default:throw Error(`Unknown task: ${e}`)}}}let a=o.getInstance()},3295:e=>{e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},12909:(e,t,r)=>{r.d(t,{DT:()=>f,DY:()=>y,HU:()=>d,Lx:()=>m,b9:()=>h,qc:()=>w});var n=r(85663),s=r(43205),i=r.n(s),o=r(6710);let a=process.env.JWT_SECRET||"fallback-secret-key",l=process.env.JWT_EXPIRES_IN||"30d",c=async e=>await n.Ay.hash(e,12),u=async(e,t)=>await n.Ay.compare(e,t),d=e=>i().sign(e,a,{expiresIn:l}),g=e=>{try{return i().verify(e,a)}catch(e){return null}},p=()=>{let e="ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789",t="HC";for(let r=0;r<8;r++)t+=e.charAt(Math.floor(Math.random()*e.length));return t},h=async e=>{let t=e.headers.get("authorization")?.replace("Bearer ","")||e.cookies.get("auth-token")?.value;if(!t)return{authenticated:!1,user:null};let r=g(t);if(!r)return{authenticated:!1,user:null};let n=await o.Gy.findByEmail(r.email);return n?{authenticated:!0,user:n}:{authenticated:!1,user:null}},y=async e=>{let t,n;if(await o.Gy.findByEmail(e.email))throw Error("User already exists with this email");if(e.referralCode){let r=await o.Gy.findByReferralId(e.referralCode);if(!r)throw Error("Invalid referral code");t=r.id}let s=await c(e.password),i=!1;do n=p(),i=!await o.Gy.findByReferralId(n);while(!i);let a=await o.Gy.create({email:e.email,firstName:e.firstName,lastName:e.lastName,password:s,referralId:n});if(t){let{placeUserByReferralType:n}=await r.e(2746).then(r.bind(r,2746)),s="general";"left"===e.placementSide?s="left":"right"===e.placementSide&&(s="right"),await n(t,a.id,s)}return{id:a.id,email:a.email,referralId:a.referralId,kycStatus:a.kycStatus}},m=async e=>{let t=await o.Gy.findByEmail(e.email);if(!t||!await u(e.password,t.password))throw Error("Invalid email or password");return{token:d({userId:t.id,email:t.email}),user:{id:t.id,email:t.email,referralId:t.referralId,kycStatus:t.kycStatus}}},f=e=>/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(e),w=async e=>{let t=await o.Gy.findById(e);return t?.role==="ADMIN"}},21820:e=>{e.exports=require("os")},24923:(e,t,r)=>{r.r(t),r.d(t,{patchFetch:()=>f,routeModule:()=>p,serverHooks:()=>m,workAsyncStorage:()=>h,workUnitAsyncStorage:()=>y});var n={};r.r(n),r.d(n,{GET:()=>g});var s=r(96559),i=r(48088),o=r(37719),a=r(32190),l=r(12909),c=r(803),u=r(1827),d=r(79046);async function g(e){try{let{authenticated:t,user:r}=await (0,l.b9)(e);if(!t||!r)return a.NextResponse.json({success:!1,error:"Not authenticated"},{status:401});if("ADMIN"!==r.role)return a.NextResponse.json({success:!1,error:"Access denied"},{status:403});let n=c.x.getStatus(),s=u.j.getStatus(),i=await (0,d.E)();return a.NextResponse.json({success:!0,data:{scheduler:n,depositVerificationService:s,depositProcessing:i,serverTime:new Date().toISOString()}})}catch(e){return console.error("Error getting scheduler status:",e),a.NextResponse.json({success:!1,error:"Failed to get scheduler status"},{status:500})}}let p=new s.AppRouteRouteModule({definition:{kind:i.RouteKind.APP_ROUTE,page:"/api/admin/scheduler/status/route",pathname:"/api/admin/scheduler/status",filename:"route",bundlePath:"app/api/admin/scheduler/status/route"},resolvedPagePath:"C:\\Users\\<USER>\\Desktop\\Hash_Minings\\hashcorex\\src\\app\\api\\admin\\scheduler\\status\\route.ts",nextConfigOutput:"",userland:n}),{workAsyncStorage:h,workUnitAsyncStorage:y,serverHooks:m}=p;function f(){return(0,o.patchFetch)({workAsyncStorage:h,workUnitAsyncStorage:y})}},27910:e=>{e.exports=require("stream")},28354:e=>{e.exports=require("util")},29021:e=>{e.exports=require("fs")},29294:e=>{e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:e=>{e.exports=require("path")},34631:e=>{e.exports=require("tls")},37366:e=>{e.exports=require("dns")},44870:e=>{e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},55511:e=>{e.exports=require("crypto")},55591:e=>{e.exports=require("https")},63033:e=>{e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},74075:e=>{e.exports=require("zlib")},79046:(e,t,r)=>{r.d(t,{E:()=>u,processDeposits:()=>o});var n=r(6710),s=r(31183),i=r(59480);async function o(){try{console.log("Starting deposit processing...");let e=parseInt(await n.rs.get("minConfirmations")||"10"),t=await n.rs.get("depositAddress");if(!t)throw Error("No deposit address configured");let r=await a(t),s=await l(t,e),i=await c(),o={pendingVerificationProcessed:r.processed,waitingConfirmationsProcessed:s.processed,confirmedDepositsProcessed:i.processed,totalProcessed:r.processed+s.processed+i.processed,errors:[...r.errors,...s.errors,...i.errors]};return console.log(`Deposit processing completed: ${o.totalProcessed} deposits processed`),o}catch(e){throw console.error("Deposit processing error:",e),e}}async function a(e){let t={processed:0,errors:[]};try{let r=await n.J6.getPendingVerificationDeposits();for(let s of(console.log(`Found ${r.length} deposits pending verification`),r))try{let r=await (0,i.gp)(s.transactionId,e,1);r.isValid?(await n.J6.updateStatus(s.transactionId,"WAITING_FOR_CONFIRMATIONS"),await n.J6.updateConfirmations(s.transactionId,r.confirmations),console.log(`Verified deposit ${s.transactionId} with ${r.confirmations} confirmations`),t.processed++):console.log(`Deposit ${s.transactionId} not yet found on blockchain`)}catch(r){let e=`Error verifying deposit ${s.transactionId}: ${r instanceof Error?r.message:"Unknown error"}`;console.error(e),t.errors.push(e)}}catch(r){let e=`Error processing pending verification deposits: ${r instanceof Error?r.message:"Unknown error"}`;console.error(e),t.errors.push(e)}return t}async function l(e,t){let r={processed:0,errors:[]};try{let s=await n.J6.getWaitingForConfirmationsDeposits();for(let o of(console.log(`Found ${s.length} deposits waiting for confirmations`),s))try{let s=await (0,i.gp)(o.transactionId,e,1);s.isValid?(await n.J6.updateConfirmations(o.transactionId,s.confirmations),s.confirmations>=t?(await n.J6.updateStatus(o.transactionId,"CONFIRMED"),console.log(`Deposit ${o.transactionId} confirmed with ${s.confirmations} confirmations`),r.processed++):console.log(`Deposit ${o.transactionId} has ${s.confirmations}/${t} confirmations`)):console.log(`Deposit ${o.transactionId} is no longer valid during confirmation check`)}catch(t){let e=`Error checking confirmations for deposit ${o.transactionId}: ${t instanceof Error?t.message:"Unknown error"}`;console.error(e),r.errors.push(e)}}catch(t){let e=`Error processing waiting for confirmations deposits: ${t instanceof Error?t.message:"Unknown error"}`;console.error(e),r.errors.push(e)}return r}async function c(){let e={processed:0,errors:[]};try{let t=await n.J6.getConfirmedDeposits();for(let r of(console.log(`Found ${t.length} confirmed deposits to process`),t))try{await s.prisma.$transaction(async e=>{await n.k_.addDeposit(r.userId,r.amount),await n.DR.create({userId:r.userId,type:"DEPOSIT",amount:r.amount,status:"COMPLETED",reference:`deposit:${r.transactionId}`,description:`USDT Deposit - ${r.transactionId}`}),await n.J6.updateStatus(r.transactionId,"COMPLETED")}),console.log(`Processed confirmed deposit ${r.transactionId} for user ${r.userId}: $${r.amount}`),e.processed++,await n.AJ.create({action:"DEPOSIT_PROCESSED",userId:r.userId,details:{transactionId:r.transactionId,amount:r.amount,tronAddress:r.tronAddress,confirmations:r.confirmations,processedAt:new Date().toISOString()}})}catch(s){let t=`Error processing confirmed deposit ${r.transactionId}: ${s instanceof Error?s.message:"Unknown error"}`;console.error(t),e.errors.push(t),await n.AJ.create({action:"DEPOSIT_PROCESSING_ERROR",userId:r.userId,details:{transactionId:r.transactionId,error:s instanceof Error?s.message:"Unknown error",timestamp:new Date().toISOString()}})}}catch(r){let t=`Error processing confirmed deposits: ${r instanceof Error?r.message:"Unknown error"}`;console.error(t),e.errors.push(t)}return e}async function u(){try{let e=await n.J6.getPendingVerificationDeposits(),t=await n.J6.getWaitingForConfirmationsDeposits(),r=await n.J6.getConfirmedDeposits();return{pendingVerification:e.length,waitingConfirmations:t.length,confirmed:r.length,total:e.length+t.length+r.length}}catch(e){throw console.error("Error getting deposit processing status:",e),e}}},79428:e=>{e.exports=require("buffer")},79551:e=>{e.exports=require("url")},79646:e=>{e.exports=require("child_process")},81630:e=>{e.exports=require("http")},91645:e=>{e.exports=require("net")},94735:e=>{e.exports=require("events")},96330:e=>{e.exports=require("@prisma/client")}};var t=require("../../../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),n=t.X(0,[4447,580,5315,9526,3306,2746,5112,9480,3161,1827],()=>r(24923));module.exports=n})();