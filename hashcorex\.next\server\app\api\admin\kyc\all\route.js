"use strict";(()=>{var e={};e.id=6765,e.ids=[6765],e.modules={3295:e=>{e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},12909:(e,r,t)=>{t.d(r,{DT:()=>w,DY:()=>y,HU:()=>c,Lx:()=>h,b9:()=>f,qc:()=>x});var a=t(85663),s=t(43205),i=t.n(s),n=t(6710);let d=process.env.JWT_SECRET||"fallback-secret-key",o=process.env.JWT_EXPIRES_IN||"30d",l=async e=>await a.Ay.hash(e,12),u=async(e,r)=>await a.Ay.compare(e,r),c=e=>i().sign(e,d,{expiresIn:o}),p=e=>{try{return i().verify(e,d)}catch(e){return null}},m=()=>{let e="ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789",r="HC";for(let t=0;t<8;t++)r+=e.charAt(Math.floor(Math.random()*e.length));return r},f=async e=>{let r=e.headers.get("authorization")?.replace("Bearer ","")||e.cookies.get("auth-token")?.value;if(!r)return{authenticated:!1,user:null};let t=p(r);if(!t)return{authenticated:!1,user:null};let a=await n.Gy.findByEmail(t.email);return a?{authenticated:!0,user:a}:{authenticated:!1,user:null}},y=async e=>{let r,a;if(await n.Gy.findByEmail(e.email))throw Error("User already exists with this email");if(e.referralCode){let t=await n.Gy.findByReferralId(e.referralCode);if(!t)throw Error("Invalid referral code");r=t.id}let s=await l(e.password),i=!1;do a=m(),i=!await n.Gy.findByReferralId(a);while(!i);let d=await n.Gy.create({email:e.email,firstName:e.firstName,lastName:e.lastName,password:s,referralId:a});if(r){let{placeUserByReferralType:a}=await t.e(2746).then(t.bind(t,2746)),s="general";"left"===e.placementSide?s="left":"right"===e.placementSide&&(s="right"),await a(r,d.id,s)}return{id:d.id,email:d.email,referralId:d.referralId,kycStatus:d.kycStatus}},h=async e=>{let r=await n.Gy.findByEmail(e.email);if(!r||!await u(e.password,r.password))throw Error("Invalid email or password");return{token:c({userId:r.id,email:r.email}),user:{id:r.id,email:r.email,referralId:r.referralId,kycStatus:r.kycStatus}}},w=e=>/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(e),x=async e=>{let r=await n.Gy.findById(e);return r?.role==="ADMIN"}},27910:e=>{e.exports=require("stream")},28354:e=>{e.exports=require("util")},29294:e=>{e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},44870:e=>{e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},55511:e=>{e.exports=require("crypto")},63033:e=>{e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},63138:(e,r,t)=>{t.r(r),t.d(r,{patchFetch:()=>y,routeModule:()=>c,serverHooks:()=>f,workAsyncStorage:()=>p,workUnitAsyncStorage:()=>m});var a={};t.r(a),t.d(a,{GET:()=>u});var s=t(96559),i=t(48088),n=t(37719),d=t(32190),o=t(12909),l=t(31183);async function u(e){try{let{authenticated:r,user:t}=await (0,o.b9)(e);if(!r||!t)return d.NextResponse.json({success:!1,error:"Not authenticated"},{status:401});if(!await (0,o.qc)(t.id))return d.NextResponse.json({success:!1,error:"Admin access required"},{status:403});let a=await l.prisma.kYCDocument.findMany({include:{user:{select:{id:!0,firstName:!0,lastName:!0,email:!0,referralId:!0,kycStatus:!0,createdAt:!0}}},orderBy:{createdAt:"desc"}}),s=new Map;for(let e of a){let r=e.userId;s.has(r)||s.set(r,{userId:r,user:{firstName:e.user.firstName,lastName:e.user.lastName,email:e.user.email,referralId:e.user.referralId},documents:[],status:e.user.kycStatus,submittedAt:e.createdAt.toISOString()}),s.get(r).documents.push({id:e.id,userId:e.userId,documentType:e.documentType,idType:e.idType,documentSide:e.documentSide,documentUrl:e.filePath,status:e.status,submittedAt:e.createdAt.toISOString(),reviewedAt:e.reviewedAt?.toISOString(),rejectionReason:e.rejectionReason})}let i=Array.from(s.values());return d.NextResponse.json({success:!0,data:i})}catch(e){return console.error("KYC all fetch error:",e),d.NextResponse.json({success:!1,error:"Failed to fetch KYC data"},{status:500})}}let c=new s.AppRouteRouteModule({definition:{kind:i.RouteKind.APP_ROUTE,page:"/api/admin/kyc/all/route",pathname:"/api/admin/kyc/all",filename:"route",bundlePath:"app/api/admin/kyc/all/route"},resolvedPagePath:"C:\\Users\\<USER>\\Desktop\\Hash_Minings\\hashcorex\\src\\app\\api\\admin\\kyc\\all\\route.ts",nextConfigOutput:"",userland:a}),{workAsyncStorage:p,workUnitAsyncStorage:m,serverHooks:f}=c;function y(){return(0,n.patchFetch)({workAsyncStorage:p,workUnitAsyncStorage:m})}},79428:e=>{e.exports=require("buffer")},96330:e=>{e.exports=require("@prisma/client")}};var r=require("../../../../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),a=r.X(0,[4447,580,5315,3306],()=>t(63138));module.exports=a})();