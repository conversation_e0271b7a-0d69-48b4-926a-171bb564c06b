"use strict";(()=>{var e={};e.id=3008,e.ids=[3008],e.modules={3295:e=>{e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},12909:(e,r,t)=>{t.d(r,{DT:()=>w,DY:()=>h,HU:()=>l,Lx:()=>f,b9:()=>y,qc:()=>k});var a=t(85663),s=t(43205),i=t.n(s),n=t(6710);let o=process.env.JWT_SECRET||"fallback-secret-key",d=process.env.JWT_EXPIRES_IN||"30d",c=async e=>await a.Ay.hash(e,12),u=async(e,r)=>await a.Ay.compare(e,r),l=e=>i().sign(e,o,{expiresIn:d}),m=e=>{try{return i().verify(e,o)}catch(e){return null}},p=()=>{let e="ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789",r="HC";for(let t=0;t<8;t++)r+=e.charAt(Math.floor(Math.random()*e.length));return r},y=async e=>{let r=e.headers.get("authorization")?.replace("Bearer ","")||e.cookies.get("auth-token")?.value;if(!r)return{authenticated:!1,user:null};let t=m(r);if(!t)return{authenticated:!1,user:null};let a=await n.Gy.findByEmail(t.email);return a?{authenticated:!0,user:a}:{authenticated:!1,user:null}},h=async e=>{let r,a;if(await n.Gy.findByEmail(e.email))throw Error("User already exists with this email");if(e.referralCode){let t=await n.Gy.findByReferralId(e.referralCode);if(!t)throw Error("Invalid referral code");r=t.id}let s=await c(e.password),i=!1;do a=p(),i=!await n.Gy.findByReferralId(a);while(!i);let o=await n.Gy.create({email:e.email,firstName:e.firstName,lastName:e.lastName,password:s,referralId:a});if(r){let{placeUserByReferralType:a}=await t.e(2746).then(t.bind(t,2746)),s="general";"left"===e.placementSide?s="left":"right"===e.placementSide&&(s="right"),await a(r,o.id,s)}return{id:o.id,email:o.email,referralId:o.referralId,kycStatus:o.kycStatus}},f=async e=>{let r=await n.Gy.findByEmail(e.email);if(!r||!await u(e.password,r.password))throw Error("Invalid email or password");return{token:l({userId:r.id,email:r.email}),user:{id:r.id,email:r.email,referralId:r.referralId,kycStatus:r.kycStatus}}},w=e=>/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(e),k=async e=>{let r=await n.Gy.findById(e);return r?.role==="ADMIN"}},27910:e=>{e.exports=require("stream")},28354:e=>{e.exports=require("util")},29294:e=>{e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},44870:e=>{e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},55511:e=>{e.exports=require("crypto")},63033:e=>{e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},78231:(e,r,t)=>{t.r(r),t.d(r,{patchFetch:()=>h,routeModule:()=>l,serverHooks:()=>y,workAsyncStorage:()=>m,workUnitAsyncStorage:()=>p});var a={};t.r(a),t.d(a,{GET:()=>u});var s=t(96559),i=t(48088),n=t(37719),o=t(32190),d=t(12909),c=t(31183);async function u(e){try{let{authenticated:r,user:t}=await (0,d.b9)(e);if(!r||!t)return o.NextResponse.json({success:!1,error:"Not authenticated"},{status:401});if(!await (0,d.qc)(t.id))return o.NextResponse.json({success:!1,error:"Admin access required"},{status:403});let a=new URL(e.url),s=a.searchParams.get("search")||"",i=a.searchParams.get("status")||"ALL",n=parseInt(a.searchParams.get("page")||"1"),u=parseInt(a.searchParams.get("limit")||"20"),l=a.searchParams.get("sortBy")||"createdAt",m=a.searchParams.get("sortOrder")||"desc",p={};s&&(p.OR=[{firstName:{contains:s,mode:"insensitive"}},{lastName:{contains:s,mode:"insensitive"}},{email:{contains:s,mode:"insensitive"}},{referralId:{contains:s,mode:"insensitive"}}]),"ALL"!==i&&(p.kycStatus=i);let y=await c.prisma.user.findMany({where:{...p,kycDocuments:{some:{}}},include:{kycDocuments:{orderBy:{createdAt:"desc"}}},orderBy:{[l]:m},skip:(n-1)*u,take:u}),h=await c.prisma.user.count({where:{...p,kycDocuments:{some:{}}}}),f=y.map(e=>({userId:e.id,user:{firstName:e.firstName,lastName:e.lastName,email:e.email,referralId:e.referralId},documents:e.kycDocuments.map(e=>({id:e.id,documentType:e.documentType,idType:e.idType,documentSide:e.documentSide,documentUrl:e.filePath,status:e.status,submittedAt:e.createdAt.toISOString(),reviewedAt:e.reviewedAt?.toISOString(),rejectionReason:e.rejectionReason})),status:e.kycStatus,submittedAt:e.kycDocuments[0]?.createdAt.toISOString()||e.createdAt.toISOString()})),w=Math.ceil(h/u);return o.NextResponse.json({success:!0,data:{users:f,pagination:{currentPage:n,totalPages:w,totalCount:h,hasNext:n<w,hasPrev:n>1,limit:u},filters:{searchTerm:s,status:i,sortBy:l,sortOrder:m}}})}catch(e){return console.error("KYC search error:",e),o.NextResponse.json({success:!1,error:"Failed to search KYC data"},{status:500})}}let l=new s.AppRouteRouteModule({definition:{kind:i.RouteKind.APP_ROUTE,page:"/api/admin/kyc/search/route",pathname:"/api/admin/kyc/search",filename:"route",bundlePath:"app/api/admin/kyc/search/route"},resolvedPagePath:"C:\\Users\\<USER>\\Desktop\\Hash_Minings\\hashcorex\\src\\app\\api\\admin\\kyc\\search\\route.ts",nextConfigOutput:"",userland:a}),{workAsyncStorage:m,workUnitAsyncStorage:p,serverHooks:y}=l;function h(){return(0,n.patchFetch)({workAsyncStorage:m,workUnitAsyncStorage:p})}},79428:e=>{e.exports=require("buffer")},96330:e=>{e.exports=require("@prisma/client")}};var r=require("../../../../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),a=r.X(0,[4447,580,5315,3306],()=>t(78231));module.exports=a})();