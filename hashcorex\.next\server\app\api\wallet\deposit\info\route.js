"use strict";(()=>{var e={};e.id=6377,e.ids=[6377],e.modules={3295:e=>{e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},12909:(e,t,r)=>{r.d(t,{DT:()=>I,DY:()=>w,HU:()=>p,Lx:()=>h,b9:()=>m,qc:()=>y});var a=r(85663),s=r(43205),i=r.n(s),n=r(6710);let o=process.env.JWT_SECRET||"fallback-secret-key",l=process.env.JWT_EXPIRES_IN||"30d",d=async e=>await a.Ay.hash(e,12),u=async(e,t)=>await a.Ay.compare(e,t),p=e=>i().sign(e,o,{expiresIn:l}),c=e=>{try{return i().verify(e,o)}catch(e){return null}},f=()=>{let e="ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789",t="HC";for(let r=0;r<8;r++)t+=e.charAt(Math.floor(Math.random()*e.length));return t},m=async e=>{let t=e.headers.get("authorization")?.replace("Bearer ","")||e.cookies.get("auth-token")?.value;if(!t)return{authenticated:!1,user:null};let r=c(t);if(!r)return{authenticated:!1,user:null};let a=await n.Gy.findByEmail(r.email);return a?{authenticated:!0,user:a}:{authenticated:!1,user:null}},w=async e=>{let t,a;if(await n.Gy.findByEmail(e.email))throw Error("User already exists with this email");if(e.referralCode){let r=await n.Gy.findByReferralId(e.referralCode);if(!r)throw Error("Invalid referral code");t=r.id}let s=await d(e.password),i=!1;do a=f(),i=!await n.Gy.findByReferralId(a);while(!i);let o=await n.Gy.create({email:e.email,firstName:e.firstName,lastName:e.lastName,password:s,referralId:a});if(t){let{placeUserByReferralType:a}=await r.e(2746).then(r.bind(r,2746)),s="general";"left"===e.placementSide?s="left":"right"===e.placementSide&&(s="right"),await a(t,o.id,s)}return{id:o.id,email:o.email,referralId:o.referralId,kycStatus:o.kycStatus}},h=async e=>{let t=await n.Gy.findByEmail(e.email);if(!t||!await u(e.password,t.password))throw Error("Invalid email or password");return{token:p({userId:t.id,email:t.email}),user:{id:t.id,email:t.email,referralId:t.referralId,kycStatus:t.kycStatus}}},I=e=>/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(e),y=async e=>{let t=await n.Gy.findById(e);return t?.role==="ADMIN"}},27910:e=>{e.exports=require("stream")},28354:e=>{e.exports=require("util")},29294:e=>{e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},44870:e=>{e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},54027:(e,t,r)=>{r.r(t),r.d(t,{patchFetch:()=>h,routeModule:()=>c,serverHooks:()=>w,workAsyncStorage:()=>f,workUnitAsyncStorage:()=>m});var a={};r.r(a),r.d(a,{GET:()=>p});var s=r(96559),i=r(48088),n=r(37719),o=r(32190),l=r(12909),d=r(6710),u=r(59480);async function p(e){try{let{authenticated:t,user:r}=await (0,l.b9)(e);if(!t||!r)return o.NextResponse.json({success:!1,error:"Not authenticated"},{status:401});let a=await d.rs.get("usdtDepositAddress");a||(a=await d.rs.get("USDT_DEPOSIT_ADDRESS")),a&&(a=a.replace(/['"]/g,"").trim());let s=await d.rs.get("minDepositAmount");s||(s=await d.rs.get("MIN_DEPOSIT_AMOUNT")),s=parseFloat(s||"10");let i=await d.rs.get("maxDepositAmount");i||(i=await d.rs.get("MAX_DEPOSIT_AMOUNT")),i=parseFloat(i||"10000");let n=await d.rs.get("depositEnabled");n||(n=await d.rs.get("DEPOSIT_ENABLED")),n="true"===n||!0===n;let p=await d.rs.get("minConfirmations");p||(p=await d.rs.get("MIN_CONFIRMATIONS")),p=parseInt(p||"1");let c=await d.rs.get("depositFeePercentage");c||(c=await d.rs.get("DEPOSIT_FEE_PERCENTAGE")),c=parseFloat(c||"0");let{searchParams:f}=new URL(e.url),m=parseInt(f.get("limit")||"10"),w=parseInt(f.get("offset")||"0"),h=await d.J6.findByUserId(r.id,{limit:Math.min(m,50),offset:w}),I=h.filter(e=>"COMPLETED"===e.status||"CONFIRMED"===e.status),y=h.filter(e=>["PENDING","PENDING_VERIFICATION","WAITING_FOR_CONFIRMATIONS"].includes(e.status)),g=I.reduce((e,t)=>e+t.usdtAmount,0),A=I.length,E=y.length,N=await (0,u.i4)();return o.NextResponse.json({success:!0,data:{depositInfo:{depositAddress:a||null,minDepositAmount:s,maxDepositAmount:i,depositEnabled:n,minConfirmations:p,depositFeePercentage:c,network:"TRC20",currency:"USDT",tronNetwork:N.network,tronApiUrl:N.apiUrl,usdtContract:N.usdtContract},userStats:{totalDeposited:g,depositCount:A,pendingDeposits:E},deposits:h.map(e=>({id:e.id,transactionId:e.transactionId,amount:e.usdtAmount,status:e.status,confirmations:e.confirmations,blockNumber:e.blockNumber,senderAddress:e.senderAddress,createdAt:e.createdAt,verifiedAt:e.verifiedAt,processedAt:e.processedAt,failureReason:e.failureReason})),pagination:{limit:m,offset:w,hasMore:h.length===m}}})}catch(e){return console.error("Deposit info error:",e),o.NextResponse.json({success:!1,error:"Failed to fetch deposit information"},{status:500})}}let c=new s.AppRouteRouteModule({definition:{kind:i.RouteKind.APP_ROUTE,page:"/api/wallet/deposit/info/route",pathname:"/api/wallet/deposit/info",filename:"route",bundlePath:"app/api/wallet/deposit/info/route"},resolvedPagePath:"C:\\Users\\<USER>\\Desktop\\Hash_Minings\\hashcorex\\src\\app\\api\\wallet\\deposit\\info\\route.ts",nextConfigOutput:"",userland:a}),{workAsyncStorage:f,workUnitAsyncStorage:m,serverHooks:w}=c;function h(){return(0,n.patchFetch)({workAsyncStorage:f,workUnitAsyncStorage:m})}},55511:e=>{e.exports=require("crypto")},63033:e=>{e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},79428:e=>{e.exports=require("buffer")},96330:e=>{e.exports=require("@prisma/client")}};var t=require("../../../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),a=t.X(0,[4447,580,5315,3306,9480],()=>r(54027));module.exports=a})();