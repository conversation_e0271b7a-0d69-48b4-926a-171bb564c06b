"use strict";(()=>{var e={};e.id=8489,e.ids=[8489],e.modules={3295:e=>{e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},12909:(e,r,t)=>{t.d(r,{DT:()=>w,DY:()=>m,HU:()=>c,Lx:()=>y,b9:()=>h,qc:()=>g});var a=t(85663),s=t(43205),i=t.n(s),o=t(6710);let n=process.env.JWT_SECRET||"fallback-secret-key",l=process.env.JWT_EXPIRES_IN||"30d",u=async e=>await a.Ay.hash(e,12),d=async(e,r)=>await a.Ay.compare(e,r),c=e=>i().sign(e,n,{expiresIn:l}),p=e=>{try{return i().verify(e,n)}catch(e){return null}},f=()=>{let e="ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789",r="HC";for(let t=0;t<8;t++)r+=e.charAt(Math.floor(Math.random()*e.length));return r},h=async e=>{let r=e.headers.get("authorization")?.replace("Bearer ","")||e.cookies.get("auth-token")?.value;if(!r)return{authenticated:!1,user:null};let t=p(r);if(!t)return{authenticated:!1,user:null};let a=await o.Gy.findByEmail(t.email);return a?{authenticated:!0,user:a}:{authenticated:!1,user:null}},m=async e=>{let r,a;if(await o.Gy.findByEmail(e.email))throw Error("User already exists with this email");if(e.referralCode){let t=await o.Gy.findByReferralId(e.referralCode);if(!t)throw Error("Invalid referral code");r=t.id}let s=await u(e.password),i=!1;do a=f(),i=!await o.Gy.findByReferralId(a);while(!i);let n=await o.Gy.create({email:e.email,firstName:e.firstName,lastName:e.lastName,password:s,referralId:a});if(r){let{placeUserByReferralType:a}=await t.e(2746).then(t.bind(t,2746)),s="general";"left"===e.placementSide?s="left":"right"===e.placementSide&&(s="right"),await a(r,n.id,s)}return{id:n.id,email:n.email,referralId:n.referralId,kycStatus:n.kycStatus}},y=async e=>{let r=await o.Gy.findByEmail(e.email);if(!r||!await d(e.password,r.password))throw Error("Invalid email or password");return{token:c({userId:r.id,email:r.email}),user:{id:r.id,email:r.email,referralId:r.referralId,kycStatus:r.kycStatus}}},w=e=>/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(e),g=async e=>{let r=await o.Gy.findById(e);return r?.role==="ADMIN"}},27910:e=>{e.exports=require("stream")},28354:e=>{e.exports=require("util")},29294:e=>{e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},44870:e=>{e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},55511:e=>{e.exports=require("crypto")},63033:e=>{e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},79428:e=>{e.exports=require("buffer")},89199:(e,r,t)=>{t.r(r),t.d(r,{patchFetch:()=>m,routeModule:()=>c,serverHooks:()=>h,workAsyncStorage:()=>p,workUnitAsyncStorage:()=>f});var a={};t.r(a),t.d(a,{POST:()=>d});var s=t(96559),i=t(48088),o=t(37719),n=t(32190),l=t(12909),u=t(6710);async function d(e){try{let{authenticated:r,user:t}=await (0,l.b9)(e);r&&t&&await u.AJ.create({action:"USER_LOGOUT",userId:t.id,details:{email:t.email,logoutTime:new Date().toISOString()},ipAddress:e.headers.get("x-forwarded-for")||"unknown",userAgent:e.headers.get("user-agent")||"unknown"});let a=n.NextResponse.json({success:!0,message:"Logout successful"});return a.cookies.set("auth-token","",{httpOnly:!0,secure:!0,sameSite:"strict",maxAge:0}),a}catch(e){return console.error("Logout error:",e),n.NextResponse.json({success:!1,error:"Logout failed"},{status:500})}}let c=new s.AppRouteRouteModule({definition:{kind:i.RouteKind.APP_ROUTE,page:"/api/auth/logout/route",pathname:"/api/auth/logout",filename:"route",bundlePath:"app/api/auth/logout/route"},resolvedPagePath:"C:\\Users\\<USER>\\Desktop\\Hash_Minings\\hashcorex\\src\\app\\api\\auth\\logout\\route.ts",nextConfigOutput:"",userland:a}),{workAsyncStorage:p,workUnitAsyncStorage:f,serverHooks:h}=c;function m(){return(0,o.patchFetch)({workAsyncStorage:p,workUnitAsyncStorage:f})}},96330:e=>{e.exports=require("@prisma/client")}};var r=require("../../../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),a=r.X(0,[4447,580,5315,3306],()=>t(89199));module.exports=a})();