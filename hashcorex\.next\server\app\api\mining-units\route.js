"use strict";(()=>{var e={};e.id=964,e.ids=[964],e.modules={3295:e=>{e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},3934:(e,t,r)=>{r.r(t),r.d(t,{patchFetch:()=>y,routeModule:()=>g,serverHooks:()=>w,workAsyncStorage:()=>f,workUnitAsyncStorage:()=>h});var a={};r.r(a),r.d(a,{GET:()=>p,POST:()=>m});var s=r(96559),n=r(48088),i=r(37719),o=r(32190),l=r(12909),u=r(6710),d=r(39794),c=r(2746);async function p(e){try{let{authenticated:t,user:r}=await (0,l.b9)(e);if(!t||!r)return o.NextResponse.json({success:!1,error:"Not authenticated"},{status:401});let a=(await (0,d.kp)(r.id)).map(e=>{let t=e.miningEarnings+e.referralEarnings+e.binaryEarnings,r=5*e.investmentAmount,a=t/r*100,s=Math.max(0,r-t);return{...e,totalEarningsCalculated:t,progressPercentage:Math.min(a,100),remainingCapacity:s,maxEarnings:r,willExpireSoon:a>=95}}).sort((e,t)=>new Date(e.createdAt).getTime()-new Date(t.createdAt).getTime());return o.NextResponse.json({success:!0,data:a,summary:{totalUnits:a.length,activeUnits:a.filter(e=>"ACTIVE"===e.status).length,expiredUnits:a.filter(e=>"EXPIRED"===e.status).length,totalMiningEarnings:a.reduce((e,t)=>e+t.miningEarnings,0),totalReferralEarnings:a.reduce((e,t)=>e+t.referralEarnings,0),totalBinaryEarnings:a.reduce((e,t)=>e+t.binaryEarnings,0),totalEarnings:a.reduce((e,t)=>e+(t.miningEarnings+t.referralEarnings+t.binaryEarnings),0),totalInvestment:a.reduce((e,t)=>e+t.investmentAmount,0),totalMiningPower:a.reduce((e,t)=>e+t.thsAmount,0)}})}catch(e){return console.error("Mining units fetch error:",e),o.NextResponse.json({success:!1,error:"Failed to fetch mining units"},{status:500})}}async function m(e){try{let{authenticated:t,user:a}=await (0,l.b9)(e);if(!t||!a)return o.NextResponse.json({success:!1,error:"Not authenticated"},{status:401});let{thsAmount:s,investmentAmount:n}=await e.json();if(!s||!n||s<=0||n<=0)return o.NextResponse.json({success:!1,error:"Invalid TH/s amount or investment amount"},{status:400});let i=parseFloat(await u.rs.get("MINIMUM_PURCHASE")||"50");if(n<i)return o.NextResponse.json({success:!1,error:`Minimum purchase amount is $${i}`},{status:400});let d=parseFloat(await u.rs.get("THS_PRICE")||"50"),p=s*d;if(Math.abs(n-p)>.01*p)return o.NextResponse.json({success:!1,error:"Investment amount does not match TH/s price"},{status:400});let{calculateDynamicROI:m}=await r.e(5112).then(r.bind(r,92731)),g=await m(s),f=await u.k_.getOrCreate(a.id);if(f.availableBalance<n)return o.NextResponse.json({success:!1,error:`Insufficient balance. Available: $${f.availableBalance.toFixed(2)}, Required: $${n.toFixed(2)}`},{status:400});let h=await u.tg.create({userId:a.id,thsAmount:s,investmentAmount:n,dailyROI:g});await u.k_.updateBalance(a.id,{availableBalance:f.availableBalance-n}),await u.DR.create({userId:a.id,type:"PURCHASE",amount:n,description:`Mining unit purchase - ${s} TH/s`,status:"COMPLETED"});let w=await (0,c.lv)(a.id);if(w)try{let e=await (0,c.$D)(w.id,n,a.id);console.log(`Direct referral bonus of $${e} added to sponsor ${w.id}`),await (0,c.gS)(a.id,n),console.log(`Binary points added for investment of $${n}`)}catch(e){console.error("Error processing commissions:",e)}return await u.AJ.create({action:"MINING_UNIT_PURCHASED",userId:a.id,details:{miningUnitId:h.id,thsAmount:s,investmentAmount:n,dailyROI:g,sponsorId:w?.id},ipAddress:e.headers.get("x-forwarded-for")||"unknown",userAgent:e.headers.get("user-agent")||"unknown"}),o.NextResponse.json({success:!0,message:"Mining unit purchased successfully",data:h})}catch(e){return console.error("Mining unit purchase error:",e),o.NextResponse.json({success:!1,error:"Failed to purchase mining unit"},{status:500})}}let g=new s.AppRouteRouteModule({definition:{kind:n.RouteKind.APP_ROUTE,page:"/api/mining-units/route",pathname:"/api/mining-units",filename:"route",bundlePath:"app/api/mining-units/route"},resolvedPagePath:"C:\\Users\\<USER>\\Desktop\\Hash_Minings\\hashcorex\\src\\app\\api\\mining-units\\route.ts",nextConfigOutput:"",userland:a}),{workAsyncStorage:f,workUnitAsyncStorage:h,serverHooks:w}=g;function y(){return(0,i.patchFetch)({workAsyncStorage:f,workUnitAsyncStorage:h})}},10846:e=>{e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},12909:(e,t,r)=>{r.d(t,{DT:()=>w,DY:()=>f,HU:()=>c,Lx:()=>h,b9:()=>g,qc:()=>y});var a=r(85663),s=r(43205),n=r.n(s),i=r(6710);let o=process.env.JWT_SECRET||"fallback-secret-key",l=process.env.JWT_EXPIRES_IN||"30d",u=async e=>await a.Ay.hash(e,12),d=async(e,t)=>await a.Ay.compare(e,t),c=e=>n().sign(e,o,{expiresIn:l}),p=e=>{try{return n().verify(e,o)}catch(e){return null}},m=()=>{let e="ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789",t="HC";for(let r=0;r<8;r++)t+=e.charAt(Math.floor(Math.random()*e.length));return t},g=async e=>{let t=e.headers.get("authorization")?.replace("Bearer ","")||e.cookies.get("auth-token")?.value;if(!t)return{authenticated:!1,user:null};let r=p(t);if(!r)return{authenticated:!1,user:null};let a=await i.Gy.findByEmail(r.email);return a?{authenticated:!0,user:a}:{authenticated:!1,user:null}},f=async e=>{let t,a;if(await i.Gy.findByEmail(e.email))throw Error("User already exists with this email");if(e.referralCode){let r=await i.Gy.findByReferralId(e.referralCode);if(!r)throw Error("Invalid referral code");t=r.id}let s=await u(e.password),n=!1;do a=m(),n=!await i.Gy.findByReferralId(a);while(!n);let o=await i.Gy.create({email:e.email,firstName:e.firstName,lastName:e.lastName,password:s,referralId:a});if(t){let{placeUserByReferralType:a}=await r.e(2746).then(r.bind(r,2746)),s="general";"left"===e.placementSide?s="left":"right"===e.placementSide&&(s="right"),await a(t,o.id,s)}return{id:o.id,email:o.email,referralId:o.referralId,kycStatus:o.kycStatus}},h=async e=>{let t=await i.Gy.findByEmail(e.email);if(!t||!await d(e.password,t.password))throw Error("Invalid email or password");return{token:c({userId:t.id,email:t.email}),user:{id:t.id,email:t.email,referralId:t.referralId,kycStatus:t.kycStatus}}},w=e=>/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(e),y=async e=>{let t=await i.Gy.findById(e);return t?.role==="ADMIN"}},27910:e=>{e.exports=require("stream")},28354:e=>{e.exports=require("util")},29294:e=>{e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},44870:e=>{e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},55511:e=>{e.exports=require("crypto")},63033:e=>{e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},79428:e=>{e.exports=require("buffer")},96330:e=>{e.exports=require("@prisma/client")}};var t=require("../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),a=t.X(0,[4447,580,5315,3306,2746],()=>r(3934));module.exports=a})();