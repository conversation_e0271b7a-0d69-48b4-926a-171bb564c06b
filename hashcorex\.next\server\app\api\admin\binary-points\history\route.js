"use strict";(()=>{var e={};e.id=950,e.ids=[950],e.modules={3295:e=>{e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},12909:(e,r,t)=>{t.d(r,{DT:()=>w,DY:()=>f,HU:()=>c,Lx:()=>h,b9:()=>y,qc:()=>A});var a=t(85663),i=t(43205),s=t.n(i),n=t(6710);let o=process.env.JWT_SECRET||"fallback-secret-key",d=process.env.JWT_EXPIRES_IN||"30d",l=async e=>await a.Ay.hash(e,12),u=async(e,r)=>await a.Ay.compare(e,r),c=e=>s().sign(e,o,{expiresIn:d}),p=e=>{try{return s().verify(e,o)}catch(e){return null}},m=()=>{let e="ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789",r="HC";for(let t=0;t<8;t++)r+=e.charAt(Math.floor(Math.random()*e.length));return r},y=async e=>{let r=e.headers.get("authorization")?.replace("Bearer ","")||e.cookies.get("auth-token")?.value;if(!r)return{authenticated:!1,user:null};let t=p(r);if(!t)return{authenticated:!1,user:null};let a=await n.Gy.findByEmail(t.email);return a?{authenticated:!0,user:a}:{authenticated:!1,user:null}},f=async e=>{let r,a;if(await n.Gy.findByEmail(e.email))throw Error("User already exists with this email");if(e.referralCode){let t=await n.Gy.findByReferralId(e.referralCode);if(!t)throw Error("Invalid referral code");r=t.id}let i=await l(e.password),s=!1;do a=m(),s=!await n.Gy.findByReferralId(a);while(!s);let o=await n.Gy.create({email:e.email,firstName:e.firstName,lastName:e.lastName,password:i,referralId:a});if(r){let{placeUserByReferralType:a}=await t.e(2746).then(t.bind(t,2746)),i="general";"left"===e.placementSide?i="left":"right"===e.placementSide&&(i="right"),await a(r,o.id,i)}return{id:o.id,email:o.email,referralId:o.referralId,kycStatus:o.kycStatus}},h=async e=>{let r=await n.Gy.findByEmail(e.email);if(!r||!await u(e.password,r.password))throw Error("Invalid email or password");return{token:c({userId:r.id,email:r.email}),user:{id:r.id,email:r.email,referralId:r.referralId,kycStatus:r.kycStatus}}},w=e=>/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(e),A=async e=>{let r=await n.Gy.findById(e);return r?.role==="ADMIN"}},27910:e=>{e.exports=require("stream")},28354:e=>{e.exports=require("util")},29294:e=>{e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},40908:(e,r,t)=>{t.r(r),t.d(r,{patchFetch:()=>f,routeModule:()=>c,serverHooks:()=>y,workAsyncStorage:()=>p,workUnitAsyncStorage:()=>m});var a={};t.r(a),t.d(a,{GET:()=>u});var i=t(96559),s=t(48088),n=t(37719),o=t(32190),d=t(12909),l=t(31183);async function u(e){try{let{authenticated:r,user:t}=await (0,d.b9)(e);if(!r||!t)return o.NextResponse.json({success:!1,error:"Authentication required"},{status:401});if("ADMIN"!==t.role)return o.NextResponse.json({success:!1,error:"Admin access required"},{status:403});let{searchParams:a}=new URL(e.url),i=a.get("userId"),s=parseInt(a.get("limit")||"100"),n=parseInt(a.get("offset")||"0"),u={type:"BINARY_BONUS",status:"COMPLETED"};i&&(u.userId=i);let c=await l.prisma.transaction.findMany({where:u,include:{user:{select:{id:!0,email:!0,firstName:!0,lastName:!0}}},orderBy:{createdAt:"desc"},take:s,skip:n}),p=await l.prisma.transaction.count({where:u}),m=await Promise.all(c.map(async e=>{let r=await l.prisma.systemLog.findFirst({where:{action:{in:["BINARY_MATCHING_PROCESSED","MANUAL_BINARY_MATCHING_TRIGGERED"]},createdAt:{gte:new Date(e.createdAt.getTime()-6e4),lte:new Date(e.createdAt.getTime()+6e4)}},orderBy:{createdAt:"desc"}}),t=e.description?.includes("Manual")||r?.action==="MANUAL_BINARY_MATCHING_TRIGGERED";return{id:e.id,userId:e.userId,user:e.user,matchedPoints:e.amount/10,payout:e.amount,matchDate:e.createdAt,type:t?"MANUAL":"WEEKLY",description:e.description,leftPointsBefore:0,rightPointsBefore:0,leftPointsAfter:0,rightPointsAfter:0}}));return o.NextResponse.json({success:!0,data:m,pagination:{total:p,limit:s,offset:n,hasMore:n+s<p}})}catch(e){return console.error("Binary points history fetch error:",e),o.NextResponse.json({success:!1,error:"Failed to fetch binary points history"},{status:500})}}let c=new i.AppRouteRouteModule({definition:{kind:s.RouteKind.APP_ROUTE,page:"/api/admin/binary-points/history/route",pathname:"/api/admin/binary-points/history",filename:"route",bundlePath:"app/api/admin/binary-points/history/route"},resolvedPagePath:"C:\\Users\\<USER>\\Desktop\\Hash_Minings\\hashcorex\\src\\app\\api\\admin\\binary-points\\history\\route.ts",nextConfigOutput:"",userland:a}),{workAsyncStorage:p,workUnitAsyncStorage:m,serverHooks:y}=c;function f(){return(0,n.patchFetch)({workAsyncStorage:p,workUnitAsyncStorage:m})}},44870:e=>{e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},55511:e=>{e.exports=require("crypto")},63033:e=>{e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},79428:e=>{e.exports=require("buffer")},96330:e=>{e.exports=require("@prisma/client")}};var r=require("../../../../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),a=r.X(0,[4447,580,5315,3306],()=>t(40908));module.exports=a})();